require('dotenv').config();
const express = require('express');
const http = require('http');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const connectDB = require('./config/db');
const errorHandler = require('./middleware/errorHandler');
const { setupStaticFiles } = require('./middleware/staticFilesMiddleware');
const { refreshTokenMiddleware } = require('./middleware/refreshTokenMiddleware');
const path = require('path');

// Connect to Database
console.log('Attempting to connect to database...');
connectDB();

const app = express();
app.set('trust proxy', 1); // Trust the first hop from the proxy
const cookieParser = require('cookie-parser');

// --- Middleware ---
app.use(helmet());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(cookieParser());

// Define allowed origins
const allowedOrigins = process.env.CLIENT_URL ? process.env.CLIENT_URL.split(',') : ['http://localhost:3001', 'http://localhost:5173', 'http://localhost:5174'];


// Apply CORS to all routes, including static files
app.use(cors({
  origin: allowedOrigins,
  credentials: true,
  exposedHeaders: ['x-access-token'] // expose custom headers for SPA
}));




// --- Static Files ---
// Serve static files from uploads directory using custom middleware
const uploadsDir = path.join(__dirname, 'uploads');
setupStaticFiles(app, uploadsDir);

// Make sure uploads directory exists
const fs = require('fs');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

// --- Refresh Token Endpoint ---
app.get('/api/auth/refresh', refreshTokenMiddleware, (req, res) => {
  // Also return tokens in JSON for test visibility
  res.setHeader('x-access-token', req.tokens.accessToken);
  res.json({
    success: true,
    message: 'Token refreshed successfully',
    accessToken: req.tokens.accessToken,
    refreshToken: req.tokens.refreshToken
  });
});

// --- Logout Endpoint ---
app.post('/api/auth/logout', refreshTokenMiddleware, (req, res) => {
  // Revoke tokens
  res.clearCookie('refreshToken');
  res.json({
    success: true,
    message: 'Logged out successfully'
  });
});
// --- Root Route ---
app.get('/', (req, res) => {
  res.json({ message: 'Task Manager API is running!', status: 'OK' });
});

// --- API Routes ---
const authRoutes = require('./routes/authRoutes');
const taskRoutes = require('./routes/taskRoutes');
const userRoutes = require('./routes/userRoutes');
const commentRoutes = require('./routes/commentRoutes');
const chatRoutes = require('./routes/chatRoutes');
const notificationRoutes = require('./routes/notificationRoutes');
const uploadRoutes = require('./routes/uploadRoutes');
// TODO: Restore teamRoutes if/when needed in the future
// const teamRoutes = require('./routes/teamRoutes');
const { enhancedProtect } = require('./middleware/enhancedAuthMiddleware');

// Rate limiting - apply to all auth routes to prevent brute-force attacks
const authLimiter = rateLimit({
  windowMs: process.env.RATE_LIMIT_WINDOW_MS || 15 * 60 * 1000, // 15 minutes
  max: process.env.RATE_LIMIT_MAX_REQUESTS || 20, // Limit each IP to 20 auth requests per windowMs
  standardHeaders: true,
  legacyHeaders: false,
  message: 'Too many authentication attempts from this IP, please try again after 15 minutes',
});

app.use('/api/auth', authLimiter, authRoutes);
app.use('/api/users', enhancedProtect, userRoutes);
app.use('/api/tasks', enhancedProtect, taskRoutes);
app.use('/api/chat', enhancedProtect, chatRoutes);
app.use('/api/notifications', enhancedProtect, notificationRoutes);
app.use('/api/uploads', enhancedProtect, uploadRoutes);
// Mount comment routes nested under tasks
app.use('/api/tasks/:taskId/comments', enhancedProtect, commentRoutes);


// --- Error Handling ---

app.use(errorHandler);

// --- Server Initialization ---
const PORT = process.env.PORT || 3000;
const server = http.createServer(app);

// --- Socket.io Initialization ---
const { initializeSocket } = require('./socket/socketHandler');
initializeSocket(server);

server.listen(PORT, () => {
  console.log(`Server running in ${process.env.NODE_ENV || 'development'} mode on port ${PORT}`);
  console.log(`Server is accessible at http://localhost:${PORT}`);
  console.log(`Socket.io server initialized`);
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (err, promise) => {
  console.error(`Error: ${err.message}`);
  // Close server & exit process
  server.close(() => process.exit(1));
});
