import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    react({
      // Disable React DevTools in production
      babel: {
        plugins: process.env.NODE_ENV === 'production' ? [
          ['transform-remove-console', { exclude: ['error', 'warn'] }]
        ] : []
      }
    })
  ],
  css: {
    devSourcemap: true, // Helps with debugging
  },
  server: {
    port: 5173,
    host: true,
    // Reduce HMR noise in console
    hmr: {
      overlay: false
    },
    proxy: {
      '/api': 'http://localhost:5000',
    }
  },
  build: {
    // Reduce bundle size
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          router: ['react-router-dom'],
          ui: ['react-hot-toast', 'react-icons']
        }
      }
    }
  }
})
