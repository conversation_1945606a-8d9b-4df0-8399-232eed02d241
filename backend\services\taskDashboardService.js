/**
 * Task Dashboard Service
 * Handles dashboard data aggregation and statistics
 * Separated from controller for better maintainability
 */

const Task = require('../models/Task');
const User = require('../models/User');
const mongoose = require('mongoose');
const { getTaskStatistics, getDistributionByField } = require('./taskStatisticsService');

/**
 * Get comprehensive dashboard data for admin
 * @returns {Object} Dashboard data with statistics and charts
 */
const getAdminDashboardData = async () => {
  try {
    // Get basic task statistics
    const taskStats = await getTaskStatistics();
    
    // Get task distribution by priority
    const priorityDistribution = await getDistributionByField('priority');
    
    // Get task distribution by status
    const statusDistribution = await getDistributionByField('status');
    
    // Get recent tasks (last 10)
    const recentTasks = await Task.find()
      .sort({ createdAt: -1 })
      .limit(10)
      .populate('team', 'name email profileImageUrl')
      .populate('createdBy', 'name email profileImageUrl')
      .select('title status priority deadline createdAt team createdBy attachments');
    
    // Get user count
    const totalUsers = await User.countDocuments({ role: 'member' });
    
    // Get tasks created in the last 7 days
    const lastWeek = new Date();
    lastWeek.setDate(lastWeek.getDate() - 7);
    
    const recentTasksCount = await Task.countDocuments({
      createdAt: { $gte: lastWeek }
    });
    
    // Get overdue tasks
    const overdueTasks = await Task.find({
      status: { $ne: 'completed' },
      deadline: { $lt: new Date() }
    })
    .populate('team', 'name email')
    .populate('createdBy', 'name email')
    .select('title deadline team createdBy priority')
    .sort({ deadline: 1 })
    .limit(5);
    
    // Calculate completion rate
    const completionRate = taskStats.totalTasks > 0 
      ? Math.round((taskStats.completedTasks / taskStats.totalTasks) * 100)
      : 0;
    
    // Get task creation trend (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    const taskTrend = await Task.aggregate([
      {
        $match: {
          createdAt: { $gte: thirtyDaysAgo }
        }
      },
      {
        $group: {
          _id: {
            $dateToString: {
              format: "%Y-%m-%d",
              date: "$createdAt"
            }
          },
          count: { $sum: 1 }
        }
      },
      {
        $sort: { _id: 1 }
      }
    ]);
    
    const statistics = {
      ...taskStats,
      completionRate,
      recentTasksCount
    };

    // Transform priority distribution array to object
    const priorityObj = { Low: 0, Medium: 0, High: 0 };
    priorityDistribution.forEach(item => {
      if (item._id && typeof item.count === 'number') {
        priorityObj[item._id] = item.count;
      }
    });

    // Create charts format for frontend compatibility
    const charts = {
      taskDistribution: {
        All: statistics.totalTasks,
        Pending: statistics.pendingTasks,
        InProgress: statistics.inProgressTasks,
        Completed: statistics.completedTasks
      },
      taskPriorityLevels: priorityObj
    };

    return {
      taskStatistics: statistics, // Keep for backward compatibility
      statistics: statistics, // Add for frontend compatibility
      charts: charts, // Add charts format for admin dashboard
      userStatistics: {
        totalUsers
      },
      distributions: {
        priority: priorityDistribution,
        status: statusDistribution
      },
      recentTasks,
      overdueTasks,
      taskTrend
    };
  } catch (error) {
    throw new Error(`Failed to get admin dashboard data: ${error.message}`);
  }
};

/**
 * Get dashboard data for a specific user
 * @param {String} userId - User ID
 * @returns {Object} User-specific dashboard data
 */
const getUserDashboardData = async (userId) => {
  try {
    // Get user-specific task statistics
    const taskStats = await getTaskStatistics(userId);
    
    // Get user's task distribution by priority
    const priorityDistribution = await getDistributionByField('priority', userId);
    
    // Get user's recent tasks
    let userObjectId;
    try {
      userObjectId = typeof userId === 'string' ? new mongoose.Types.ObjectId(userId) : userId;
    } catch (error) {
      userObjectId = userId;
    }

    const recentTasks = await Task.find({ team: userObjectId })
      .sort({ createdAt: -1 })
      .limit(5)
      .populate('team', 'name email profileImageUrl')
      .populate('createdBy', 'name email profileImageUrl')
      .select('title status priority deadline createdAt');
    
    // Get user's upcoming tasks (due in next 7 days)
    const nextWeek = new Date();
    nextWeek.setDate(nextWeek.getDate() + 7);
    
    const upcomingTasks = await Task.find({
      team: userObjectId,
      status: { $ne: 'completed' },
      deadline: {
        $gte: new Date(),
        $lte: nextWeek
      }
    })
    .sort({ deadline: 1 })
    .select('title deadline priority status')
    .limit(5);
    
    // Get user's overdue tasks
    const overdueTasks = await Task.find({
      team: userObjectId,
      status: { $ne: 'completed' },
      deadline: { $lt: new Date() }
    })
    .sort({ deadline: 1 })
    .select('title deadline priority status')
    .limit(5);
    
    // Calculate user's completion rate
    const completionRate = taskStats.totalTasks > 0 
      ? Math.round((taskStats.completedTasks / taskStats.totalTasks) * 100)
      : 0;
    
    // Get user's task completion trend (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    const completionTrend = await Task.aggregate([
      {
        $match: {
          team: userObjectId,
          status: 'completed',
          updatedAt: { $gte: thirtyDaysAgo }
        }
      },
      {
        $group: {
          _id: {
            $dateToString: {
              format: "%Y-%m-%d",
              date: "$updatedAt"
            }
          },
          count: { $sum: 1 }
        }
      },
      {
        $sort: { _id: 1 }
      }
    ]);
    
    const statistics = {
      ...taskStats,
      completionRate
    };

    // Transform priority distribution array to object
    const priorityObj = { Low: 0, Medium: 0, High: 0 };
    priorityDistribution.forEach(item => {
      if (item._id && typeof item.count === 'number') {
        priorityObj[item._id] = item.count;
      }
    });

    // Create charts format for frontend compatibility
    const charts = {
      taskDistribution: {
        All: statistics.totalTasks,
        Pending: statistics.pendingTasks,
        InProgress: statistics.inProgressTasks,
        Completed: statistics.completedTasks
      },
      taskPriorityLevels: priorityObj
    };

    return {
      taskStatistics: statistics, // Keep for backward compatibility
      statistics: statistics, // Add for frontend compatibility
      charts: charts, // Add charts format for dashboard compatibility
      distributions: {
        priority: priorityDistribution
      },
      recentTasks,
      upcomingTasks,
      overdueTasks,
      completionTrend
    };
  } catch (error) {
    throw new Error(`Failed to get user dashboard data: ${error.message}`);
  }
};

/**
 * Get task analytics for reporting
 * @param {Object} filters - Date range and other filters
 * @returns {Object} Analytics data
 */
const getTaskAnalytics = async (filters = {}) => {
  try {
    const {
      startDate,
      endDate,
      userId = null,
      status = null,
      priority = null
    } = filters;
    
    // Build match stage for aggregation
    const matchStage = {};
    
    if (userId) {
      matchStage.team = userId;
    }
    
    if (status) {
      matchStage.status = status;
    }
    
    if (priority) {
      matchStage.priority = priority;
    }
    
    if (startDate || endDate) {
      matchStage.createdAt = {};
      if (startDate) {
        matchStage.createdAt.$gte = new Date(startDate);
      }
      if (endDate) {
        matchStage.createdAt.$lte = new Date(endDate);
      }
    }
    
    // Get comprehensive analytics
    const analytics = await Task.aggregate([
      { $match: matchStage },
      {
        $facet: {
          // Total counts
          totalStats: [
            {
              $group: {
                _id: null,
                totalTasks: { $sum: 1 },
                avgProgress: { $avg: "$progress" }
              }
            }
          ],
          
          // Status breakdown
          statusBreakdown: [
            {
              $group: {
                _id: "$status",
                count: { $sum: 1 }
              }
            }
          ],
          
          // Priority breakdown
          priorityBreakdown: [
            {
              $group: {
                _id: "$priority",
                count: { $sum: 1 }
              }
            }
          ],
          
          // Monthly creation trend
          monthlyTrend: [
            {
              $group: {
                _id: {
                  year: { $year: "$createdAt" },
                  month: { $month: "$createdAt" }
                },
                count: { $sum: 1 }
              }
            },
            { $sort: { "_id.year": 1, "_id.month": 1 } }
          ],
          
          // Average completion time
          completionTime: [
            {
              $match: { status: "completed" }
            },
            {
              $project: {
                completionDays: {
                  $divide: [
                    { $subtract: ["$updatedAt", "$createdAt"] },
                    1000 * 60 * 60 * 24 // Convert to days
                  ]
                }
              }
            },
            {
              $group: {
                _id: null,
                avgCompletionDays: { $avg: "$completionDays" }
              }
            }
          ]
        }
      }
    ]);
    
    return {
      totalStats: analytics[0].totalStats[0] || { totalTasks: 0, avgProgress: 0 },
      statusBreakdown: analytics[0].statusBreakdown,
      priorityBreakdown: analytics[0].priorityBreakdown,
      monthlyTrend: analytics[0].monthlyTrend,
      completionTime: analytics[0].completionTime[0] || { avgCompletionDays: 0 }
    };
  } catch (error) {
    throw new Error(`Failed to get task analytics: ${error.message}`);
  }
};

module.exports = {
  getAdminDashboardData,
  getUserDashboardData,
  getTaskAnalytics
};
