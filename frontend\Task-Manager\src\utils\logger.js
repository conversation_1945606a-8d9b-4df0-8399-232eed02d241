/**
 * Logger utility to control console output
 * Set DEBUG to false to disable all debug logs
 */

const DEBUG = false; // Set to false to disable debug logs
const SHOW_ERRORS = true; // Set to false to disable error logs
const SHOW_WARNINGS = true; // Set to false to disable warning logs

const logger = {
  log: (...args) => {
    if (DEBUG) {

    }
  },
  
  error: (...args) => {
    if (SHOW_ERRORS) {
      console.error(...args);
    }
  },
  
  warn: (...args) => {
    if (SHOW_WARNINGS) {
      console.warn(...args);
    }
  },
  
  info: (...args) => {
    if (DEBUG) {
      console.info(...args);
    }
  }
};

export default logger;
