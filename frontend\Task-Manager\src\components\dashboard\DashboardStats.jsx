/**
 * Dashboard Statistics Component
 * Displays key metrics in card format
 * Extracted from Dashboard.jsx for better maintainability
 */

import React from 'react';
import { Hi<PERSON>hart<PERSON><PERSON>, Hi<PERSON>lock, HiLightningBolt, HiCheckCircle } from 'react-icons/hi';
import InfoCard from '../cards/InfoCard';
import { addThousandsSeparator } from '../../utils/helper';

const DashboardStats = ({ dashboardData, loading = false }) => {
  if (loading) {
    return (
      <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-4 gap-4 lg:gap-6 mb-6">
        {Array.from({ length: 4 }).map((_, index) => (
          <div
            key={index}
            className="bg-white p-6 rounded-lg shadow-sm border border-gray-200 animate-pulse"
          >
            <div className="flex items-center justify-between">
              <div>
                <div className="h-4 bg-gray-200 rounded w-20 mb-2" />
                <div className="h-8 bg-gray-200 rounded w-16" />
              </div>
              <div className="w-12 h-12 bg-gray-200 rounded-lg" />
            </div>
          </div>
        ))}
      </div>
    );
  }

  const stats = [
    {
      label: "Total Tasks",
      value: addThousandsSeparator(dashboardData?.charts?.taskDistribution?.All || 0),
      color: "bg-blue-600",
      icon: <HiChartPie className="h-6 w-6 text-blue-200" />,
      bgColor: "bg-blue-50",
      textColor: "text-blue-600"
    },
    {
      label: "Pending Tasks",
      value: addThousandsSeparator(dashboardData?.charts?.taskDistribution?.Pending || 0),
      color: "bg-violet-600",
      icon: <HiClock className="h-6 w-6 text-violet-200" />,
      bgColor: "bg-violet-50",
      textColor: "text-violet-600"
    },
    {
      label: "In Progress Tasks",
      value: addThousandsSeparator(
        dashboardData?.charts?.taskDistribution?.InProgress || 
        dashboardData?.charts?.taskDistribution?.["In Progress"] || 0
      ),
      color: "bg-cyan-600",
      icon: <HiLightningBolt className="h-6 w-6 text-cyan-200" />,
      bgColor: "bg-cyan-50",
      textColor: "text-cyan-600"
    },
    {
      label: "Completed Tasks",
      value: addThousandsSeparator(dashboardData?.charts?.taskDistribution?.Completed || 0),
      color: "bg-green-600",
      icon: <HiCheckCircle className="h-6 w-6 text-green-200" />,
      bgColor: "bg-green-50",
      textColor: "text-green-600"
    }
  ];

  return (
    <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-4 gap-4 lg:gap-6 mb-6">
      {stats.map((stat, index) => (
        <InfoCard
          key={index}
          label={stat.label}
          value={stat.value}
          color={stat.color}
          icon={stat.icon}
          className="hover:shadow-md transition-shadow duration-200"
        />
      ))}
    </div>
  );
};

export default DashboardStats;
