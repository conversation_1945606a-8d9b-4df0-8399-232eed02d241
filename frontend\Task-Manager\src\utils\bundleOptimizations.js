/**
 * Bundle Optimization Utilities
 * Helps reduce bundle size and improve loading performance
 */

import React, { Suspense } from 'react';

// Lazy loading utilities
export const createLazyComponent = (importFunc, fallback = null) => {
  const LazyComponent = React.lazy(importFunc);
  
  return (props) => (
    <Suspense fallback={fallback}>
      <LazyComponent {...props} />
    </Suspense>
  );
};

// Tree-shaking friendly icon imports
export const getIcon = (iconName) => {
  switch (iconName) {
    case 'calendar':
      return import('react-icons/lu').then(module => module.LuCalendar);
    case 'user':
      return import('react-icons/lu').then(module => module.LuUser);
    case 'task':
      return import('react-icons/lu').then(module => module.LuCheck);
    case 'clock':
      return import('react-icons/lu').then(module => module.LuClock);
    case 'flag':
      return import('react-icons/lu').then(module => module.LuFlag);
    case 'chart':
      return import('react-icons/lu').then(module => module.LuBarChart);
    case 'settings':
      return import('react-icons/lu').then(module => module.LuSettings);
    case 'plus':
      return import('react-icons/lu').then(module => module.LuPlus);
    case 'edit':
      return import('react-icons/lu').then(module => module.LuEdit);
    case 'delete':
      return import('react-icons/lu').then(module => module.LuTrash2);
    case 'search':
      return import('react-icons/lu').then(module => module.LuSearch);
    case 'filter':
      return import('react-icons/lu').then(module => module.LuFilter);
    case 'download':
      return import('react-icons/lu').then(module => module.LuDownload);
    case 'upload':
      return import('react-icons/lu').then(module => module.LuUpload);
    case 'refresh':
      return import('react-icons/lu').then(module => module.LuRefreshCw);
    case 'more':
      return import('react-icons/lu').then(module => module.LuMoreVertical);
    case 'home':
      return import('react-icons/lu').then(module => module.LuHouse);
    case 'mail':
      return import('react-icons/lu').then(module => module.LuMail);
    case 'bell':
      return import('react-icons/lu').then(module => module.LuBell);
    case 'paperclip':
      return import('react-icons/lu').then(module => module.LuPaperclip);
    case 'eye':
      return import('react-icons/lu').then(module => module.LuEye);
    case 'eye-off':
      return import('react-icons/lu').then(module => module.LuEyeOff);
    case 'check':
      return import('react-icons/lu').then(module => module.LuCheck);
    case 'x':
      return import('react-icons/lu').then(module => module.LuX);
    case 'arrow-left':
      return import('react-icons/lu').then(module => module.LuArrowLeft);
    case 'arrow-right':
      return import('react-icons/lu').then(module => module.LuArrowRight);
    case 'chevron-down':
      return import('react-icons/lu').then(module => module.LuChevronDown);
    case 'chevron-up':
      return import('react-icons/lu').then(module => module.LuChevronUp);
    case 'info':
      return import('react-icons/lu').then(module => module.LuInfo);
    case 'alert':
      return import('react-icons/lu').then(module => module.LuInfo);
    case 'success':
      return import('react-icons/lu').then(module => module.LuCheck);
    case 'error':
      return import('react-icons/lu').then(module => module.LuX);
    default:
      return import('react-icons/lu').then(module => module.LuInfo);
  }
};

// Dynamic icon component
export const DynamicIcon = ({ name, className = "", ...props }) => {
  const [IconComponent, setIconComponent] = React.useState(null);
  
  React.useEffect(() => {
    getIcon(name).then(setIconComponent);
  }, [name]);
  
  if (!IconComponent) {
    return <div className={`w-4 h-4 bg-gray-300 rounded ${className}`} />;
  }
  
  return <IconComponent className={className} {...props} />;
};

// Optimized date formatting (avoid importing entire date libraries)
export const formatDateOptimized = (date, format = 'short') => {
  if (!date) return '';
  
  const dateObj = new Date(date);
  
  switch (format) {
    case 'short':
      return dateObj.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric'
      });
    case 'medium':
      return dateObj.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric'
      });
    case 'long':
      return dateObj.toLocaleDateString('en-US', {
        weekday: 'long',
        month: 'long',
        day: 'numeric',
        year: 'numeric'
      });
    case 'time':
      return dateObj.toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
      });
    case 'datetime':
      return `${formatDateOptimized(date, 'medium')} at ${formatDateOptimized(date, 'time')}`;
    default:
      return dateObj.toLocaleDateString();
  }
};

// Optimized number formatting (avoid importing entire libraries)
export const formatNumberOptimized = (number, options = {}) => {
  const {
    decimals = 0,
    thousands = true,
    currency = false,
    percentage = false
  } = options;
  
  if (typeof number !== 'number' || isNaN(number)) {
    return '0';
  }
  
  let formatted = number.toFixed(decimals);
  
  if (thousands) {
    formatted = formatted.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  }
  
  if (currency) {
    formatted = `$${formatted}`;
  }
  
  if (percentage) {
    formatted = `${formatted}%`;
  }
  
  return formatted;
};

// Debounced function utility (avoid lodash)
export const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

// Throttled function utility (avoid lodash)
export const throttle = (func, limit) => {
  let inThrottle;
  return function executedFunction(...args) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
};

// Optimized class name utility (avoid clsx/classnames)
export const cn = (...classes) => {
  return classes
    .filter(Boolean)
    .join(' ')
    .replace(/\s+/g, ' ')
    .trim();
};

// Image optimization utilities
export const getOptimizedImageUrl = (url, options = {}) => {
  if (!url) return '';
  
  const {
    width = null,
    height = null,
    quality = 80,
    format = 'webp'
  } = options;
  
  // If it's already an optimized URL or external URL, return as-is
  if (url.includes('?') || url.startsWith('http') || url.startsWith('data:')) {
    return url;
  }
  
  // Add optimization parameters
  const params = new URLSearchParams();
  if (width) params.append('w', width);
  if (height) params.append('h', height);
  if (quality !== 80) params.append('q', quality);
  if (format !== 'webp') params.append('f', format);
  
  const queryString = params.toString();
  return queryString ? `${url}?${queryString}` : url;
};

// Memory usage monitoring (development only)
export const monitorMemoryUsage = () => {
  if (process.env.NODE_ENV !== 'development' || !performance.memory) {
    return null;
  }
  
  const memory = performance.memory;
  return {
    used: Math.round(memory.usedJSHeapSize / 1024 / 1024),
    total: Math.round(memory.totalJSHeapSize / 1024 / 1024),
    limit: Math.round(memory.jsHeapSizeLimit / 1024 / 1024)
  };
};

// Performance timing utilities
export const measurePerformance = (name, fn) => {
  if (process.env.NODE_ENV !== 'development') {
    return fn();
  }
  
  const start = performance.now();
  const result = fn();
  const end = performance.now();
  
  return result;
};

// Async performance measurement
export const measureAsyncPerformance = async (name, fn) => {
  if (process.env.NODE_ENV !== 'development') {
    return await fn();
  }
  
  const start = performance.now();
  const result = await fn();
  const end = performance.now();
  
  return result;
};

// Bundle size analyzer (development only)
export const analyzeBundleSize = () => {
  if (process.env.NODE_ENV !== 'development') {
    return;
  }
  
  // This would typically be used with webpack-bundle-analyzer



};
