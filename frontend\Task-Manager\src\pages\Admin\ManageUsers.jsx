import React, { useEffect } from "react";
import DashboardLayout from "../../components/layout/DashboardLayout";
import { LuFileSpreadsheet } from "react-icons/lu";
import UserCard from "../../components/cards/UserCard";
import axiosInstance from "../../utils/axiosInstance";
import { API_PATHS } from "../../utils/apiPaths";
import ConfirmationModal from "../../components/common/ConfirmationModal";
import toast from "react-hot-toast";





const ManageUsers = () => {
  const [allUsers, setAllUsers] = React.useState([]);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = React.useState(false);
  const [userToDelete, setUserToDelete] = React.useState(null);
  const [isLoading, setIsLoading] = React.useState(true);
  const [fetchError, setFetchError] = React.useState(null);

  const getAllUsers = async () => {
    setIsLoading(true);
    setFetchError(null);
    try {
      const response = await axiosInstance.get(API_PATHS.USERS.GET_ALL_USERS);
      const rawUsers = Array.isArray(response.data)
        ? response.data
        : response.data?.data || [];
      setAllUsers(rawUsers);
    } catch (error) {
      console.error("Error fetching users:", error);
      setFetchError("Failed to fetch users. Please try again later.");
      setAllUsers([]); // Clear any existing user data on error
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    getAllUsers();
  }, []);

  // Helper function to render user list or messages
  const renderUserList = () => {
    if (isLoading) {
      return <p className="text-center text-gray-500 mt-8">Loading team members...</p>;
    }

    if (fetchError) {
      return <p className="text-center text-red-500 mt-8">{fetchError}</p>;
    }

    if (allUsers.length === 0) {
      return <p className="text-center text-gray-500 mt-8">No team members found.</p>;
    }

    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-4">
        {allUsers.map((user) => (
          <UserCard
            key={user._id}
            userInfo={user}
            onDelete={() => handleDeleteUser(user)}
          />
        ))}
      </div>
    );
  };





  // download task report
  const handleDownloadReport = async () => {
    try {
      const response = await axiosInstance.get(API_PATHS.REPORTS.EXPORT_USERS, {
        responseType: "blob",
      });
      // Create a URL for the blob
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", "user_details.xlsx");
      document.body.appendChild(link);
      link.click();
      link.parentNode.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error("Error downloading expense details:", error);
      toast.error("Failed to download expense details. Please try again.");
    }
  };







  const handleDeleteUser = (user) => {
    setUserToDelete(user);
    setIsDeleteModalOpen(true);
  };

  const confirmDeleteUser = async () => {
    if (!userToDelete) return;

    try {
      await axiosInstance.delete(API_PATHS.USERS.DELETE_USER(userToDelete._id));
      // Refresh user list after deletion
      getAllUsers();
      setIsDeleteModalOpen(false);
      setUserToDelete(null);
    } catch (error) {
      console.error("Error deleting user:", error);
    }
  };

  return (
    <DashboardLayout activeMenu="Team Members">
      <div className="mt-5 mb-10">
        <div className="flex md:flex-row md:items-center justify-between">
          <h2 className="text-xl md:text-xl font-medium">Team Members</h2>

          <button
            className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md shadow-sm transition-all"
            onClick={handleDownloadReport}
          >
            <LuFileSpreadsheet className="text-lg" />
            <span>Export Report</span>
          </button>
        </div>

        {renderUserList()}
      </div>

      <ConfirmationModal
        isOpen={isDeleteModalOpen}
        onClose={() => {
          setIsDeleteModalOpen(false);
          setUserToDelete(null);
        }}
        onConfirm={confirmDeleteUser}
        title="Delete User"
        message={`Are you sure you want to delete ${
          userToDelete?.name || "this user"
        }? This action cannot be undone.`}
        confirmText="Delete"
        cancelText="Cancel"
      />
    </DashboardLayout>
  );
};

export default ManageUsers;
