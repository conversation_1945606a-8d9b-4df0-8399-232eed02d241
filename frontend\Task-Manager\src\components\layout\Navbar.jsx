import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';

/**
 * Navbar component for the landing page
 * @param {Object} props - Component props
 * @param {React.RefObject} props.navRef - Ref for the navbar element
 * @param {Array} props.navLinks - Array of navigation links
 * @param {string} props.activeSection - ID of the currently active section
 * @param {Function} props.handleNavLinkClick - Function to handle navigation link clicks
 * @returns {JSX.Element} - Rendered component
 */
const Navbar = ({ navRef, navLinks, activeSection, handleNavLinkClick }) => {
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 50) {
        setIsScrolled(true);
      } else {
        setIsScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);
  
  // Pre-compute the nav classes based on scroll state
  const navClasses = isScrolled
    ? "fixed top-0 bg-white shadow-lg py-5 px-6 sm:px-8 md:px-16 left-0 right-0 z-[100] transition-all duration-300"
    : "absolute top-0 bg-transparent py-5 px-6 sm:px-8 md:px-16 left-0 right-0 z-[100] transition-all duration-300";
    
  return (
    <nav ref={navRef} id="main-nav" className={navClasses}>
      <div className="container mx-auto flex justify-between items-center">
        <a 
          href="#hero" 
          onClick={(e) => handleNavLinkClick(e, '#hero')} 
          className="text-4xl font-black text-slate-800 tracking-tighter hover:text-blue-600 transition-colors"
        >
          xerox
        </a>
        
        <div className="hidden md:flex space-x-8 items-center nav-links">
          {navLinks.map(link => (
            <a
              key={link.label + link.href}
              href={link.href}
              onClick={(e) => handleNavLinkClick(e, link.href)}
              className={`nav-link font-semibold transition-colors text-base ${activeSection === link.sectionId ? 'text-slate-900 nav-link-active' : 'text-slate-500 hover:text-slate-700'}`}
              aria-current={activeSection === link.sectionId ? 'page' : undefined}
            >
              {link.label}
            </a>
          ))}
        </div>
        
        <div className="flex items-center space-x-4">
          <Link 
            to="/login"
            className="text-slate-600 hover:text-slate-900 font-medium transition-colors text-sm sm:text-base"
          >
            Log In
          </Link>
          <Link 
            to="/signup"
            className="px-5 py-2.5 bg-slate-900 text-white font-medium rounded-lg hover:bg-slate-800 transition-all duration-300 shadow-sm hover:shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-100 focus:ring-slate-500 text-sm sm:text-base"
          >
            Sign Up
          </Link>
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
