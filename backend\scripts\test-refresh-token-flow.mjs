// Automated script to test refresh token rotation and logout invalidation (ESM version)
import axios from 'axios';
import { <PERSON>ieJar } from 'tough-cookie';
import { wrapper } from 'axios-cookiejar-support';

const BASE_URL = 'http://localhost:3000/api/auth';
const TEST_USER = {
  email: `autotestuser${Date.now()}@example.com`,
  password: 'TestUser@123',
  name: `Auto Test User ${Date.now()}`
};

async function main() {
  const jar = new CookieJar();
  const client = wrapper(axios.create({ baseURL: BASE_URL, jar, withCredentials: true }));

  // 1. Register or login
  let res;
  try {
    res = await client.post('/register', TEST_USER);
    console.log('Registered new test user.');
  } catch (e) {
    res = await client.post('/login', { email: TEST_USER.email, password: TEST_USER.password });
    console.log('Logged in as test user.');
  }
  const accessToken1 = res.data.token;

  // 2. Refresh token (should rotate)
  res = await client.get('/refresh');
  const accessToken2 = res.headers['x-access-token'];
  if (!accessToken2 || accessToken2 === accessToken1) {
    throw new Error('Refresh token did not rotate properly.');
  }
  console.log('Refresh token rotated successfully.');

  // 3. Try using old refresh token (simulate by restoring old cookie)
  // Not directly possible with axios-cookiejar-support, so we skip direct old-token test
  // Instead, we proceed to logout and test invalidation

  // 4. Logout (should clear all tokens)
  await client.post('/logout');
  console.log('Logged out, all tokens should be invalid.');

  // 5. Attempt refresh again (should fail)
  let failed = false;
  try {
    await client.get('/refresh');
  } catch (e) {
    failed = true;
    if (e.response && e.response.status === 401) {
      console.log('Refresh after logout failed as expected.');
    } else {
      throw e;
    }
  }
  if (!failed) throw new Error('Refresh after logout did NOT fail as expected.');

  console.log('All tests passed!');
}

main().catch(e => {
  console.error('Test failed:', e.message);
  process.exit(1);
});
