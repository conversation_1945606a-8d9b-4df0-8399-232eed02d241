import React, { useState, useEffect, useCallback } from "react";
import { LuPlus, LuTrash } from "react-icons/lu";

const TodoListInput = ({ todos = [], setTodos }) => {
  const [newItem, setNewItem] = useState("");
  
  // Normalize incoming todos to ensure consistent structure
  const normalizeTodo = useCallback((todo, index) => ({
    id: todo.id || todo._id || `todo-${Date.now()}-${index}`,
    text: todo.text || todo.title || todo.name || '',
    completed: todo.completed || todo.isCompleted || todo.status === 'completed' || false
  }), []);

  // Initialize local state with normalized todos
  const [localTodos, setLocalTodos] = useState(() => {
    console.log('Initializing TodoListInput with:', todos);
    return Array.isArray(todos) ? todos.map(normalizeTodo) : [];
  });

  // Update local state when prop changes
  useEffect(() => {
    if (!Array.isArray(todos)) return;

    const normalizedTodos = todos.map(normalizeTodo);
    // Only update if the list truly changed (by shallow compare)
    const isEqual =
      normalizedTodos.length === localTodos.length &&
      normalizedTodos.every((t, i) =>
        t.id === localTodos[i].id &&
        t.text === localTodos[i].text &&
        t.completed === localTodos[i].completed
      );
    if (!isEqual) {
      setLocalTodos(normalizedTodos);
    }
  }, [todos, normalizeTodo]);

  const handleAddItem = useCallback(() => {
    if (!newItem.trim()) return;

    const newTodoItem = {
      id: `todo-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      text: newItem.trim(),
      completed: false
    };

    console.log('Adding new todo:', newTodoItem);
    
    const updatedTodos = [...localTodos, newTodoItem];
    setLocalTodos(updatedTodos);
    setTodos(updatedTodos);
    setNewItem("");
  }, [newItem, localTodos, setTodos]);

  const handleKeyPress = useCallback((e) => {
    if (e.key === "Enter") {
      e.preventDefault();
      handleAddItem();
    }
  }, [handleAddItem]);

  const handleRemoveItem = useCallback((id) => {
    console.log('Removing todo:', id);
    const updatedTodos = localTodos.filter(item => item.id !== id);
    setLocalTodos(updatedTodos);
    setTodos(updatedTodos);
  }, [localTodos, setTodos]);

  const handleToggleComplete = useCallback((id) => {
    console.log('Toggling completion for todo:', id);
    const updatedTodos = localTodos.map(item =>
      item.id === id ? { ...item, completed: !item.completed } : item
    );
    setLocalTodos(updatedTodos);
    setTodos(updatedTodos);
  }, [localTodos, setTodos]);

  return (
    <div className="mt-2">
      <div className="flex gap-2 mb-3">
        <input
          type="text"
          placeholder="Add a todo item"
          className="form-input flex-1 w-full px-4 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          value={newItem}
          onChange={(e) => setNewItem(e.target.value)}
          onKeyPress={handleKeyPress}
        />
        <button
          type="button"
          onClick={handleAddItem}
          className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <LuPlus />
        </button>
      </div>

      <div className="space-y-2 max-h-60 overflow-y-auto pr-2">
        {localTodos.map((item) => (
          <div
            key={item.id}
            className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg border border-gray-200"
          >
            <input
              type="checkbox"
              checked={item.completed}
              onChange={() => handleToggleComplete(item.id)}
              className="h-5 w-5 rounded text-blue-600 focus:ring-blue-500 border-gray-300"
            />
            <span
              className={`flex-1 text-sm ${item.completed ? "line-through text-gray-400" : "text-gray-700"}`}
            >
              {item.text}
            </span>
            <button
              type="button"
              onClick={() => handleRemoveItem(item.id)}
              className="text-gray-400 hover:text-red-500 transition-colors"
            >
              <LuTrash size={16} />
            </button>
          </div>
        ))}
      </div>
    </div>
  );
};

export default TodoListInput;
