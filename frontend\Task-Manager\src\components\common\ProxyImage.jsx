import React, { useState, useEffect, useRef, useCallback, memo } from 'react';
import { LuUser } from 'react-icons/lu';
import { BASE_URL } from '../../utils/apiPaths';

// Simple in-memory cache for loaded images with size limit
const imageCache = new Map();
const MAX_CACHE_SIZE = 100;

// Helper function to manage cache size
const addToCache = (url, status) => {
  if (imageCache.size >= MAX_CACHE_SIZE) {
    // Remove oldest entry (first one)
    const firstKey = imageCache.keys().next().value;
    imageCache.delete(firstKey);
  }
  imageCache.set(url, status);
};

/**
 * ProxyImage component with improved image loading and error handling
 * @param {Object} props
 * @param {string|Object} props.src - Image source URL or object
 * @param {string} props.alt - Alt text for accessibility
 * @param {string} props.className - Additional CSS classes
 * @param {string} props.fallbackText - Text to show when image fails to load
 * @param {string} props.containerClassName - Additional CSS classes for container
 * @param {Object} props.style - Additional styles for image
 * @param {Object} props.containerStyle - Additional styles for container
 */




const ProxyImage = ({
  src,
  alt = 'Image',
  className = '',
  fallbackText = '',
  containerClassName = '',
  style = {},
  containerStyle = {},
}) => {
  // State management
  const [hasError, setHasError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const imgRef = useRef(null);






  /**
   * Process and normalize different types of image URLs
   * @param {string} urlStr - The URL to process
   * @returns {string} - Processed URL
   */
  const processUrl = (urlStr) => {
    if (!urlStr) return '';
  
  // Handle non-string URLs (e.g., from API responses)
  if (typeof urlStr !== 'string') {
    if (urlStr?.url) return urlStr.url;
    if (urlStr?.path) return urlStr.path;
    return '';
  }
  
    // Handle JSON string with url property
    try {
      if (urlStr.startsWith('{') && urlStr.includes('"url":')) {
        const parsed = JSON.parse(urlStr);
        if (parsed.url) return parsed.url;
      }
    } catch {
      // Not a JSON string, continue with normal processing
    }
  
    // Handle special URLs
    if (urlStr.startsWith('blob:') || urlStr.startsWith('data:')) {
      return urlStr;
    }
  
    if (urlStr.startsWith('#')) {
      return '';
    }
  
    // Handle full URLs
    if (urlStr.startsWith('http://') || urlStr.startsWith('https://')) {
      return urlStr;
    }
  
    // Handle asset paths
    if (urlStr.startsWith('/src/assets/')) {
      return urlStr;
    }
  
    // Handle upload paths - ensure we handle both full and partial paths
    if (urlStr.includes('/uploads/')) {
      // If the URL already contains the full base path, don't modify it
      if (urlStr.startsWith(BASE_URL)) {
        return urlStr;
      }
      const filename = urlStr.split('/uploads/').pop();
      if (!filename) {
        return '';
      }
      return `${BASE_URL}/uploads/${filename}`;
    }
  
    // Handle other relative paths
    if (urlStr.startsWith('/')) {
      return urlStr.startsWith('/uploads/') ? `${BASE_URL}${urlStr}` : `${BASE_URL}/uploads${urlStr}`;
    }
  
    // Handle simple filenames - ensure they go to uploads directory
    return `${BASE_URL}/uploads/${urlStr.split('/').pop()}`;
  };







  /**
   * Process the source URL to ensure it's properly formatted
   */
  const processedSrc = React.useMemo(() => {
    if (!src) return '';
    
    try {
      // Handle different types of image sources
      if (typeof src === 'object') {
        if (src.isClientSide && src.url) {
          return src.url;
        } else if (src.url) {
          return processUrl(src.url);
        } else if (src.path) {
          return processUrl(src.path);
        }
        return '';
      }
      
      // Handle string sources
      if (typeof src === 'string') {
        if (src.startsWith('blob:') || src.startsWith('data:')) {
          return src;
        }
        return processUrl(src);
      }
      
      return '';
    } catch (error) {
      console.error('Error processing image source:', error);
      return '';
    }
  }, [src]);






  /**
   * Reset states when the source changes and check cache
   */
  useEffect(() => {
    if (!processedSrc) {
      setHasError(true);
      setIsLoading(false);
      console.log('ProxyImage: No valid source URL', { src, processedSrc });
      return;
    }

    console.log('ProxyImage: Processing source', { 
      original: src,
      processed: processedSrc,
      cached: imageCache.has(processedSrc)
    });

    // Check if image is already cached
    if (imageCache.has(processedSrc)) {
      const cachedStatus = imageCache.get(processedSrc);
      if (import.meta.env.DEV) {
        console.log('Image found in cache:', {
          src: processedSrc,
          status: cachedStatus
        });
      }
      setHasError(cachedStatus === 'error');
      setIsLoading(false);
      return;
    }

    setHasError(false);
    setIsLoading(true);
  }, [processedSrc]);







  /**
   * Handle successful image load
   */
  const handleLoad = useCallback(() => {
    setIsLoading(false);
    setHasError(false);
    // Cache successful load
    if (processedSrc) {
      addToCache(processedSrc, 'loaded');
      if (import.meta.env.DEV) {
        console.log('Image loaded and cached:', processedSrc);
      }
    }
  }, [processedSrc]);





  /**
   * Handle image load error
   */
  const handleError = useCallback(() => {
    setHasError(true);
    setIsLoading(false);
    // Cache error state
    if (processedSrc) {
      addToCache(processedSrc, 'error');
    }
  }, [processedSrc]);




  
  /**
   * Attach event listeners for image load and error
   */
  useEffect(() => {
    const imgElement = imgRef.current;
    if (imgElement) {
      imgElement.addEventListener('load', handleLoad);
      imgElement.addEventListener('error', handleError);
      
      // Check if the image is already loaded (cached)
      if (imgElement.complete) {
        if (imgElement.naturalWidth === 0) {
          handleError();
        } else {
          handleLoad();
        }
      }

      return () => {
        imgElement.removeEventListener('load', handleLoad);
        imgElement.removeEventListener('error', handleError);
      };
    }
  }, [handleLoad, handleError]);

  // Render fallback UI when there's an error or no source
  if (hasError || !src || !processedSrc) {
    console.log('ProxyImage: Rendering fallback', {
      hasError,
      src,
      processedSrc,
      fallbackText
    });
    return (
      <div 
        className={`flex items-center justify-center bg-gray-100 text-gray-500 ${containerClassName}`}
        style={{
          ...containerStyle,
          backgroundColor: style.backgroundColor || containerStyle.backgroundColor || '#f3f4f6'
        }}
      >
        {fallbackText ? (
          <div className="font-medium">
            {typeof fallbackText === 'string' ? fallbackText.charAt(0).toUpperCase() : 'A'}
          </div>
        ) : (
          <LuUser className="w-6 h-6" />
        )}
      </div>
    );
  }
  
  // Render loading state
  if (isLoading && !processedSrc) {
    return (
      <div 
        className={`flex items-center justify-center bg-gray-100 animate-pulse ${containerClassName}`}
        style={containerStyle}
      >
        <div className="w-6 h-6 rounded-full bg-gray-200"></div>
      </div>
    );
  }
  
  // Render the actual image
  return (
    <img
      ref={imgRef}
      src={processedSrc}
      alt={alt}
      className={className}
      style={style}
      loading="lazy"
      decoding="async"
    />
  );
};

export default memo(ProxyImage);
