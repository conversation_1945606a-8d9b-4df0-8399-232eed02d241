/**
 * Static files middleware configuration for Express app
 */
const express = require('express');
const path = require('path');

/**
 * Sets up static file serving middleware for the Express app
 * @param {object} app - Express application instance
 * @param {string} uploadsPath - Path to uploads directory
 */
const setupStaticFiles = (app, uploadsPath) => {
  // Middleware for serving static files from uploads directory with proper CORS headers
  app.use('/uploads', (req, res, next) => {
    // Set CORS headers for all requests to /uploads
    const allowedOrigins = process.env.CLIENT_URL 
      ? process.env.CLIENT_URL.split(',') 
      : ['http://localhost:3001', 'http://localhost:5173', 'http://localhost:5174'];
    const origin = req.headers.origin;
    if (origin && allowedOrigins.includes(origin)) {
      res.header('Access-Control-Allow-Origin', origin);
    } else {
      res.header('Access-Control-Allow-Origin', '*');
    }
    res.header('Vary', 'Origin');
    res.header('Cross-Origin-Resource-Policy', 'cross-origin');
    res.header('Access-Control-Allow-Credentials', 'true');
    res.header('Access-Control-Allow-Methods', 'GET, HEAD, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, Cache-Control');

    // Handle OPTIONS requests explicitly
    if (req.method === 'OPTIONS') {
      return res.sendStatus(200);
    }

    next();
  }, express.static(uploadsPath, {
    // Set cache control headers to prevent caching issues
    setHeaders: (res, filePath) => {
      try {
        // Allow browser to cache uploads for 1 year; rely on filename changes for cache busting
        res.set('Cache-Control', 'public, max-age=31536000, immutable');

        // Ensure proper MIME types for different image formats
        const lowerPath = filePath.toLowerCase();
        if (lowerPath.endsWith('.webp')) {
          res.set('Content-Type', 'image/webp');
        } else if (lowerPath.endsWith('.png')) {
          res.set('Content-Type', 'image/png');
        } else if (lowerPath.endsWith('.jpg') || lowerPath.endsWith('.jpeg')) {
          res.set('Content-Type', 'image/jpeg');
        } else if (lowerPath.endsWith('.gif')) {
          res.set('Content-Type', 'image/gif');
        }
      } catch (error) {
        console.error('Error in setHeaders:', error);
      }
    },
    // Add error handling for static file serving
    fallthrough: false
  }));

  // Add error handler for static file serving
  app.use('/uploads', (err, req, res, next) => {
    console.error('Static file error:', err);
    console.error('Requested file:', req.url);
    res.status(500).json({ error: 'Error serving static file', details: err.message });
  });

  // Add diagnostic routes for troubleshooting file access
  setupDiagnosticRoutes(app, uploadsPath);
};

/**
 * Sets up diagnostic routes for troubleshooting file access
 * @param {object} app - Express application instance
 * @param {string} uploadsPath - Path to uploads directory
 */
const setupDiagnosticRoutes = (app, uploadsPath) => {
  const fs = require('fs');
  
  // Route for checking image existence and metadata
  app.get('/check-image/:filename', (req, res) => {
    const filename = req.params.filename;
    const imagePath = path.join(uploadsPath, filename);
    
    // Check if file exists
    fs.access(imagePath, fs.constants.F_OK, (err) => {
      if (err) {
        return res.status(404).json({
          exists: false,
          requestedPath: imagePath,
          error: 'File not found',
          message: err.message
        });
      }
      
      // File exists - get detailed stats
      const stats = fs.statSync(imagePath);
      
      // Return detailed information about the file
      res.json({
        exists: true,
        requestedPath: imagePath,
        fileStats: stats,
        accessUrl: `${req.protocol}://${req.get('host')}/uploads/${filename}`,
        filename: filename
      });
    });
  });

  // Direct image serving route as a fallback
  app.get('/direct-image/:filename', (req, res) => {
    const filename = req.params.filename;
    const imagePath = path.join(uploadsPath, filename);
    
    // Set CORS headers
    res.header('Access-Control-Allow-Origin', '*');
    res.header('Cross-Origin-Resource-Policy', 'cross-origin');
    
    // Send the file directly
    res.sendFile(imagePath, (err) => {
      if (err) {
        res.status(404).send('Image not found');
      }
    });
  });
};

module.exports = { setupStaticFiles };