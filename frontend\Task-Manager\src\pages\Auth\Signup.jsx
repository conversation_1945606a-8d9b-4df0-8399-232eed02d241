import React, { useState, useEffect, useContext } from "react";
import AuthLayout from "../../components/layout/AuthLayout";
import ProfilePhotoSelector from "../../components/Inputs/ProfilePhotoSelector";
import Input from "../../components/Inputs/Input";
import { validateEmail, validatePassword } from "../../utils/helper.js";
import { Link, useNavigate } from "react-router-dom";
import axiosInstance from "../../utils/axiosInstance";
import { API_PATHS } from "../../utils/apiPaths";
import { UserContext } from "../../contexts/userContext.jsx";
import { LuRefreshCw } from "react-icons/lu";
import logger from "../../utils/logger";
// import uploadImage from "../../utils/uploadImage"; // No longer using the generic one for signup

const SignUp = () => {
  const [profilePic, setProfilePic] = useState(null);
  const [fullName, setFullName] = useState("");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [adminInviteToken, setAdminInviteToken] = useState("");
  const [loading, setLoading] = useState(false);

  const [error, setError] = useState(null);
  const navigate = useNavigate();
  const { updateUser } = useContext(UserContext);

  // Effect to clear error message after 3 seconds
  useEffect(() => {
    if (error) {
      const timer = setTimeout(() => {
        setError(null);
      }, 3000);
      
      // Clean up timer on component unmount or when error changes
      return () => clearTimeout(timer);
    }
  }, [error]);

const handleSignUp = async (e) => {
  e.preventDefault();

  let profileImageUrl = '';

  if (!fullName) {
    setError("Please enter full name.");
    return;
  }

  if (!validateEmail(email)) {
    setError("Please enter a valid email address.");
    return;
  }

  // Validate password with enhanced validation
  const passwordValidation = validatePassword(password);
  if (!passwordValidation.isValid) {
    setError(passwordValidation.message);
    return;
  }
  
  // Image validation
  if (profilePic) {
    // Check file type
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(profilePic.type)) {
      setError("Please upload a valid image file (JPEG, PNG, GIF, or WEBP).");
      return;
    }
    
    // Check file size (max 5MB)
    const maxSize = 5 * 1024 * 1024; // 5MB in bytes
    if (profilePic.size > maxSize) {
      setError("Image size should be less than 5MB.");
      return;
    }
  }

  setError("");
  setLoading(true);

  try {
    // Upload profile image if provided
    if (profilePic) {
      try {

        const formData = new FormData();
        formData.append('image', profilePic);

        const imgUploadRes = await axiosInstance.post(API_PATHS.IMAGE.UPLOAD_SIGNUP_IMAGE, formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        });


        profileImageUrl = imgUploadRes?.data?.imageUrl || ""; // Accessing data from axios response

        if (!profileImageUrl) {
          console.error("Signup.jsx - No imageUrl in upload response:", imgUploadRes.data);
          throw new Error("Image upload succeeded but no URL returned");
        }

        logger.log("Signup.jsx - After uploadImage, profileImageUrl:", profileImageUrl);
      } catch (imgError) {
        logger.error("Signup.jsx - Image upload error during signup:", imgError);
        console.error("Signup.jsx - Full image upload error:", imgError.response?.data || imgError.message);
        profileImageUrl = ""; // Ensure it's at least an empty string on error
        // Instead of stopping registration, we'll just continue without the profile image
        // and show a warning to the user
        logger.warn("Continuing registration without profile image");
        setError("Image upload failed, but continuing with registration without profile picture.");
      }
    }

    logger.log("Signup.jsx - Sending to /register, profileImageUrl:", profileImageUrl);
    const response = await axiosInstance.post(API_PATHS.AUTH.REGISTER, {
      name: fullName,
      email,
      password,
      profileImageUrl,
      adminInviteToken: adminInviteToken || undefined
    });

    const { token, role } = response.data;
    
    if (token) {
      // Store token WITHOUT Bearer prefix (axiosInstance will add it)
      const cleanToken = token.startsWith('Bearer ') ? token.substring(7) : token;
      localStorage.setItem("token", cleanToken);
      updateUser(response.data);
      
      // Redirect based on role
      if (role === "admin") {
        navigate("/admin/dashboard");
      } else {
        navigate("/user/dashboard");
      }
    }
  } catch (error) {
    if (error.response && error.response.data.message) {
      setError(error.response.data.message);
    } else {
      setError("Something went wrong. Please try again.");
    }
  } finally {
    setLoading(false);
  }
};


  return (
    <AuthLayout>
      <div className="w-full max-w-md mx-auto py-0">
        <div className="text-center mb-2">
          <h3 className="text-2xl font-semibold text-gray-900">Create an Account</h3>
          <p className="text-xs text-slate-600 mt-0.5">
            Join us today by entering your details below.
          </p>
        </div>

        <form onSubmit={handleSignUp} className="space-y-3">
          <ProfilePhotoSelector image={profilePic} setImage={setProfilePic} />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-2">



            <Input
              label="Full Name"
              type="text"
              name="fullName"
              placeholder="John Doe"
              value={fullName}
              onChange={({ target }) => setFullName(target.value)}
            />

            <Input
              label="Email Address"
              type="email"
              name="email"
              placeholder="<EMAIL>"
              value={email}
              onChange={({ target }) => setEmail(target.value)}
            />

            <Input
              label="Password"
              type="password"
              name="password"
              placeholder="Minimum 8 characters"
              value={password}
              onChange={({ target }) => setPassword(target.value)}
            />

            <Input
              label="Admin Invite Token"
              type="text"
              name="adminInviteToken"
              placeholder="Enter admin invite token"
              value={adminInviteToken}
              onChange={({ target }) => setAdminInviteToken(target.value)}
            />  
          </div>
          {/* Reserve space for error message to prevent layout shift */}
          <div className="h-4 flex items-center justify-center">
            {error && <p className="text-red-500 text-xs text-center">{error}</p>}
          </div>

          <button 
            type="submit" 
            className="auth-btn flex items-center justify-center" 
            disabled={loading}
          >
            {loading ? (
              <>
                <LuRefreshCw className="animate-spin mr-2" />
                Processing...
              </>
            ) : (
              "Register"
            )}
          </button>
        </form>
        
        <p className="text-xs text-slate-700 mt-3 text-center">
          Already an account? {" "}
          <Link
            className="font-medium text-primary hover:underline"
            to="/login"
          >
            Login
          </Link>
        </p>
      </div>
    </AuthLayout>
  );
};

export default SignUp;
