/**
 * Helper functions for task-related operations
 */
const mongoose = require('mongoose');

/**
 * Formats task count statistics into a standardized response object
 * 
 * @param {object} counts - Task count data
 * @returns {object} Formatted statistics object
 */
const formatTaskCountStatistics = (counts) => {
  return {
    totalTasks: counts.totalTasks || 0,
    pendingTasks: counts.pendingTasks || 0,
    inProgressTasks: counts.inProgressTasks || 0,
    completedTasks: counts.completedTasks || 0,
    overdueTasks: counts.overdueTasks || 0
  };
};

/**
 * Formats aggregated data into a consistent structure for frontend charts
 * 
 * @param {Array} data - Array of aggregated data objects with _id and count
 * @param {Array} categories - List of expected categories
 * @param {string} type - Type of data (status or priority)
 * @returns {object} Formatted data for frontend charts
 */
const formatDistributionData = (data, categories, type) => {
  const result = {};
  
  // Add total count
  const totalCount = data.reduce((sum, item) => sum + item.count, 0);
  if (type === 'status') {
    result.All = totalCount;
  }
  
  // Process each category
  categories.forEach(category => {
    const item = data.find(d => d._id === category);
    const count = item ? item.count : 0;
    
    // Handle status categories specifically
    if (type === 'status' && category === 'In Progress') {
      result.InProgress = count; // Convert to camelCase for frontend consistency
    } else {
      result[category] = count;
    }
  });
  
  return result;
};

/**
 * Task Helper Utilities
 * Contains functions for task-related operations
 */

/**
 * Build filter for task queries based on user role and query parameters
 * @param {Object} options - Filter options
 * @returns {Object} MongoDB filter object
 */
const buildTaskFilters = (options) => {
  const { status, userId, search, priority, dueDateRange } = options;
  const filter = {};

  // Filter by status if provided
  if (status && status !== 'all') {
    filter.status = status;
  }

  // Filter by user ID (for non-admin users)
  if (userId) {
    if (!mongoose.Types.ObjectId.isValid(userId)) {
      console.warn('Invalid userId format provided to buildTaskFilters:', userId);
      // Return a filter that guarantees no results, preventing a crash.
      filter._id = null;
    } else {
      filter.team = { $in: [userId] };
    }
  }

  // Filter by search term
  if (search) {
    filter.$or = [
      { title: { $regex: search, $options: 'i' } },
      { description: { $regex: search, $options: 'i' } }
    ];
  }

  // Filter by priority
  if (priority) {
    filter.priority = priority;
  }

  // Filter by due date range
  if (dueDateRange && dueDateRange.start && dueDateRange.end) {
    filter.dueDate = {
      $gte: new Date(dueDateRange.start),
      $lte: new Date(dueDateRange.end)
    };
  }

  return filter;
};

/**
 * Ensures users are properly populated in task objects
 * @param {Array} tasks - Array of task objects
 * @returns {Array} Tasks with properly populated users
 */
const ensureUserPopulation = async (tasks) => {
  if (!tasks || !Array.isArray(tasks)) return tasks;

  const userIdsToFetch = new Set();

  // Identify which user IDs need to be fetched
  tasks.forEach(task => {
    // Check team members
    if (task.team && Array.isArray(task.team)) {
      task.team.forEach(member => {
        if (member) {
          const id = member._id ? member._id.toString() : member.toString();
          if (mongoose.Types.ObjectId.isValid(id)) {
            userIdsToFetch.add(id);
          }
        }
      });
    }
    // Check createdBy field
    if (task.createdBy) {
      const id = task.createdBy._id ? task.createdBy._id.toString() : task.createdBy.toString();
      if (mongoose.Types.ObjectId.isValid(id)) {
        userIdsToFetch.add(id);
      }
    }
  });

  if (userIdsToFetch.size === 0) {
    // Return a deep copy to avoid downstream mutation issues
    return JSON.parse(JSON.stringify(tasks));
  }

  // Fetch all unique users in one query
  const users = await mongoose.model('User').find({
    _id: { $in: Array.from(userIdsToFetch) }
  }).select('name email profileImageUrl').lean(); // Use .lean() for plain JS objects

  // Create a user map for quick lookup
  const userMap = new Map(users.map(user => [user._id.toString(), user]));

  // Populate tasks with the fetched user data
  return tasks.map(task => {
    // Create a deep copy to ensure no side effects
    const populatedTask = JSON.parse(JSON.stringify(task));

    // Populate team members
    if (populatedTask.team && Array.isArray(populatedTask.team)) {
      populatedTask.team = populatedTask.team
        .map(member => {
          if (!member) return null;
          const id = member._id ? member._id.toString() : member.toString();
          return userMap.get(id) || member; // Fallback to original if not found
        })
        .filter(Boolean); // Remove any null entries
      // Also set assignedTo as an alias for team for frontend compatibility
      populatedTask.assignedTo = [...populatedTask.team];
    } else {
      // If no team, ensure assignedTo is an empty array
      populatedTask.assignedTo = [];
    }

    // Populate createdBy field
    if (populatedTask.createdBy) {
      const id = populatedTask.createdBy._id ? populatedTask.createdBy._id.toString() : populatedTask.createdBy.toString();
      populatedTask.createdBy = userMap.get(id) || populatedTask.createdBy; // Fallback to original
    }

    return populatedTask;
  });
};

module.exports = {
  buildTaskFilters,
  ensureUserPopulation,
  formatTaskCountStatistics,
  formatDistributionData
};
