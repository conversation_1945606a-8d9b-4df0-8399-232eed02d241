import { API_PATHS, BASE_URL } from "./apiPaths";
import axiosInstance from "./axiosInstance";

/**
 * Uploads a file to the server
 * @param {File} file - The file to upload
 * @param {string} fieldName - The field name to use in the form data (default: "file")
 * @returns {Promise<Object>} - The response data from the server
 */
const uploadFile = async (file, fieldName = "file") => {
  // Skip upload for files that don't meet backend constraints
  const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg', 'image/webp', 'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
  const maxSize = 2 * 1024 * 1024; // 2MB
  
  if (!allowedTypes.includes(file.type)) {
    console.error(`File type not allowed: ${file.type}. Only JPEG, PNG, WEBP, PDF, DOC, and DOCX files are allowed.`);
    return {
      success: false,
      error: 'File type not allowed',
      // Return a minimal object with placeholder values
      path: '',
      url: '#',
      name: file.name,
      size: file.size,
      type: file.type
    };
  }
  
  if (file.size > maxSize) {
    console.error(`File too large: ${file.name}. Maximum size is 2MB.`);
    return {
      success: false,
      error: 'File too large',
      // Return a minimal object with placeholder values
      path: '',
      url: '#',
      name: file.name,
      size: file.size,
      type: file.type
    };
  }
  
  const formData = new FormData();
  // Append file to form data
  formData.append(fieldName, file);

  try {
    // Log the file being uploaded for debugging

    
    // First try the main upload endpoint
    try {
      // Use the API_PATHS for consistency
      const uploadUrl = API_PATHS.UPLOADS.SINGLE_UPLOAD;
      

      
      const response = await axiosInstance.post(
        uploadUrl,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
          timeout: 30000, // 30 seconds timeout
        }
      );
      

      
      // Ensure we have a consistent URL format in the response
      let path = response.data.path || response.data.url || '';
      
      // Make sure the path starts with /uploads/ if it's a relative path
      if (path && !path.startsWith('http') && !path.startsWith('/uploads/')) {
        path = `/uploads/${path}`;
      }
      
      return {
        ...response.data,
        path: path,
        url: path,
        success: true
      };
    } catch (uploadError) {
      console.error('File upload failed:', uploadError);

      // Check if it's a specific error we can handle
      if (uploadError?.response?.status === 400) {
        const errorMsg = uploadError.response.data?.message || 'Invalid file';
        console.error('Upload validation error:', errorMsg);

        return {
          success: false,
          error: errorMsg,
          path: '',
          url: '#',
          name: file.name,
          size: file.size,
          type: file.type
        };
      }

      // For network errors or server errors, try client-side fallback
      console.warn('File upload failed, using client-side fallback:', uploadError.message);

      // For images and PDFs, we can try to use the browser's URL.createObjectURL
      // This creates a temporary local URL for the file that can be displayed
      try {
        // Create a client-side URL for the file
        const objectUrl = URL.createObjectURL(file);


        // For small image files, we can also create a base64 data URL as a backup
        if (file.type.startsWith('image/') && file.size < 500000) { // < 500KB
          const reader = new FileReader();
          reader.readAsDataURL(file);

          return new Promise((resolve) => {
            reader.onload = () => {
              const dataUrl = reader.result;


              resolve({
                success: true,
                path: objectUrl,
                url: dataUrl, // Use data URL which is more reliable
                name: file.name,
                size: file.size,
                type: file.type,
                isClientSide: true // Flag to indicate this is a client-side only file
              });
            };

            reader.onerror = () => {
              // If data URL creation fails, fall back to object URL
              resolve({
                success: true,
                path: objectUrl,
                url: objectUrl,
                name: file.name,
                size: file.size,
                type: file.type,
                isClientSide: true
              });
            };
          });
        }

        // For non-image files or larger images
        return {
          success: true,
          path: objectUrl,
          url: objectUrl,
          name: file.name,
          size: file.size,
          type: file.type,
          isClientSide: true
        };
      } catch (fallbackError) {
        console.error('Fallback mechanism also failed:', fallbackError);

        // Last resort fallback - return a minimal object with file info but no URL
        return {
          success: false,
          error: 'Upload failed and fallback mechanisms failed',
          path: '',
          url: '#',
          name: file.name,
          size: file.size,
          type: file.type
        };
      }
    }
  } catch (error) {
    console.error("Error in upload process:", error);
    // Return a minimal object with placeholder values to prevent task creation from failing
    return {
      success: false,
      error: error.message || 'Upload failed',
      path: '',
      url: '#',
      name: file.name,
      size: file.size,
      type: file.type
    };
  }
};

export default uploadFile;
