### Full-Stack Task Manager

*   **Developed a full-stack task management application using the MERN stack (MongoDB, Express.js, React.js, Node.js) with Docker containerization and Nginx for reverse proxying, ensuring a consistent and scalable production environment.**
*   **Implemented a secure JWT-based authentication system with role-based access control (admin/user), utilizing middleware to protect routes and bcrypt for password hashing to safeguard user credentials.**
*   **Engineered a real-time notification and chat system using Socket.IO, enabling instant communication and updates for task assignments, comments, and direct messages in one-on-one or group conversations.**
*   **Designed and built RESTful APIs for comprehensive task management, including CRUD operations, status updates, checklist management, and file attachments with Multer.**
*   **Created dynamic and interactive user and admin dashboards with Recharts.js for data visualization of task progress and user activity, and utilized Tailwind CSS and Material-UI for a responsive and modern user interface.**
*   **Integrated robust security measures, including Helmet for securing HTTP headers, `express-rate-limit` to prevent brute-force attacks, `csurf` for CSRF protection, and `express-mongo-sanitize` to prevent NoSQL injection attacks.**
