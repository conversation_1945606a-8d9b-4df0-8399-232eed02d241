const express = require('express');
const rateLimit = require('express-rate-limit');
const request = require('supertest');

describe('Rate Limiter Middleware Integration', () => {
  let app;
  beforeEach(() => {
    app = express();
    // Apply rate limiter: allow 2 requests per minute for testing
    const testLimiter = rateLimit({
      windowMs: 60 * 1000, // 1 minute
      max: 2,
      message: { message: 'Too many requests, please try again later.' },
      standardHeaders: true,
      legacyHeaders: false,
    });
    app.use('/test', testLimiter);
    app.get('/test', (req, res) => {
      res.json({ ok: true });
    });
  });

  it('should allow requests within the rate limit', async () => {
    const res1 = await request(app).get('/test');
    expect(res1.status).toBe(200);
    const res2 = await request(app).get('/test');
    expect(res2.status).toBe(200);
  });

  it('should block requests over the rate limit', async () => {
    await request(app).get('/test');
    await request(app).get('/test');
    const res3 = await request(app).get('/test');
    expect(res3.status).toBe(429);
    expect(res3.body).toHaveProperty('message');
    expect(res3.body.message.toLowerCase()).toContain('too many');
  });
});
