import React, { useState, useContext, useEffect, useRef } from "react";
import { UserContext } from "../../contexts/userContext";
import axiosInstance from "../../utils/axiosInstance";
import { API_PATHS } from "../../utils/apiPaths";
import ProfilePhotoSelector from "../Inputs/ProfilePhotoSelector";
import { LuRefreshCw, LuX, LuPlus, LuUser, Lu<PERSON>llipsis, LuLock, LuPencil } from "react-icons/lu"; 
import { BsThreeDotsVertical } from "react-icons/bs";
import { FaGoogle } from "react-icons/fa";
import uploadImage from "../../utils/uploadImage";
import "../../styles/modal-animation.css";
import toast from "react-hot-toast";

const ProfileSettingsModal = ({ isOpen, onClose }) => {
  const { user, updateUser } = useContext(UserContext);
  const [activeTab, setActiveTab] = useState("profile");
  const [profilePic, setProfilePic] = useState(null);
  const [fullName, setFullName] = useState(user?.name || "");
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState(null);
  const [isEditing, setIsEditing] = useState(false); 
  const modalRef = useRef(null);

  useEffect(() => {
    function handleClickOutside(event) {
      if (modalRef.current && !modalRef.current.contains(event.target)) {
        onClose();
      }
    }
    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isOpen, onClose]);

  useEffect(() => {
    if (isOpen) {
      setIsEditing(false); 
      if (user) {
        setFullName(user.name || "");
        setProfilePic(user.profileImageUrl || null);
      }
      setError(null);
      setSuccess(false);
      setActiveTab("profile"); 
    }
  }, [isOpen, user]);

  const handleUpdateProfile = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setSuccess(false);
    
    try {
      let finalProfileImageUrl;

      // Case 1: User has removed their profile image
      if (profilePic === null) {
        finalProfileImageUrl = ""; 
      } 
      // Case 2: User has selected a new image file
      else if (profilePic && typeof profilePic !== 'string') {
        try {
          // Upload the image to the server

          const imgUploadRes = await uploadImage(profilePic);


          finalProfileImageUrl = imgUploadRes?.imageUrl || imgUploadRes?.url || "";


          // Verify the image URL was returned properly
          if (!finalProfileImageUrl) {
            throw new Error('Failed to get image URL from server');
          }
          
          // Ensure the URL includes the full backend URL
          if (finalProfileImageUrl.includes('/uploads/') && !finalProfileImageUrl.startsWith('http')) {
            // If it's a relative path, convert to absolute URL
            const { UPLOADS_URL } = await import('../../utils/apiPaths');
            finalProfileImageUrl = `${UPLOADS_URL}${finalProfileImageUrl}`;
          }
          
          // Verify the image is actually accessible - this helps catch 404 issues early
          try {
            const checkImageExists = await fetch(finalProfileImageUrl, { 
              method: 'HEAD',
              headers: {
                'Cache-Control': 'no-cache'
              }
            });
            
            if (!checkImageExists.ok) {
              // Try to get the filename from the URL
              const filenameMatch = finalProfileImageUrl.match(/\/uploads\/([^?]+)/);
              if (filenameMatch && filenameMatch[1]) {
                const { UPLOADS_URL } = await import('../../utils/apiPaths');
                const checkUrl = `${UPLOADS_URL}/check-image/${filenameMatch[1]}`;
                
                const checkResponse = await fetch(checkUrl);
                const checkData = await checkResponse.json();
                
                if (checkData.exists) {
                  finalProfileImageUrl = checkData.accessUrl;
                }
              }
            }
          } catch (checkError) {
            // Continue anyway as the image might still be accessible
          }
        } catch (imgError) {
          console.error('Image upload error:', imgError);
          // Fall back to existing image if there is one
          if (user?.profileImageUrl) {
            finalProfileImageUrl = user.profileImageUrl;

            // Don't show error if we can fall back successfully
          } else {
            finalProfileImageUrl = "";
            setError("Failed to upload new image. Please try again.");
          }
        }
      } 
      // Case 3: Using an existing image URL string
      else if (typeof profilePic === 'string'){
        finalProfileImageUrl = profilePic;
      } 
      // Case 4: Fallback - no image
      else {
        finalProfileImageUrl = "";
      }

      // Prepare updated user data
      const updatedUserData = { 
        ...user, 
        name: fullName, 
        profileImageUrl: finalProfileImageUrl 
      };
      
      // Send the updated profile data to the backend first
      const response = await axiosInstance.put(API_PATHS.AUTH.UPDATE_PROFILE, {
        name: fullName,
        profileImageUrl: finalProfileImageUrl
      });
      
      // Get the user data from the response
      // This ensures we're using exactly what the server saved
      if (response.data) {
        // Use the server's returned user data to ensure data consistency
        const serverUserData = {
          ...user,  // Keep any fields not returned by the server
          ...response.data,  // Override with server data
        };
        
        // Update frontend state with the server's version
        updateUser(serverUserData);
      } else {
        // Fallback to our locally constructed object if server doesn't return user data
        updateUser(updatedUserData);
      }
      
      toast.success('Profile updated successfully!');
      setSuccess(true);
      setIsEditing(false); 
      setTimeout(() => {
        setSuccess(false);
      }, 3000);
    } catch (error) {
      console.error("Profile update error:", error);
      toast.error('Something went wrong. Please try again.');
      setError("Something went wrong. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
    if (user) {
      setFullName(user.name || "");
      setProfilePic(user.profileImageUrl || null);
    }
    setError(null); 
    setSuccess(false);
  };

  const [showOverlay, setShowOverlay] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [showContent, setShowContent] = useState(false);

  useEffect(() => {
    let overlayTimer, modalTimer, contentTimer;
    if (isOpen) {
      setShowOverlay(true);
      overlayTimer = setTimeout(() => {
        setShowModal(true);
        modalTimer = setTimeout(() => {
          setShowContent(true);
        }, 200); 
      }, 50);
    } else {
      setShowContent(false);
      contentTimer = setTimeout(() => {
        setShowModal(false);
        modalTimer = setTimeout(() => {
          setShowOverlay(false);
        }, 200);
      }, 100);
    }
    return () => {
      clearTimeout(overlayTimer);
      clearTimeout(modalTimer);
      clearTimeout(contentTimer);
    };
  }, [isOpen]);

  if (!isOpen && !showOverlay) return null;

  return (
    <div className={`fixed inset-0 z-50 overflow-y-auto bg-black bg-opacity-60 flex items-center justify-center p-4 modal-overlay ${showOverlay ? 'show' : ''}`}>
      <div
        ref={modalRef}
        className={`bg-white rounded-xl shadow-2xl overflow-hidden max-w-4xl w-full flex modal-container ${showModal ? 'show' : ''}`}
        style={{ height: "auto", minHeight: "600px", maxHeight: "90vh" }} 
      >
        {/* Sidebar */}
        <div className="w-64 bg-gray-50 border-r border-gray-100 p-6 flex flex-col">
          <div>
            <h2 className="text-xl font-semibold text-gray-800">Account</h2>
            <p className="text-sm text-gray-500 mt-1">
              Manage your account info
            </p>
          </div>
          <nav className="mt-8 space-y-1">
            <button
              onClick={() => setActiveTab("profile")}
              className={`w-full text-left px-4 py-3 rounded-md flex items-center text-sm font-medium transition-colors duration-150 ease-in-out ${
                activeTab === "profile"
                  ? "bg-gray-100 text-gray-900"
                  : "text-gray-600 hover:bg-gray-50 hover:text-gray-900"
              }`}
            >
              <LuUser className={`mr-3 h-5 w-5 ${activeTab === 'profile' ? 'text-primary' : 'text-gray-400'}`} />
              Profile
            </button>
            <button
              onClick={() => setActiveTab("security")}
              className={`w-full text-left px-4 py-3 rounded-md flex items-center text-sm font-medium transition-colors duration-150 ease-in-out ${
                activeTab === "security"
                  ? "bg-gray-100 text-gray-900"
                  : "text-gray-600 hover:bg-gray-50 hover:text-gray-900"
              }`}
            >
              <LuLock className={`mr-3 h-5 w-5 ${activeTab === 'security' ? 'text-primary' : 'text-gray-400'}`} />
              Security
            </button>
          </nav>
          <div className="mt-auto text-xs text-gray-400 pt-6">
            Secured by <span className="font-semibold text-gray-500">Task Manager</span>
          </div>
        </div>

        {/* Content Area */}
        <div className="flex-1 p-8 overflow-y-auto">
          <div className="flex justify-between items-center mb-8">
            <h3 className="text-2xl font-semibold text-gray-800">
              {activeTab === "profile" ? "Profile details" : "Security Settings"}
            </h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors duration-150 ease-in-out p-1 rounded-full hover:bg-gray-100"
            >
              <LuX className="h-6 w-6" />
            </button>
          </div>

          {showContent && (
            <div className={`modal-content-area ${showContent ? 'show' : ''}`}> 
              {activeTab === "profile" && (
                <form onSubmit={handleUpdateProfile} className="space-y-10">
                  {/* Profile Header with Photo */}
                  <div className="flex items-center space-x-6">
                    <div className={`relative ${!isEditing ? 'pointer-events-none' : ''}`}>
                      <ProfilePhotoSelector
                        image={profilePic}
                        setImage={setProfilePic} 
                        userName={user?.name || 'User'}
                        className="w-20 h-20" 
                      />
                    </div>
                    <div className="flex-1"> 
                      <div className="flex items-center justify-between"> 
                        <h4 className="text-lg font-semibold text-gray-800">
                          {user?.name || "User Name"}
                        </h4>
                        {!isEditing && (
                          <button
                            type="button"
                            onClick={() => setIsEditing(true)}
                            className="text-gray-500 hover:text-primary p-1 rounded-full hover:bg-gray-100 transition-colors duration-150 ease-in-out"
                            aria-label="Edit profile details"
                          >
                            <LuPencil className="h-5 w-5" />
                          </button>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Email Addresses - Cleaner Design */}
                  <div>
                    <div className="flex justify-between items-center mb-3">
                      <h5 className="text-base font-medium text-gray-700">Email addresses</h5>
                    </div>
                    <div className="p-4 border border-gray-200 rounded-lg bg-white">
                      <div className="flex justify-between items-center">
                        <div>
                          <p className="text-sm text-gray-800 font-medium">{user?.email || "<EMAIL>"}</p>
                          <span className="mt-1 inline-block text-xs bg-green-100 text-green-700 px-2 py-0.5 rounded-full font-medium">Primary</span>
                        </div>
                        <button type="button" className="text-gray-400 hover:text-gray-600 p-1 rounded-full hover:bg-gray-100">
                          <LuEllipsis className="h-5 w-5" />
                        </button>
                      </div>
                    </div>
                    <button type="button" className="mt-2 text-sm text-primary hover:text-primary-dark font-medium flex items-center">
                      <LuPlus className="mr-1 h-4 w-4" />
                      Add email address
                    </button>
                  </div>

                  {/* Full Name - Cleaner Design */}
                  <div>
                    <label htmlFor="fullName" className="block text-base font-medium text-gray-700 mb-2">Full Name</label>
                    <input
                      type="text"
                      id="fullName"
                      name="fullName"
                      value={fullName}
                      onChange={(e) => setFullName(e.target.value)}
                      disabled={!isEditing}
                      className="block w-full px-4 py-3 border border-gray-200 rounded-lg shadow-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary text-base disabled:bg-gray-100 disabled:text-gray-500 disabled:cursor-not-allowed"
                      placeholder="Your full name"
                    />
                  </div>

                  {/* Connected Accounts - Cleaner Design */}
                  <div>
                    <div className="flex justify-between items-center mb-3">
                      <h5 className="text-base font-medium text-gray-700">Connected accounts</h5>
                    </div>
                    <div className="p-4 border border-gray-200 rounded-lg bg-white">
                      <div className="flex justify-between items-center">
                        <div className="flex items-center space-x-3">
                          <FaGoogle className="h-5 w-5 text-red-500" />
                          <div>
                            <p className="text-sm text-gray-800 font-medium">Google</p>
                            <p className="text-xs text-gray-500">{user?.email || "<EMAIL>"}</p>
                          </div>
                        </div>
                        <button type="button" className="text-gray-400 hover:text-gray-600 p-1 rounded-full hover:bg-gray-100">
                          <LuEllipsis className="h-5 w-5" />
                        </button>
                      </div>
                      <div className="mt-3 text-xs text-yellow-700 bg-yellow-50 border border-yellow-100 p-2.5 rounded-md">
                        This account has been disconnected. <button type="button" className="font-medium text-primary hover:text-primary-dark underline">Reconnect</button>
                      </div>
                    </div>
                  </div>

                  {/* Action Buttons - Cleaner Design */}
                  {isEditing && (
                    <div className="pt-6 border-t border-gray-200 flex flex-col sm:flex-row justify-end space-y-2 sm:space-y-0 sm:space-x-3 mt-8">
                      <button
                        type="button"
                        onClick={handleCancelEdit} 
                        className="w-full sm:w-auto px-5 py-2.5 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-light"
                      >
                        Cancel
                      </button>
                      <button
                        type="submit"
                        disabled={loading}
                        className="w-full sm:w-auto px-5 py-2.5 text-sm font-medium text-white bg-primary border border-transparent rounded-lg shadow-sm hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-dark disabled:opacity-50 flex items-center justify-center"
                      >
                        {loading && <LuRefreshCw className="animate-spin mr-2 h-4 w-4" />} 
                        {loading ? "Saving..." : "Save changes"}
                      </button>
                    </div>
                  )}

                  {success && (
                    <div className="mt-4 p-3 bg-green-50 border border-green-200 text-green-700 rounded-lg text-sm">
                      Profile updated successfully!
                    </div>
                  )}
                  {error && (
                    <div className="mt-4 p-3 bg-red-50 border border-red-200 text-red-700 rounded-lg text-sm">
                      {error}
                    </div>
                  )}
                </form>
              )}

              {activeTab === "security" && (
                <div className="space-y-6">
                  <div>
                    <h4 className="text-lg font-medium text-gray-700 mb-1">Change Password</h4>
                    <p className="text-sm text-gray-500 mb-3">Choose a strong password that you’re not using elsewhere.</p>
                    <div className="space-y-3">
                        <div>
                            <label className="block text-sm font-medium text-gray-700">Current Password</label>
                            <input type="password" className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm" />
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700">New Password</label>
                            <input type="password" className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm" />
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700">Confirm New Password</label>
                            <input type="password" className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm" />
                        </div>
                        <button
                          type="button"
                          className="w-full sm:w-auto px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md shadow-sm hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-dark disabled:opacity-50"
                        >
                         Update Password
                        </button>
                    </div>
                  </div>
                  {/* Add more security settings as needed */}
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProfileSettingsModal;
