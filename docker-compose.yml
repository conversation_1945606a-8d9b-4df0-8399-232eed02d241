services:
  backend:
    build: ./backend
    restart: unless-stopped
    env_file:
      - ./backend/.env
    # volumes:
    #   # Mount local code for development; remove for pure production
    #   - ./backend:/home/<USER>/app
    #   # Use a named volume for node_modules to prevent it from being overwritten
    #   - node_modules_backend:/home/<USER>/app/node_modules
    # Port is not exposed to host, only to nginx proxy

  nginx:
    build:
      context: ./frontend/Task-Manager
      dockerfile: Dockerfile
    restart: unless-stopped
    ports:
      - "80:80"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - backend

volumes:
  node_modules_backend:
  frontend_dist:
