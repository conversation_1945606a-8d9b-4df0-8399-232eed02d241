import React from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  MdDoneA<PERSON>,
  MdSchedule,
  MdError
} from 'react-icons/md';

/**
 * MessageStatusIcon Component
 * 
 * Displays simple status icons for messages using Lucide React icons
 * - Sending: Clock icon (gray)
 * - Sent: Single check (gray) 
 * - Delivered: Double check (gray)
 * - Read: Double check (blue)
 * - Failed: Alert icon (red)
 */
const MessageStatusIcon = ({ message, conversation, className = "" }) => {
  // Determine message status
  const getMessageStatus = () => {
    // Check if message failed to send
    if (message.failed) {
      return 'failed';
    }

    // Check if message is still sending
    if (message.sending || message.pending) {
      return 'sending';
    }

    // Check if message has been read by others
    if (message.readBy && message.readBy.length > 1) {
      // For individual chats, check if the other participant has read it
      if (conversation?.type === 'individual') {
        const otherParticipant = conversation.participants?.find(
          p => p._id !== message.sender._id && p._id !== message.sender
        );

        if (otherParticipant) {
          const hasRead = message.readBy.some(
            readEntry => readEntry.user === otherParticipant._id ||
                        readEntry.user?._id === otherParticipant._id
          );
          return hasRead ? 'read' : 'delivered';
        }
      }

      // For group chats, if anyone else has read it
      const othersWhoRead = message.readBy.filter(
        readEntry => readEntry.user !== message.sender._id &&
                    readEntry.user !== message.sender &&
                    readEntry.user?._id !== message.sender._id &&
                    readEntry.user?._id !== message.sender
      );

      return othersWhoRead.length > 0 ? 'read' : 'delivered';
    }

    // Check if message has been delivered
    if (message.deliveredTo && message.deliveredTo.length > 0) {
      return 'delivered';
    }

    // Message has been sent but not delivered
    return message._id && !message._id.startsWith('temp-') ? 'sent' : 'sending';
  };

  const status = getMessageStatus();

  // Get status icon and color
  const getStatusDisplay = () => {
    const baseClasses = "w-4 h-4";
    
    switch (status) {
      case 'sending':
        return {
          icon: <MdSchedule className={`${baseClasses} text-gray-400`} />,
          tooltip: 'Sending...'
        };

      case 'sent':
        return {
          icon: <MdCheck className={`${baseClasses} text-gray-400`} />,
          tooltip: 'Sent'
        };

      case 'delivered':
        return {
          icon: <MdDoneAll className={`${baseClasses} text-gray-400`} />,
          tooltip: 'Delivered'
        };

      case 'read':
        return {
          icon: <MdDoneAll className={`${baseClasses} text-blue-400`} />,
          tooltip: 'Read'
        };

      case 'failed':
        return {
          icon: <MdError className={`${baseClasses} text-red-400`} />,
          tooltip: 'Failed to send'
        };

      default:
        return {
          icon: <MdCheck className={`${baseClasses} text-gray-400`} />,
          tooltip: 'Sent'
        };
    }
  };

  const statusDisplay = getStatusDisplay();

  return (
    <div 
      className={`inline-flex items-center ${className}`}
      title={statusDisplay.tooltip}
    >
      {statusDisplay.icon}
    </div>
  );
};

export default MessageStatusIcon;
