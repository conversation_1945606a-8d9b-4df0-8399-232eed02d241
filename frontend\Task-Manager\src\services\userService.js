import axiosInstance from '../utils/axiosInstance';

/**
 * Service for user related operations
 */
const userService = {
  /**
   * Get all users
   * @returns {Promise<Object>} Response with users array
   */
  getAllUsers: async () => {
    try {

      const response = await axiosInstance.get('/api/users');

      
      // The API returns an array of users directly
      let usersList = [];
      if (Array.isArray(response.data)) {
        usersList = response.data;
      } else {
        console.warn('Unexpected users API response structure:', response.data);
        usersList = [];
      }
      
      return {
        success: true,
        users: usersList
      };
    } catch (error) {
      console.error('Error fetching users:', error);
      console.error('Error details:', error.response?.data || 'No response data');
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to fetch users'
      };
    }
  },

  /**
   * Get a user by ID
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Response with user data
   */
  getUserById: async (userId) => {
    try {
      const response = await axiosInstance.get(`/api/users/${userId}`);
      return {
        success: true,
        user: response.data.user
      };
    } catch (error) {
      console.error(`Error fetching user ${userId}:`, error);
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to fetch user'
      };
    }
  },

  /**
   * Update user profile
   * @param {Object} userData - User data to update
   * @returns {Promise<Object>} Response with updated user data
   */
  updateProfile: async (userData) => {
    try {
      const response = await axiosInstance.put('/api/users/profile', userData);
      return {
        success: true,
        user: response.data.user
      };
    } catch (error) {
      console.error('Error updating profile:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to update profile'
      };
    }
  },

  /**
   * Upload profile image
   * @param {File} imageFile - Image file to upload
   * @returns {Promise<Object>} Response with profile image URL
   */
  uploadProfileImage: async (imageFile) => {
    try {
      const formData = new FormData();
      formData.append('profileImage', imageFile);

      const response = await axiosInstance.post('/api/users/profile/image', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });

      return {
        success: true,
        imageUrl: response.data.imageUrl
      };
    } catch (error) {
      console.error('Error uploading profile image:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to upload profile image'
      };
    }
  },

  /**
   * Search for users
   * @param {string} query - Search query
   * @returns {Promise<Object>} Response with matching users
   */
  searchUsers: async (query) => {
    try {
      const response = await axiosInstance.get(`/api/users/search?q=${encodeURIComponent(query)}`);
      return {
        success: true,
        users: response.data.users || []
      };
    } catch (error) {
      console.error('Error searching users:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to search users'
      };
    }
  }
};

export default userService; 