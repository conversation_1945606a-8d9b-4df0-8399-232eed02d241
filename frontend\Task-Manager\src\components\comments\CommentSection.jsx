import React, { useState, useEffect, useCallback } from 'react';
import axiosInstance from '../../utils/axiosInstance';
import { API_PATHS } from '../../utils/apiPaths';
import { useUserAuth } from '../../hooks/useUserAuth';
import toast from 'react-hot-toast';
import CommentItem from './CommentItem';
import AddCommentForm from './AddCommentForm';
import { LuLoader } from 'react-icons/lu';

const CommentSection = ({ taskId }) => {
  const [comments, setComments] = useState([]);
  const [loading, setLoading] = useState(true);
  const { user } = useUserAuth();

  const fetchComments = useCallback(async () => {
    if (!taskId) return;
    setLoading(true);
    try {
      const response = await axiosInstance.get(API_PATHS.COMMENTS.GET_ALL(taskId));
      setComments(response.data);
    } catch (error) {
      toast.error('Failed to load comments.');
      console.error('Failed to fetch comments:', error);
    } finally {
      setLoading(false);
    }
  }, [taskId]);

  useEffect(() => {
    fetchComments();
  }, [fetchComments]);

  const handleAddComment = (newComment) => {
    setComments(prevComments => [newComment, ...prevComments]);
  };

  const handlePinComment = async (commentId) => {
    try {
      await axiosInstance.put(API_PATHS.COMMENTS.PIN(taskId, commentId));
      // Re-fetch to get the new sorted order
      fetchComments();
      toast.success('Comment pin status updated.');
    } catch (error) {
      toast.error('Failed to update pin status.');
      console.error('Failed to pin comment:', error);
    }
  };

  return (
    <div className="mt-6">
      <h3 className="text-lg font-semibold text-gray-800 mb-4">Comments</h3>
      <AddCommentForm taskId={taskId} onCommentAdded={handleAddComment} />

      <div className="mt-4 space-y-4">
        {loading ? (
          <div className="flex justify-center items-center p-4">
            <LuLoader className="animate-spin text-blue-500" size={24} />
          </div>
        ) : comments.length > 0 ? (
          comments.map(comment => (
            <CommentItem 
              key={comment._id} 
              comment={comment} 
              onPinToggle={handlePinComment} 
              currentUserId={user?._id}
            />
          ))
        ) : (
          <p className="text-gray-500 text-sm">No comments yet. Be the first to comment!</p>
        )}
      </div>
    </div>
  );
};

export default CommentSection;
