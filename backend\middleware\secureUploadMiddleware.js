/**
 * Secure File Upload Middleware
 * Implements comprehensive security measures for file uploads
 */

const multer = require('multer');
const path = require('path');
const fs = require('fs');
const crypto = require('crypto');

// Try to require sharp for image processing, fallback if not available
let sharp;
try {
  sharp = require('sharp');
} catch (e) {
  console.warn('Sharp not available, image processing will be limited');
  sharp = null;
}

/**
 * Secure file storage configuration
 */
const createSecureStorage = () => {
  return multer.diskStorage({
    destination: (req, file, cb) => {
      // Create uploads directory with proper permissions
      const uploadDir = path.join(__dirname, '../uploads');
      
      if (!fs.existsSync(uploadDir)) {
        fs.mkdirSync(uploadDir, { recursive: true, mode: 0o755 });
      }
      
      cb(null, uploadDir);
    },
    filename: (req, file, cb) => {
      // Generate secure filename with timestamp and random hash
      const timestamp = Date.now();
      const randomHash = crypto.randomBytes(16).toString('hex');
      const sanitizedOriginalName = file.originalname.replace(/[^a-zA-Z0-9.-]/g, '_');
      const extension = path.extname(sanitizedOriginalName).toLowerCase();
      
      const secureFilename = `${timestamp}_${randomHash}${extension}`;
      cb(null, secureFilename);
    }
  });
};

/**
 * Enhanced file type validation
 */
const secureFileFilter = (req, file, cb) => {
  // Define allowed MIME types and extensions
  const allowedMimeTypes = {
    'image/jpeg': ['.jpg', '.jpeg'],
    'image/png': ['.png'],
    'image/webp': ['.webp'],
    'image/gif': ['.gif'],
    'application/pdf': ['.pdf'],
    'application/msword': ['.doc'],
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
    'text/plain': ['.txt'],
    'application/vnd.ms-excel': ['.xls'],
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx']
  };
  
  const fileExtension = path.extname(file.originalname).toLowerCase();
  const mimeType = file.mimetype.toLowerCase();
  
  // Check if MIME type is allowed
  if (!allowedMimeTypes[mimeType]) {
    return cb(new Error(`File type ${mimeType} is not allowed`), false);
  }
  
  // Check if extension matches MIME type
  if (!allowedMimeTypes[mimeType].includes(fileExtension)) {
    return cb(new Error(`File extension ${fileExtension} does not match MIME type ${mimeType}`), false);
  }
  
  // Additional security checks
  if (file.originalname.includes('..') || file.originalname.includes('/') || file.originalname.includes('\\')) {
    return cb(new Error('Invalid filename'), false);
  }
  
  cb(null, true);
};

/**
 * File content validation using magic numbers
 */
const validateFileContent = async (filePath, expectedMimeType) => {
  try {
    const buffer = fs.readFileSync(filePath);
    
    // Magic number signatures for common file types
    const signatures = {
      'image/jpeg': [0xFF, 0xD8, 0xFF],
      'image/png': [0x89, 0x50, 0x4E, 0x47],
      'image/gif': [0x47, 0x49, 0x46],
      'image/webp': [0x52, 0x49, 0x46, 0x46], // RIFF header
      'application/pdf': [0x25, 0x50, 0x44, 0x46],
      'application/msword': [0xD0, 0xCF, 0x11, 0xE0],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': [0x50, 0x4B, 0x03, 0x04]
    };
    
    const signature = signatures[expectedMimeType];
    if (!signature) {
      return true; // Skip validation for unknown types
    }
    
    // Check if file starts with expected signature
    for (let i = 0; i < signature.length; i++) {
      if (buffer[i] !== signature[i]) {
        return false;
      }
    }
    
    return true;
  } catch (error) {
    return false;
  }
};

/**
 * Image security processing
 */
const processSecureImage = async (filePath, mimeType) => {
  try {
    if (!mimeType.startsWith('image/')) {
      return true; // Skip processing for non-images
    }

    if (!sharp) {
      // Fallback: basic file size check without sharp
      const stats = fs.statSync(filePath);
      if (stats.size > 50 * 1024 * 1024) { // 50MB limit
        throw new Error('Image file too large');
      }
      return true;
    }

    // Use sharp to validate and process image
    const image = sharp(filePath);
    const metadata = await image.metadata();
    
    // Check for suspicious metadata or embedded scripts
    if (metadata.exif || metadata.icc || metadata.iptc) {
      // Strip metadata for security
      await image
        .jpeg({ quality: 90, mozjpeg: true })
        .png({ compressionLevel: 9 })
        .toFile(filePath + '_processed');
      
      // Replace original with processed version
      fs.renameSync(filePath + '_processed', filePath);
    }
    
    // Validate image dimensions (prevent zip bombs)
    if (metadata.width > 10000 || metadata.height > 10000) {
      throw new Error('Image dimensions too large');
    }
    
    return true;
  } catch (error) {
    throw new Error(`Image processing failed: ${error.message}`);
  }
};

/**
 * Virus scanning simulation (in production, integrate with actual antivirus)
 */
const scanForViruses = async (filePath) => {
  // This is a placeholder for virus scanning
  // In production, integrate with ClamAV or similar
  
  try {
    const stats = fs.statSync(filePath);
    
    // Basic checks
    if (stats.size === 0) {
      throw new Error('Empty file detected');
    }
    
    // Check for suspicious file patterns
    const buffer = fs.readFileSync(filePath, { encoding: 'binary' });
    
    // Look for common malware signatures (basic example)
    const suspiciousPatterns = [
      'eval(',
      'exec(',
      '<script',
      'javascript:',
      'vbscript:',
      'data:text/html'
    ];
    
    for (const pattern of suspiciousPatterns) {
      if (buffer.includes(pattern)) {
        throw new Error('Suspicious content detected');
      }
    }
    
    return true;
  } catch (error) {
    throw new Error(`Security scan failed: ${error.message}`);
  }
};

/**
 * Secure upload middleware configuration
 */
const createSecureUpload = (options = {}) => {
  const {
    maxFileSize = 5 * 1024 * 1024, // 5MB default
    maxFiles = 1,
    allowedTypes = ['image/jpeg', 'image/png', 'application/pdf']
  } = options;
  
  return multer({
    storage: createSecureStorage(),
    fileFilter: secureFileFilter,
    limits: {
      fileSize: maxFileSize,
      files: maxFiles,
      fields: 10,
      fieldNameSize: 100,
      fieldSize: 1024 * 1024 // 1MB for field values
    }
  });
};

/**
 * Post-upload security validation middleware
 */
const validateUploadedFile = async (req, res, next) => {
  try {
    if (!req.file && !req.files) {
      return next();
    }
    
    const files = req.files || [req.file];
    
    for (const file of files) {
      if (!file) continue;
      
      // Validate file content matches MIME type
      const isValidContent = await validateFileContent(file.path, file.mimetype);
      if (!isValidContent) {
        fs.unlinkSync(file.path); // Delete invalid file
        return res.status(400).json({
          success: false,
          message: 'File content does not match declared type'
        });
      }
      
      // Process images securely
      try {
        await processSecureImage(file.path, file.mimetype);
      } catch (error) {
        fs.unlinkSync(file.path); // Delete problematic file
        return res.status(400).json({
          success: false,
          message: error.message
        });
      }
      
      // Scan for viruses
      try {
        await scanForViruses(file.path);
      } catch (error) {
        fs.unlinkSync(file.path); // Delete infected file
        return res.status(400).json({
          success: false,
          message: 'File failed security scan'
        });
      }
    }
    
    next();
  } catch (error) {
    // Clean up any uploaded files on error
    if (req.file) {
      fs.unlinkSync(req.file.path);
    }
    if (req.files) {
      req.files.forEach(file => {
        if (fs.existsSync(file.path)) {
          fs.unlinkSync(file.path);
        }
      });
    }
    
    return res.status(500).json({
      success: false,
      message: 'File validation error'
    });
  }
};

/**
 * Secure file serving middleware
 */
const secureFileServing = (req, res, next) => {
  // Set security headers for file serving
  res.set({
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'Content-Security-Policy': "default-src 'none'",
    'X-Download-Options': 'noopen',
    'Cache-Control': 'no-cache, no-store, must-revalidate'
  });
  
  // Validate file path to prevent directory traversal
  const filePath = req.path;
  if (filePath.includes('..') || filePath.includes('~')) {
    return res.status(400).json({
      success: false,
      message: 'Invalid file path'
    });
  }
  
  next();
};

/**
 * File cleanup utility
 */
const cleanupOldFiles = () => {
  const uploadsDir = path.join(__dirname, '../uploads');
  const maxAge = 30 * 24 * 60 * 60 * 1000; // 30 days
  
  try {
    const files = fs.readdirSync(uploadsDir);
    
    files.forEach(file => {
      const filePath = path.join(uploadsDir, file);
      const stats = fs.statSync(filePath);
      
      if (Date.now() - stats.mtime.getTime() > maxAge) {
        fs.unlinkSync(filePath);
        console.log(`Cleaned up old file: ${file}`);
      }
    });
  } catch (error) {
    console.error('File cleanup error:', error);
  }
};

// Schedule file cleanup every 24 hours
setInterval(cleanupOldFiles, 24 * 60 * 60 * 1000);

module.exports = {
  createSecureUpload,
  validateUploadedFile,
  secureFileServing,
  cleanupOldFiles,
  validateFileContent,
  processSecureImage,
  scanForViruses
};
