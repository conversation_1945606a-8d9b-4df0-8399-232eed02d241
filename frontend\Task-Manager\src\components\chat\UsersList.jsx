import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from 'react-icons/lu';
import { useUser } from '../../contexts/userContext';
import UserAvatar from '../common/UserAvatar';
import userService from '../../services/userService';
import chatService from '../../services/chatService';
import { toast } from 'react-hot-toast';
import { getUserDisplayName } from '../../utils/userNameUtils';

const UsersList = ({ onStartConversation, onGroupCreated }) => {
  const { user } = useUser();
  const [users, setUsers] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(true);
  const [processingUser, setProcessingUser] = useState(null);
  const [isCreatingGroup, setIsCreatingGroup] = useState(false);
  const [selectedUsers, setSelectedUsers] = useState([]);
  const [groupName, setGroupName] = useState('');
  const [creatingGroup, setCreatingGroup] = useState(false);
  
  // Fetch users on component mount
  useEffect(() => {
    const fetchUsers = async () => {
      try {
        setLoading(true);
        const response = await userService.getAllUsers();
        
        if (response.success) {
          // Filter out current user from the list
          const filteredUsers = response.users
            .filter(u => u._id !== user?._id);

          setUsers(filteredUsers);
        } else {
          toast.error('Failed to load users');
        }
      } catch (error) {
        toast.error('Failed to load users');
      } finally {
        setLoading(false);
      }
    };
    
    fetchUsers();
  }, [user?._id]);
  
  // Filter users based on search term
  const filteredUsers = users.filter(user => {
    if (!user || !user.name) return false;
    return user.name.toLowerCase().includes(searchTerm.toLowerCase());
  });
  
  // Handle starting a conversation with a user
  const handleStartConversation = async (userId) => {
    if (isCreatingGroup) {
      // In group creation mode, toggle user selection
      const isAlreadySelected = selectedUsers.some(u => u._id === userId);
      
      if (isAlreadySelected) {
        setSelectedUsers(selectedUsers.filter(u => u._id !== userId));
      } else {
        const userToAdd = users.find(u => u._id === userId);
        if (userToAdd) {
          setSelectedUsers([...selectedUsers, userToAdd]);
        }
      }
    } else {
      // In individual chat mode, start a conversation
      try {
        setProcessingUser(userId);
        await onStartConversation(userId);
      } catch (error) {
        toast.error('Failed to start conversation');
      } finally {
        setProcessingUser(null);
      }
    }
  };
  
  // Handle creating a group
  const handleCreateGroup = async () => {
    if (!groupName.trim()) {
      toast.error('Please enter a group name');
      return;
    }
    
    if (selectedUsers.length < 1) {
      toast.error('Please select at least one user');
      return;
    }
    
    try {
      setCreatingGroup(true);
      const participantIds = selectedUsers.map(u => u._id);
      
      const response = await chatService.createGroupConversation(
        groupName.trim(),
        participantIds
      );
      
      if (response.success && response.conversation) {
        // Call the parent component's handler
        onGroupCreated(response.conversation);
        
        // Reset state
        setIsCreatingGroup(false);
        setSelectedUsers([]);
        setGroupName('');
      } else {
        toast.error(response.error || 'Failed to create group');
      }
    } catch (error) {
      toast.error('Failed to create group');
    } finally {
      setCreatingGroup(false);
    }
  };
  
  // Toggle group creation mode
  const toggleGroupMode = () => {
    setIsCreatingGroup(!isCreatingGroup);
    setSelectedUsers([]);
  };
  
  return (
    <div className="flex flex-col h-full bg-white">
      {/* Header */}
      <div className="flex justify-between items-center px-6 py-4 border-b">
        <h2 className="text-xl font-semibold">
          {isCreatingGroup ? 'Create Group' : 'Start Conversation'}
        </h2>
        <div className="flex space-x-2">
          <button
            onClick={toggleGroupMode}
            className={`px-3 py-1 rounded-full text-sm flex items-center ${
              isCreatingGroup
                ? 'bg-blue-100 text-blue-700'
                : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
            }`}
          >
            <LuUsers className="mr-1" />
            {isCreatingGroup ? 'Creating Group' : 'Create Group'}
          </button>
        </div>
      </div>
      
      {/* Group creation form */}
      {isCreatingGroup && (
        <div className="px-6 py-3 border-b">
          <div className="mb-3">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Group Name
            </label>
            <input
              type="text"
              value={groupName}
              onChange={(e) => setGroupName(e.target.value)}
              placeholder="Enter group name..."
              className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-400"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Selected Users ({selectedUsers.length})
            </label>
            <div className="flex flex-wrap gap-2">
              {selectedUsers.map(user => (
                <div
                  key={user._id}
                  className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-sm flex items-center"
                >
                  <span className="truncate max-w-[120px]">{user.name}</span>
                  <button
                    onClick={() => setSelectedUsers(selectedUsers.filter(u => u._id !== user._id))}
                    className="ml-1 text-blue-600 hover:text-blue-800"
                  >
                    <LuX className="w-4 h-4" />
                  </button>
                </div>
              ))}
              {selectedUsers.length === 0 && (
                <span className="text-sm text-gray-500 italic">
                  No users selected yet
                </span>
              )}
            </div>
          </div>
          
          <button
            onClick={handleCreateGroup}
            disabled={creatingGroup || !groupName.trim() || selectedUsers.length === 0}
            className={`mt-3 w-full py-2 rounded-lg flex items-center justify-center ${
              creatingGroup || !groupName.trim() || selectedUsers.length === 0
                ? 'bg-gray-200 text-gray-500 cursor-not-allowed'
                : 'bg-blue-500 text-white hover:bg-blue-600'
            }`}
          >
            {creatingGroup ? (
              <>
                <LuLoader className="w-4 h-4 mr-2 animate-spin" />
                Creating...
              </>
            ) : (
              <>
                <LuCheck className="w-4 h-4 mr-2" />
                Create Group
              </>
            )}
          </button>
        </div>
      )}
      
      {/* Search */}
      <div className="px-6 py-3 border-b">
        <div className="relative">
          <input
            type="text"
            placeholder="Search users..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full py-2 pl-10 pr-4 bg-gray-100 rounded-full focus:outline-none focus:ring-2 focus:ring-blue-400"
          />
          <LuSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
        </div>
      </div>
      
      {/* Users List */}
      <div className="flex-1 overflow-y-auto">
        {loading ? (
          <div className="flex justify-center items-center h-32">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          </div>
        ) : filteredUsers.length === 0 ? (
          <div className="text-center text-gray-500 py-8">
            {searchTerm ? (
              <p>No users match your search</p>
            ) : (
              <p>No users found</p>
            )}
          </div>
        ) : (
          <div className="divide-y">
            {filteredUsers.map((user) => {
              const isSelected = selectedUsers.some(u => u._id === user._id);
              const isProcessing = processingUser === user._id;

              // Use utility function for consistent name handling
              const displayName = getUserDisplayName(user);

              return (
                <div
                  key={user._id}
                  onClick={() => !isProcessing && handleStartConversation(user._id)}
                  className={`flex items-center p-4 hover:bg-gray-50 cursor-pointer transition-colors ${
                    isSelected ? 'bg-blue-50' : ''
                  } ${isProcessing ? 'opacity-70' : ''}`}
                >
                  <UserAvatar user={user} size={48} />

                  <div className="ml-3 flex-1">
                    <div className="flex justify-between items-center">
                      <h3 className="font-medium text-gray-900" title={displayName}>
                        {displayName}
                      </h3>
                      {isCreatingGroup ? (
                        <div className={`w-6 h-6 rounded-full flex items-center justify-center ${
                          isSelected
                            ? 'bg-blue-500 text-white'
                            : 'border-2 border-gray-300'
                        }`}>
                          {isSelected && <LuCheck className="w-4 h-4" />}
                        </div>
                      ) : isProcessing ? (
                        <div className="w-6 h-6 flex items-center justify-center">
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
                        </div>
                      ) : null}
                    </div>

                    {user.email && (
                      <p className="text-sm text-gray-500 mt-1" title={user.email}>
                        {user.email}
                      </p>
                    )}

                    {/* Show additional info if name is missing */}
                    {!user.name && user._id && (
                      <p className="text-xs text-orange-500 mt-1">
                        ID: {user._id.slice(-8)}
                      </p>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
};

export default UsersList; 