const express = require('express');
const { enhancedProtect } = require('../middleware/enhancedAuthMiddleware');
const {
  getNotifications,
  getUnreadCount,
  markAsRead,
  markAllAsRead,
  deleteNotification,
  clearAllNotifications
} = require('../controllers/notificationController');

const router = express.Router();

// All routes are protected (middleware applied at app level)

// Get all notifications for current user
router.get('/', getNotifications);

// Get unread notification count
router.get('/unread-count', getUnreadCount);

// Mark all notifications as read
router.put('/mark-all-read', markAllAsRead);

// Clear all notifications
router.delete('/clear-all', clearAllNotifications);

// Mark specific notification as read
router.put('/:id/read', markAsRead);

// Delete specific notification
router.delete('/:id', deleteNotification);

module.exports = router;
