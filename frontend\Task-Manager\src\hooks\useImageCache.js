import { useState, useEffect } from 'react';
import { UPLOADS_URL } from '../utils/apiPaths';

// Global image cache to persist across component renders and remounts
const globalImageCache = new Map();

/**
 * Custom hook for caching and managing image URLs
 * This hook provides a stable image URL that won't trigger reloads
 * when components re-render
 */
const useImageCache = (originalSrc) => {
  const [cachedSrc, setCachedSrc] = useState(() => {
    // Return from cache if available
    if (originalSrc && globalImageCache.has(originalSrc)) {
      return globalImageCache.get(originalSrc);
    }
    
    // Otherwise use the original source
    return originalSrc;
  });
  
  // Generate a data URL from the image to prevent reloading
  useEffect(() => {
    if (!originalSrc) return;
    
    // If we already have this image in cache, use it
    if (globalImageCache.has(originalSrc)) {
      setCachedSrc(globalImageCache.get(originalSrc));
      return;
    }
    
    // For data URLs and blob URLs, store directly in cache
    if (originalSrc.startsWith('data:') || originalSrc.startsWith('blob:')) {
      globalImageCache.set(originalSrc, originalSrc);
      setCachedSrc(originalSrc);
      return;
    }
    
    // Check if this is an external URL that might have CORS issues
    const isExternalUrl = originalSrc.startsWith('http') && 
                         !originalSrc.includes('localhost') && 
                         !originalSrc.includes('127.0.0.1');
                         
    // If this is a path to an uploaded file, use the correct backend URL
    let srcToUse = originalSrc;
    if (originalSrc && originalSrc.startsWith('/uploads/')) {
      srcToUse = `${UPLOADS_URL}${originalSrc}`;
    }
    
    // Use a proxy for external URLs to avoid CORS issues
    const fetchUrl = isExternalUrl 
      ? `https://images.weserv.nl/?url=${encodeURIComponent(srcToUse)}`
      : srcToUse;
    
    // For regular URLs, fetch and convert to data URL
    const img = new Image();
    img.crossOrigin = 'anonymous'; // Enable CORS for the image
    
    img.onload = () => {
      try {
        // Create a canvas to draw the image
        const canvas = document.createElement('canvas');
        canvas.width = img.width;
        canvas.height = img.height;
        
        // Draw the image to the canvas
        const ctx = canvas.getContext('2d');
        ctx.drawImage(img, 0, 0);
        
        // Convert to data URL
        const dataUrl = canvas.toDataURL('image/png');
        
        // Store in cache and update state
        globalImageCache.set(originalSrc, dataUrl);
        setCachedSrc(dataUrl);
      } catch (error) {
        // If conversion fails, use original URL
        globalImageCache.set(originalSrc, fetchUrl); // Use proxy URL if external
        setCachedSrc(fetchUrl);
      }
    };
    
    img.onerror = () => {
      // If loading fails, use a default avatar
      console.error('Error loading image:', originalSrc);
      const defaultAvatar = 'https://ui-avatars.com/api/?name=User&background=0D8ABC&color=fff';
      globalImageCache.set(originalSrc, defaultAvatar);
      setCachedSrc(defaultAvatar);
    };
    
    // Start loading the image
    img.src = fetchUrl;
  }, [originalSrc]);
  
  return cachedSrc;
};

export default useImageCache;
