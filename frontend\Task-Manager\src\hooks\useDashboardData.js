/**
 * Dashboard Data Hook
 * Custom hook for managing dashboard data fetching and state
 * Extracted from Dashboard.jsx for better separation of concerns
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import axiosInstance from '../utils/axiosInstance';
import { API_PATHS } from '../utils/apiPaths';
import { toast } from 'react-hot-toast';

const initialDashboardState = {
  statistics: {
    totalTasks: 0,
    pendingTasks: 0,
    inProgressTasks: 0,
    completedTasks: 0,
    overdueTasks: 0,
  },
  charts: {
    taskDistribution: { All: 0, Pending: 0, InProgress: 0, Completed: 0 },
    taskPriorityLevels: { Low: 0, Medium: 0, High: 0 },
  },
  recentTasks: [],
};

export const useDashboardData = (userRole = 'admin') => {
  const [dashboardData, setDashboardData] = useState(initialDashboardState);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [lastFetch, setLastFetch] = useState(null);

  const fetchDashboardData = useCallback(async (showLoadingState = true) => {
    try {
      if (showLoadingState) {
        setLoading(true);
      }
      setError(null);

      // Choose API endpoint based on user role
      const endpoint = userRole === 'admin'
        ? API_PATHS.TASKS.GET_DASHBOARD_DATA
        : API_PATHS.TASKS.GET_USER_DASHBOARD_DATA;

      const response = await axiosInstance.get(endpoint);

      if (response.data) {
        // Transform the data to match expected format
        const transformedData = transformDashboardData(response.data, userRole);
        setDashboardData(transformedData);
        setLastFetch(new Date());
      } else {
        throw new Error('Invalid response format');
      }
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      setError(error.response?.data?.message || 'Failed to load dashboard data');

      // Show error toast only if it's not a silent refresh
      if (showLoadingState) {
        toast.error('Failed to load dashboard data');
      }

      setDashboardData(initialDashboardState);
    } finally {
      setLoading(false);
    }
  }, [userRole]); // Removed dashboardData dependency to prevent infinite loops

  // Transform API response to match component expectations
  const transformDashboardData = (apiData, role) => {
    if (role === 'admin') {
      return {
        charts: {
          taskDistribution: {
            All: apiData.taskStatistics?.totalTasks || 0,
            Pending: apiData.taskStatistics?.pendingTasks || 0,
            InProgress: apiData.taskStatistics?.inProgressTasks || 0,
            Completed: apiData.taskStatistics?.completedTasks || 0,
          },
          taskPriorityLevels: transformPriorityData(apiData.distributions?.priority || []),
        },
        recentTasks: apiData.recentTasks || [],
        overdueTasks: apiData.overdueTasks || [],
        taskTrend: apiData.taskTrend || [],
        userStatistics: apiData.userStatistics || {},
      };
    } else {
      // User dashboard transformation
      return {
        statistics: {
          totalTasks: apiData.taskStatistics?.totalTasks || 0,
          pendingTasks: apiData.taskStatistics?.pendingTasks || 0,
          inProgressTasks: apiData.taskStatistics?.inProgressTasks || 0,
          completedTasks: apiData.taskStatistics?.completedTasks || 0,
          overdueTasks: apiData.taskStatistics?.overdueTasks || 0,
        },
        charts: {
          taskDistribution: {
            All: apiData.taskStatistics?.totalTasks || 0,
            Pending: apiData.taskStatistics?.pendingTasks || 0,
            InProgress: apiData.taskStatistics?.inProgressTasks || 0,
            Completed: apiData.taskStatistics?.completedTasks || 0,
          },
          taskPriorityLevels: transformPriorityData(apiData.distributions?.priority || []),
        },
        recentTasks: apiData.recentTasks || [],
        upcomingTasks: apiData.upcomingTasks || [],
        overdueTasks: apiData.overdueTasks || [],
        completionTrend: apiData.completionTrend || [],
      };
    }
  };

  // Transform priority distribution array to object
  const transformPriorityData = (priorityArray) => {
    const priorityObj = { Low: 0, Medium: 0, High: 0 };
    
    priorityArray.forEach(item => {
      if (item._id && typeof item.count === 'number') {
        priorityObj[item._id] = item.count;
      }
    });
    
    return priorityObj;
  };

  // Refresh data without showing loading state
  const refreshData = useCallback(() => {
    fetchDashboardData(false);
  }, [fetchDashboardData]);

  // Force refresh with loading state
  const forceRefresh = useCallback(() => {
    fetchDashboardData(true);
  }, [fetchDashboardData]);

  // Auto-refresh every 5 minutes
  // Add a ref to track if initial fetch is done to prevent duplicate calls
  const initialFetchDone = useRef(false);

  useEffect(() => {
    // Only fetch if not already done to prevent duplicate calls on mount
    if (!initialFetchDone.current) {
      fetchDashboardData(true);
      initialFetchDone.current = true;
    }

    const interval = setInterval(() => {
      fetchDashboardData(false); // Use direct call instead of refreshData
    }, 5 * 60 * 1000); // 5 minutes

    return () => clearInterval(interval);
  }, [userRole]); // Only depend on userRole to prevent loops

  // Calculate derived statistics - use statistics if available, fallback to charts
  const statistics = {
    totalTasks: dashboardData?.statistics?.totalTasks || dashboardData?.charts?.taskDistribution?.All || 0,
    completionRate: (dashboardData?.statistics?.totalTasks || dashboardData?.charts?.taskDistribution?.All || 0) > 0
      ? Math.round(((dashboardData?.statistics?.completedTasks || dashboardData?.charts?.taskDistribution?.Completed || 0) / (dashboardData?.statistics?.totalTasks || dashboardData?.charts?.taskDistribution?.All || 1)) * 100)
      : 0,
    pendingPercentage: (dashboardData?.statistics?.totalTasks || dashboardData?.charts?.taskDistribution?.All || 0) > 0
      ? Math.round(((dashboardData?.statistics?.pendingTasks || dashboardData?.charts?.taskDistribution?.Pending || 0) / (dashboardData?.statistics?.totalTasks || dashboardData?.charts?.taskDistribution?.All || 1)) * 100)
      : 0,
    inProgressPercentage: (dashboardData?.statistics?.totalTasks || dashboardData?.charts?.taskDistribution?.All || 0) > 0
      ? Math.round(((dashboardData?.statistics?.inProgressTasks || dashboardData?.charts?.taskDistribution?.InProgress || 0) / (dashboardData?.statistics?.totalTasks || dashboardData?.charts?.taskDistribution?.All || 1)) * 100)
      : 0,
  };

  return {
    dashboardData,
    loading,
    error,
    lastFetch,
    statistics,
    refreshData,
    forceRefresh,
    fetchDashboardData
  };
};

export default useDashboardData;
