import React, { useEffect, useRef } from "react";
import { LuX } from "react-icons/lu";

const Modal = ({ 
  isOpen, 
  onClose, 
  title, 
  children, 
  size = "md",
  showCloseButton = true,
  preventOutsideClick = false
}) => {
  const modalRef = useRef(null);

  // Handle outside clicks to close modal
  useEffect(() => {
    function handleClickOutside(event) {
      if (modalRef.current && 
          !modalRef.current.contains(event.target) && 
          !preventOutsideClick) {
        onClose();
      }
    }
    
    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
      // Prevent scrolling on body when modal is open
      document.body.style.overflow = "hidden";
    }
    
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
      // Restore scrolling when modal closes
      document.body.style.overflow = "auto";
    };
  }, [isOpen, onClose, preventOutsideClick]);

  if (!isOpen) return null;

  const sizeClasses = {
    sm: "max-w-sm",
    md: "max-w-md",
    lg: "max-w-lg",
    xl: "max-w-xl",
    "2xl": "max-w-2xl",
    "3xl": "max-w-3xl",
    "4xl": "max-w-4xl",
    "5xl": "max-w-5xl",
    "full": "max-w-full"
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div 
        ref={modalRef}
        className={`bg-white rounded-lg p-6 w-full ${sizeClasses[size]} max-h-[90vh] overflow-y-auto animate-modal-fade-in`}
      >
        {(title || showCloseButton) && (
          <div className="flex justify-between items-center mb-4">
            {title && <h3 className="text-lg font-medium text-gray-700">{title}</h3>}
            {showCloseButton && (
              <button 
                onClick={onClose}
                className="text-gray-500 hover:text-gray-700 focus:outline-none"
                aria-label="Close"
              >
                <LuX className="h-5 w-5" />
              </button>
            )}
          </div>
        )}
        
        {children}
      </div>
    </div>
  );
};

export default Modal;
