import React, { useState, useEffect } from 'react';

/**
 * Hero section component for the landing page
 * @returns {JSX.Element} - Rendered component
 */
const Hero = () => {
  const [startCtaPulse, setStartCtaPulse] = useState(false);
  const [startFloatingAnimation, setStartFloatingAnimation] = useState(false);
  
  // Define animation classes for floating avatars
  const floatingAnimations = [
    'animate-float-1', // Gentle up-down
    'animate-float-2', // Slight left-right
    'animate-float-3', // Circular motion
    'animate-float-4', // Diagonal drift
    'animate-float-5', // Bouncy movement
  ];

  const floatingAvatarsData = [
    { id: 'fa1', url: 'https://i.pravatar.cc/24?u=float1', size: 'w-6 h-6 md:w-7 md:h-7', positionClasses: 'top-[2%] left-[85%] transform -translate-x-1/2 -translate-y-1/2', animation: 'animate-float-1' },
    { id: 'fa2', url: 'https://i.pravatar.cc/32?u=float2', size: 'w-8 h-8 md:w-9 md:h-9', positionClasses: 'top-[4%] right-[65%] sm:right-[30%] transform translate-x-1/2 -translate-y-1/2', animation: 'animate-float-2' },
    { id: 'fa3', url: 'https://i.pravatar.cc/32?u=float3', size: 'w-8 h-8 md:w-9 md:h-9', positionClasses: 'top-[50%] left-[20%] sm:left-[15%] transform -translate-x-1/2 -translate-y-1/2', animation: 'animate-float-3' },
    { id: 'fa4', url: 'https://i.pravatar.cc/32?u=float4', size: 'w-8 h-8 md:w-9 md:h-9', positionClasses: 'top-[8%] left-[20%]', animation: 'animate-float-4' },
    { id: 'fa5', url: 'https://i.pravatar.cc/40?u=float5', size: 'w-10 h-10 md:w-11 md:h-11', positionClasses: 'bottom-[-2%] right-[35%] sm:right-[10%] transform translate-x-1/2 translate-y-1/2', animation: 'animate-float-5' },
    // New avatars
    { id: 'fa6', url: 'https://i.pravatar.cc/28?u=float6', size: 'w-7 h-7 md:w-8 md:h-8', positionClasses: 'top-[15%] right-[35%] transform translate-x-1/2 -translate-y-1/2', animation: 'animate-float-3' },
    { id: 'fa7', url: 'https://i.pravatar.cc/36?u=float7', size: 'w-9 h-9 md:w-10 md:h-10', positionClasses: 'top-[70%] right-[40%] transform translate-x-1/2 -translate-y-1/2', animation: 'animate-float-1' },
    { id: 'fa8', url: 'https://i.pravatar.cc/30?u=float8', size: 'w-7 h-7 md:w-8 md:h-8', positionClasses: 'bottom-[20%] left-[25%] transform -translate-x-1/2 translate-y-1/2', animation: 'animate-float-4' },
  ];

  useEffect(() => {
    const pulseTimer = setTimeout(() => {
      setStartCtaPulse(true);
    }, 1500); // Start pulse after 1.5 seconds
    
    // Start floating animation after all main content has appeared (around 1000ms)
    const floatingTimer = setTimeout(() => {
      setStartFloatingAnimation(true);
    }, 1000); // Show avatars earlier, after main content begins to appear
    
    return () => {
      clearTimeout(pulseTimer);
      clearTimeout(floatingTimer);
    };
  }, []);
  const teamMembers = [
    { name: 'Kathy M.', role: 'UX Designer', avatarUrl: 'https://i.pravatar.cc/40?u=kathy', added: true },
    { name: 'Thomas J.', role: 'Frontend Dev', avatarUrl: 'https://i.pravatar.cc/40?u=thomas', added: true },
    { name: 'Alex D.', role: 'Product Manager', avatarUrl: 'https://i.pravatar.cc/40?u=alex', added: false },
    { name: 'Sarah B.', role: 'Backend Lead', avatarUrl: 'https://i.pravatar.cc/40?u=sarah', added: false },
  ];

  const ctaButtonClasses = `px-8 py-3.5 bg-slate-900 text-white font-semibold rounded-lg hover:bg-slate-800 transition-all duration-300 shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-100 focus:ring-slate-500 text-base sm:text-lg transform hover:scale-105 active:scale-100 ${startCtaPulse ? 'animate-pulse-cta' : ''}`;

  const comments = [
    { name: 'Olivia R.', avatarUrl: 'https://i.pravatar.cc/40?u=olivia', text: 'This is looking fantastic! Love the new direction.' },
    { name: 'Ken F.', avatarUrl: 'https://i.pravatar.cc/40?u=ken', text: 'Good progress. Let’s sync on the CTA button text.' },
  ];

  const currentUserAvatarUrl = 'https://i.pravatar.cc/40?u=currentUser';



  return (
    <header 
      id="hero" 
      className="relative bg-gradient-to-br from-green-100 via-cyan-100 to-sky-200 pt-32 pb-24 md:pt-40 md:pb-32 px-6 sm:px-8 md:px-16 overflow-hidden min-h-screen flex flex-col justify-center"
    >
      <div className="container mx-auto flex flex-col md:flex-row items-center gap-8 md:gap-12 z-10">
        {/* Hero Content (Left Side) */}
        <div className="md:w-3/5 lg:w-1/2 text-center md:text-left">
          <h1 
            className="text-5xl sm:text-6xl lg:text-7xl font-bold text-slate-900 mb-6 leading-tight tracking-tight"
            data-aos="fade-right" 
            data-aos-duration="600"
          >
            Effortless task management peak execution
          </h1>
          <p 
            className="text-slate-600 mb-10 text-lg sm:text-xl leading-relaxed max-w-xl mx-auto md:mx-0"
            data-aos="fade-up" 
            data-aos-duration="600" 
            data-aos-delay="300"
          >
            The most powerful finance software that connects with your financial accounts. Track spending and categorize expenses so you can see where your money is going.
          </p>
          <button 
            className={ctaButtonClasses}
            data-aos="fade-up" 
            data-aos-duration="600" 
            data-aos-delay="500"
          >
            Make An Appointment
          </button>
        </div>
        
        {/* Hero Visuals (Right Side - More Realistic Placeholders) */}
        <div 
          className="hidden md:flex md:w-2/5 lg:w-1/2 mt-12 md:mt-0 justify-center items-center relative h-[480px] sm:h-[520px] md:h-[580px] lg:h-[620px] will-change-transform transform-gpu isolate [transform-style:preserve-3d]"
          data-aos="fade-left" data-aos-duration="600" data-aos-delay="700"
        >
          {/* Card 1: Design Team / Invite (Top-Left, Larger) */}
          <div data-aos="zoom-in-up" data-aos-duration="500" data-aos-delay="500" className="hidden md:block">
            <div 
              className="absolute w-[75%] sm:w-[65%] md:w-[320px] lg:w-[370px] bg-white rounded-2xl shadow-xl p-5 transform -rotate-[6deg] translate-x-[-30%] translate-y-[-25%] z-10 border border-slate-100 flex flex-col text-sm isolate"
              style={{ aspectRatio: '0.9' }}
            >
            <h3 className="text-slate-800 font-semibold text-base mb-3 shrink-0">Design Team</h3>
            <div className="space-y-2 overflow-y-auto flex-grow mb-3 pr-1">
              {teamMembers.map((member, index) => (
                <div key={index} className="flex items-center space-x-2.5 p-1.5 rounded-md hover:bg-slate-50">
                  <img src={member.avatarUrl} alt={member.name} className="w-7 h-7 rounded-full flex-shrink-0 object-cover" />
                  <div className="flex-1 min-w-0">
                    <p className="text-slate-700 font-medium truncate">{member.name}</p>
                    <p className="text-slate-500 text-xs truncate">{member.role}</p>
                  </div>
                  {member.added && <span className="text-xs bg-sky-100 text-sky-700 px-1.5 py-0.5 rounded-full font-medium">Added</span>}
                </div>
              ))}
            </div>
            <div className="shrink-0 space-y-2.5 pt-2 border-t border-slate-100">
              <div className="flex items-center space-x-2">
                <input type="email" placeholder="Invite with email..." className="flex-grow p-2 border border-slate-200 rounded-md text-xs focus:ring-1 focus:ring-blue-500 focus:border-blue-500" />
                <button className="px-3 py-2 bg-slate-700 text-white text-xs font-medium rounded-md hover:bg-slate-600 transition-colors">Send Invite</button>
              </div>
              <div className="flex items-center space-x-2">
                <p className="text-slate-500 text-xs truncate flex-grow">share.example.com/dt123</p>
                <button className="px-3 py-1.5 border border-slate-300 text-slate-600 text-xs font-medium rounded-md hover:bg-slate-100 transition-colors">Copy Link</button>
              </div>
            </div>
          </div>
          </div> {/* Closing wrapper for Card 1 */}

          {/* Card 2: Comments (Bottom-Right, Smaller) */}
          <div data-aos="zoom-in-down" data-aos-delay="600" data-aos-duration="500" className="hidden md:block">
            <div 
              className="absolute w-[65%] sm:w-[55%] md:w-[300px] lg:w-[350px] bg-white rounded-2xl shadow-2xl p-5 transform rotate-[4deg] translate-x-[5%] translate-y-[22%] z-20 border border-slate-100 flex flex-col text-sm isolate"
              style={{ aspectRatio: '1.1' }}
            >
            <h3 className="text-slate-800 font-semibold text-base mb-3 shrink-0">Comments</h3>
            <div className="space-y-3 overflow-y-auto flex-grow mb-3 pr-1">
              {comments.map((comment, index) => (
                <div key={index} className="flex items-start space-x-2">
                  <img src={comment.avatarUrl} alt={comment.name} className="w-6 h-6 rounded-full flex-shrink-0 mt-0.5 object-cover" />
                  <div className="flex-1">
                    <p className="text-slate-700 font-medium">{comment.name}</p>
                    <p className="text-slate-600 text-xs leading-relaxed">{comment.text}</p>
                  </div>
                </div>
              ))}
            </div>
            <div className="shrink-0 pt-3 border-t border-slate-100 mt-auto">
              <div className="flex items-center space-x-2 mb-2">
                  <img src={currentUserAvatarUrl} alt="Current User" className="w-6 h-6 rounded-full flex-shrink-0 object-cover" /> {/* Current user avatar placeholder */}
                  <input type="text" placeholder="Write a comment..." className="flex-grow p-2 border border-slate-200 rounded-md text-xs focus:ring-1 focus:ring-blue-500 focus:border-blue-500" />
              </div>
              <div className="flex items-center justify-between">
                <div className="flex space-x-1.5">
                  <button className="w-6 h-6 bg-gray-100 rounded hover:bg-gray-200 flex flex-col items-center justify-center space-y-0.5 p-0.5">
                    <div className="w-2.5 h-[1.5px] bg-gray-500 rounded-full"></div> <div className="w-3.5 h-[1.5px] bg-gray-500 rounded-full"></div> <div className="w-3.5 h-[1.5px] bg-gray-500 rounded-full"></div>
                  </button>
                  <button className="w-6 h-6 bg-gray-100 rounded hover:bg-gray-200 flex items-center justify-center">
                    <div className="w-2 h-2 bg-gray-500 rounded-full"></div>
                  </button>
                  <button className="w-6 h-6 bg-gray-100 rounded hover:bg-gray-200 flex items-center justify-center relative">
                    <div className="w-3 h-[1.5px] bg-gray-500 rounded-full"></div> <div className="w-[1.5px] h-3 bg-gray-500 rounded-full absolute"></div>
                  </button>
                </div>
                <button className="px-4 py-1.5 bg-blue-500 text-white text-xs font-medium rounded-md hover:bg-blue-600 transition-colors">Send</button>
              </div>
            </div>
          </div>
          </div> {/* Closing wrapper for Card 2 */}

          {/* Floating Avatars */}
          {floatingAvatarsData.map(avatar => (
            <img
              key={avatar.id}
              src={avatar.url}
              alt="Team avatar"
              className={`absolute rounded-full shadow-lg object-cover ${avatar.size} ${avatar.positionClasses} ${startFloatingAnimation ? `${avatar.animation} opacity-100` : 'opacity-0'} transition-all duration-700 ease-in-out`}
            />
          ))}
        </div>
      </div>
    </header>
  );
};

export default Hero;
