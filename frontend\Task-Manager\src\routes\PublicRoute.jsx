import React, { useContext } from 'react';
import { Navigate, Outlet, useLocation } from 'react-router-dom';
import { UserContext } from '../contexts/userContext';

const PublicRoute = ({ component: Component }) => {
  const { user, loading } = useContext(UserContext);
  const location = useLocation();

  if (loading) {
    // Show loading indicator while checking auth status
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin rounded-full h-16 w-16 border-t-4 border-blue-500"></div>
      </div>
    );
  }

  // Only redirect authenticated users away from login/signup pages
  // Landing page (/) should be accessible to everyone
  if (user && (location.pathname === '/login' || location.pathname === '/signup')) {
    // User is authenticated and trying to access login/signup, redirect to dashboard
    const redirectTo = user.role === 'admin' ? '/admin/dashboard' : '/user/dashboard';
    return <Navigate to={redirectTo} replace />;
  }

  // Render the component (landing page, login, or signup)
  return Component ? <Component /> : <Outlet />;
};

export default PublicRoute;
