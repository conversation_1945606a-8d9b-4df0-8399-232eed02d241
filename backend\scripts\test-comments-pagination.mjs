// Automated script to test comments pagination (ESM)
import axios from 'axios';
import { CookieJar } from 'tough-cookie';
import { wrapper } from 'axios-cookiejar-support';
import { BASE_URL, ADMIN_INVITE_TOKEN } from './test-helpers.mjs';
const ADMIN_USER = {
  email: `commentadmin${Date.now()}@example.com`,
  password: 'TestAdminUser123',
  name: `Comment Admin User ${Date.now()}`
};
const NUM_TEST_COMMENTS = 7;

async function main() {
  const jar = new CookieJar();
  const client = wrapper(axios.create({ baseURL: BASE_URL, jar, withCredentials: true }));
  let accessToken = null;
  let testTaskId = null;

  try {
    // 1. Register and login as an admin user
    let res = await client.get('/auth/csrf-token');
    let csrfToken = res.data.csrfToken;
    await client.post('/auth/register', { ...ADMIN_USER, adminInviteToken: ADMIN_INVITE_TOKEN }, { headers: { 'x-csrf-token': csrfToken } });
    console.log('Registered new admin test user.');

    res = await client.get('/auth/csrf-token');
    csrfToken = res.data.csrfToken;
    const loginRes = await client.post('/auth/login', { email: ADMIN_USER.email, password: ADMIN_USER.password }, { headers: { 'x-csrf-token': csrfToken } });
    console.log('Logged in as admin user.');

    const cookiesAfterLogin = await jar.getCookies(BASE_URL);
    const accessTokenCookie = cookiesAfterLogin.find(c => c.key === 'accessToken');
    if (!accessTokenCookie) throw new Error('accessToken cookie missing after login');
    accessToken = accessTokenCookie.value;

    // 2. Create a task to add comments to
    res = await client.get('/auth/csrf-token');
    csrfToken = res.data.csrfToken;
    const taskRes = await client.post('/tasks', 
      { title: 'Task for Comment Pagination Test', description: '...' }, 
      { headers: { 'Authorization': `Bearer ${accessToken}`, 'x-csrf-token': csrfToken } }
    );
    testTaskId = taskRes.data.task._id;
    console.log(`Created test task with ID: ${testTaskId}`);

    // 3. Create comments for the task
    for (let i = 1; i <= NUM_TEST_COMMENTS; i++) {
      res = await client.get('/auth/csrf-token');
      csrfToken = res.data.csrfToken;
      await client.post(`/tasks/${testTaskId}/comments`, 
        { content: `Test Comment ${i}` }, 
        { headers: { 'Authorization': `Bearer ${accessToken}`, 'x-csrf-token': csrfToken } }
      );
    }
    console.log(`Created ${NUM_TEST_COMMENTS} test comments.`);

    // 4. Run Pagination Tests
    // Page 1, limit 3
    res = await client.get(`/tasks/${testTaskId}/comments?page=1&limit=3`, { headers: { 'Authorization': `Bearer ${accessToken}` } });
    if (res.data.data.length !== 3) throw new Error(`Page 1: Expected 3 comments, got ${res.data.data.length}`);
    if (res.data.total !== NUM_TEST_COMMENTS) throw new Error(`Page 1: Total is incorrect.`);
    console.log('Page 1 OK:', res.data.data.map(c => c.content));

    // Page 2, limit 3
    res = await client.get(`/tasks/${testTaskId}/comments?page=2&limit=3`, { headers: { 'Authorization': `Bearer ${accessToken}` } });
    if (res.data.data.length !== 3) throw new Error(`Page 2: Expected 3 comments, got ${res.data.data.length}`);
    console.log('Page 2 OK:', res.data.data.map(c => c.content));

    // Page 3, limit 3 (should have 1 comment)
    res = await client.get(`/tasks/${testTaskId}/comments?page=3&limit=3`, { headers: { 'Authorization': `Bearer ${accessToken}` } });
    if (res.data.data.length !== 1) throw new Error(`Page 3: Expected 1 comment, got ${res.data.data.length}`);
    console.log('Page 3 OK:', res.data.data.map(c => c.content));

    // Page 4, limit 3 (should be empty)
    res = await client.get(`/tasks/${testTaskId}/comments?page=4&limit=3`, { headers: { 'Authorization': `Bearer ${accessToken}` } });
    if (res.data.data.length !== 0) throw new Error(`Page 4: Expected 0 comments, got ${res.data.data.length}`);
    console.log('Page 4 OK (empty as expected)');

    console.log('All comment pagination tests passed!');

  } catch (e) {
    if (e.response) {
      console.error('Test failed with response:', {
        status: e.response.status,
        data: e.response.data,
        config: { url: e.response.config.url, method: e.response.config.method }
      });
    } else {
      console.error('Test failed:', e.message);
    }
    process.exit(1);
  } finally {
    // 5. Cleanup: Delete the test task
    if (testTaskId) {
      console.log('Cleaning up created task...');
      try {
        const res = await client.get('/auth/csrf-token');
        const csrfToken = res.data.csrfToken;
        await client.delete(`/tasks/${testTaskId}`, { headers: { 'Authorization': `Bearer ${accessToken}`, 'x-csrf-token': csrfToken } });
        console.log('Cleanup complete.');
      } catch (e) {
        console.warn(`Failed to delete task ${testTaskId}:`, e.message);
      }
    }
  }
}

main();
