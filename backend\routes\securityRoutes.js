/**
 * Security Routes
 * Endpoints for security monitoring and management
 */

const express = require('express');
const { enhancedProtect, enhancedAdminOnly } = require('../middleware/enhancedAuthMiddleware');
const { securityAudit, SECURITY_EVENTS, logSecurityEvent } = require('../utils/securityAudit');
const { handleServerError } = require('../utils/errorHandlers');

const router = express.Router();

// @desc    Get security dashboard data
// @route   GET /api/security/dashboard
// @access  Private (Admin only)
router.get('/dashboard', enhancedProtect, enhancedAdminOnly, async (req, res) => {
  try {
    const timeframe = req.query.timeframe || '24h';
    const report = securityAudit.generateReport(timeframe);
    const healthCheck = securityAudit.healthCheck();

    res.json({
      success: true,
      data: {
        report,
        healthCheck,
        timestamp: new Date().toISOString()
      }
    });

    // Log admin access
    logSecurityEvent(SECURITY_EVENTS.ADMIN_ACTION, {
      action: 'view_security_dashboard',
      timeframe
    }, 'info', req);

  } catch (error) {
    handleServerError(res, error);
  }
});

// @desc    Get security events
// @route   GET /api/security/events
// @access  Private (Admin only)
router.get('/events', enhancedProtect, enhancedAdminOnly, async (req, res) => {
  try {
    const filters = {
      type: req.query.type,
      severity: req.query.severity,
      userId: req.query.userId,
      ip: req.query.ip,
      since: req.query.since,
      limit: parseInt(req.query.limit) || 100
    };

    const events = securityAudit.getEvents(filters);

    res.json({
      success: true,
      data: {
        events,
        total: events.length,
        filters
      }
    });

    // Log admin access
    logSecurityEvent(SECURITY_EVENTS.ADMIN_ACTION, {
      action: 'view_security_events',
      filters
    }, 'info', req);

  } catch (error) {
    handleServerError(res, error);
  }
});

// @desc    Get security health status
// @route   GET /api/security/health
// @access  Private (Admin only)
router.get('/health', enhancedProtect, enhancedAdminOnly, async (req, res) => {
  try {
    const healthCheck = securityAudit.healthCheck();

    res.json({
      success: true,
      data: healthCheck
    });

  } catch (error) {
    handleServerError(res, error);
  }
});

// @desc    Export security report
// @route   GET /api/security/export
// @access  Private (Admin only)
router.get('/export', enhancedProtect, enhancedAdminOnly, async (req, res) => {
  try {
    const timeframe = req.query.timeframe || '24h';
    const format = req.query.format || 'json';

    const report = securityAudit.generateReport(timeframe);
    const events = securityAudit.getEvents({
      since: new Date(Date.now() - getTimeframeMs(timeframe)).toISOString(),
      limit: 10000
    });

    const exportData = {
      generatedAt: new Date().toISOString(),
      timeframe,
      report,
      events
    };

    // Log data export
    logSecurityEvent(SECURITY_EVENTS.DATA_EXPORT, {
      type: 'security_report',
      timeframe,
      format,
      eventCount: events.length
    }, 'info', req);

    if (format === 'csv') {
      // Convert to CSV format
      const csv = convertToCSV(events);
      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', `attachment; filename="security-report-${timeframe}.csv"`);
      res.send(csv);
    } else {
      // JSON format
      res.setHeader('Content-Type', 'application/json');
      res.setHeader('Content-Disposition', `attachment; filename="security-report-${timeframe}.json"`);
      res.json(exportData);
    }

  } catch (error) {
    handleServerError(res, error);
  }
});

// @desc    CSP violation reporting endpoint
// @route   POST /api/security/csp-violation
// @access  Public (for CSP reports)
router.post('/csp-violation', async (req, res) => {
  try {
    const violation = req.body;

    // Log CSP violation
    logSecurityEvent(SECURITY_EVENTS.CSRF_VIOLATION, {
      violation,
      userAgent: req.get('User-Agent')
    }, 'warning', req);

    res.status(204).send(); // No content response for CSP reports

  } catch (error) {
    // Don't expose errors for CSP endpoint
    res.status(204).send();
  }
});

// @desc    Security scan endpoint
// @route   POST /api/security/scan
// @access  Private (Admin only)
router.post('/scan', enhancedProtect, enhancedAdminOnly, async (req, res) => {
  try {
    const scanType = req.body.type || 'basic';
    const scanResults = await performSecurityScan(scanType);

    // Log security scan
    logSecurityEvent(SECURITY_EVENTS.SECURITY_SCAN, {
      scanType,
      results: scanResults
    }, 'info', req);

    res.json({
      success: true,
      data: scanResults
    });

  } catch (error) {
    handleServerError(res, error);
  }
});

// @desc    Block IP address
// @route   POST /api/security/block-ip
// @access  Private (Admin only)
router.post('/block-ip', enhancedProtect, enhancedAdminOnly, async (req, res) => {
  try {
    const { ip, reason, duration } = req.body;

    if (!ip) {
      return res.status(400).json({
        success: false,
        message: 'IP address is required'
      });
    }

    // In a production environment, you would integrate with your firewall/load balancer
    // For now, we'll just log the action
    logSecurityEvent(SECURITY_EVENTS.ADMIN_ACTION, {
      action: 'block_ip',
      targetIp: ip,
      reason,
      duration
    }, 'high', req);

    res.json({
      success: true,
      message: `IP ${ip} has been blocked`,
      data: { ip, reason, duration }
    });

  } catch (error) {
    handleServerError(res, error);
  }
});

// Helper functions

/**
 * Convert timeframe string to milliseconds
 */
function getTimeframeMs(timeframe) {
  switch (timeframe) {
    case '1h':
      return 60 * 60 * 1000;
    case '24h':
      return 24 * 60 * 60 * 1000;
    case '7d':
      return 7 * 24 * 60 * 60 * 1000;
    case '30d':
      return 30 * 24 * 60 * 60 * 1000;
    default:
      return 24 * 60 * 60 * 1000;
  }
}

/**
 * Convert events to CSV format
 */
function convertToCSV(events) {
  if (events.length === 0) return 'No events found';

  const headers = ['timestamp', 'type', 'severity', 'userId', 'ip', 'details'];
  const csvRows = [headers.join(',')];

  events.forEach(event => {
    const row = [
      event.timestamp,
      event.type,
      event.severity,
      event.userId || '',
      event.ip || '',
      JSON.stringify(event.details).replace(/"/g, '""')
    ];
    csvRows.push(row.join(','));
  });

  return csvRows.join('\n');
}

/**
 * Perform security scan
 */
async function performSecurityScan(scanType) {
  const results = {
    scanType,
    timestamp: new Date().toISOString(),
    findings: []
  };

  try {
    // Basic security checks
    if (scanType === 'basic' || scanType === 'full') {
      // Check for common vulnerabilities
      results.findings.push(...await checkCommonVulnerabilities());
    }

    // File system checks
    if (scanType === 'filesystem' || scanType === 'full') {
      results.findings.push(...await checkFileSystemSecurity());
    }

    // Configuration checks
    if (scanType === 'config' || scanType === 'full') {
      results.findings.push(...await checkConfigurationSecurity());
    }

    results.status = results.findings.length === 0 ? 'clean' : 'issues_found';
    results.issueCount = results.findings.length;

  } catch (error) {
    results.status = 'error';
    results.error = error.message;
  }

  return results;
}

/**
 * Check for common vulnerabilities
 */
async function checkCommonVulnerabilities() {
  const findings = [];

  // Check environment variables
  if (!process.env.JWT_SECRET || process.env.JWT_SECRET.length < 32) {
    findings.push({
      type: 'weak_jwt_secret',
      severity: 'high',
      message: 'JWT secret is weak or missing'
    });
  }

  // Check for debug mode in production
  if (process.env.NODE_ENV === 'production' && process.env.DEBUG) {
    findings.push({
      type: 'debug_in_production',
      severity: 'medium',
      message: 'Debug mode enabled in production'
    });
  }

  return findings;
}

/**
 * Check file system security
 */
async function checkFileSystemSecurity() {
  const findings = [];
  const fs = require('fs');
  const path = require('path');

  try {
    // Check uploads directory permissions
    const uploadsDir = path.join(__dirname, '../uploads');
    if (fs.existsSync(uploadsDir)) {
      const stats = fs.statSync(uploadsDir);
      const mode = stats.mode & parseInt('777', 8);
      
      if (mode > parseInt('755', 8)) {
        findings.push({
          type: 'insecure_directory_permissions',
          severity: 'medium',
          message: 'Uploads directory has overly permissive permissions'
        });
      }
    }
  } catch (error) {
    findings.push({
      type: 'filesystem_check_error',
      severity: 'low',
      message: 'Could not check file system security'
    });
  }

  return findings;
}

/**
 * Check configuration security
 */
async function checkConfigurationSecurity() {
  const findings = [];

  // Check for default passwords or keys
  const defaultValues = ['admin', 'password', 'secret', '123456'];
  
  for (const [key, value] of Object.entries(process.env)) {
    if (key.toLowerCase().includes('password') || key.toLowerCase().includes('secret')) {
      if (defaultValues.some(defaultVal => 
        value && value.toLowerCase().includes(defaultVal))) {
        findings.push({
          type: 'default_credentials',
          severity: 'critical',
          message: `Environment variable ${key} appears to use default value`
        });
      }
    }
  }

  return findings;
}

module.exports = router;
