import React, { useState, useEffect, useCallback, useMemo } from "react";
import { useNavigate } from "react-router-dom";
import DashboardLayout from "../../components/layout/DashboardLayout";
import axiosInstance from "../../utils/axiosInstance";
import { API_PATHS, BASE_URL } from "../../utils/apiPaths";
import { LuFileSpreadsheet } from "react-icons/lu";
import { HiExclamation } from "react-icons/hi";
import { FiAlertTriangle, FiServer, FiWifiOff, FiRefreshCw, FiLock } from "react-icons/fi";
import TaskStatusTabs from "../../components/TaskStatusTabs";
import TaskCard from "../../components/cards/TaskCard";
import TaskDetailModal from "../../components/modals/TaskDetailModal";
import ErrorBoundary from "../../components/common/ErrorBoundary";
import LoadingSkeleton from "../../components/common/LoadingSkeleton";
import { LoadingErrorFallback } from "../../components/common/ErrorFallback";
import toast from "react-hot-toast";
import { createStatusTabs, createDummyTabs, filterTasksByStatus, mapTaskFromApi } from "../../utils/taskUtils";
import { useSocket } from "../../contexts/SocketContext";

// Enhanced error component with troubleshooting steps
const EnhancedErrorDisplay = ({ error, resetError }) => {
  // Determine what type of error it is to show appropriate guidance
  const isNetworkError = error?.toLowerCase().includes('network') || 
                         error?.toLowerCase().includes('connection') ||
                         error?.toLowerCase().includes('offline');
  
  const isAuthError = error?.toLowerCase().includes('auth') || 
                     error?.toLowerCase().includes('permission') || 
                     error?.toLowerCase().includes('login');
  
  const isServerError = error?.toLowerCase().includes('server') || 
                       error?.toLowerCase().includes('backend');

  return (
    <div className="bg-white border border-gray-200 rounded-lg shadow-sm p-6 col-span-full">
      <div className="flex items-start space-x-4">
        <div className="flex-shrink-0 bg-red-50 p-3 rounded-full">
          {isNetworkError ? (
            <FiWifiOff className="h-6 w-6 text-red-500" />
          ) : isAuthError ? (
            <FiLock className="h-6 w-6 text-red-500" />
          ) : isServerError ? (
            <FiServer className="h-6 w-6 text-red-500" />
          ) : (
            <FiAlertTriangle className="h-6 w-6 text-red-500" />
          )}
        </div>
        <div className="flex-1">
          <h3 className="text-lg font-medium text-gray-900 mb-2">Failed to Load Tasks</h3>
          <p className="text-sm text-gray-600 mb-4">{error}</p>

          <div className="bg-gray-50 p-4 rounded-md mb-4">
            <h4 className="font-medium text-gray-800 mb-2">Troubleshooting Steps:</h4>
            <ul className="text-sm text-gray-600 space-y-2 list-disc pl-5">
              {isNetworkError && (
                <>
                  <li>Check if the backend server is running</li>
                  <li>Verify your internet connection is stable</li>
                  <li>Make sure your VPN (if any) is not blocking the connection</li>
                </>
              )}
              {isAuthError && (
                <>
                  <li>Your session may have expired - try logging out and back in</li>
                  <li>Verify you have the correct permissions for this action</li>
                  <li>Clear your browser cache and cookies, then try again</li>
                </>
              )}
              {isServerError && (
                <>
                  <li>The backend server may be down or restarting</li>
                  <li>Check server logs for any errors or issues</li>
                  <li>Try again in a few minutes</li>
                </>
              )}
              {!isNetworkError && !isAuthError && !isServerError && (
                <>
                  <li>Try refreshing the page</li>
                  <li>Clear your browser cache and reload</li>
                  <li>Check browser console for specific error messages</li>
                </>
              )}
            </ul>
          </div>
          
          <div className="flex space-x-3">
            <button
              onClick={resetError}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <FiRefreshCw className="mr-2 h-4 w-4" />
              Try Again
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

const ManageTasks = () => {
  const [allTasks, setAllTasks] = useState([]);
  const [tabs, setTabs] = useState([]);
  const [filterStatus, setFilterStatus] = useState("All");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [selectedTask, setSelectedTask] = useState(null);
  const [isTaskDetailModalOpen, setIsTaskDetailModalOpen] = useState(false);
  const { socket, isConnected } = useSocket();

  const navigate = useNavigate();

  const getAllTasks = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Use axiosInstance for consistent API calls

      const response = await axiosInstance.get(API_PATHS.TASKS.GET_ALL);
      // The backend returns an object with a 'tasks' property
      if (response.data && Array.isArray(response.data.tasks)) {
        const mappedTasks = response.data.tasks.map(mapTaskFromApi);
        setAllTasks(mappedTasks);
        const statusTabs = createStatusTabs(mappedTasks);
        setTabs(statusTabs);
      } else {
        // Handle cases where the response might be structured differently or empty
        console.warn("Tasks data not found or not in expected format:", response.data);
        setAllTasks([]);
        setTabs(createDummyTabs());
      }
    } catch (error) {
      console.error('Error loading tasks:', error);

      // If API fails, show error message instead of fallback data
      setError(`Failed to load tasks: ${error.message || "Unknown error"}`);
      setAllTasks([]);
      setTabs(createDummyTabs());
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    getAllTasks();
  }, []);

  const handleClick = (task) => {
    // Re-map task to ensure consistent data structure
    const normalizedTask = mapTaskFromApi(task);
    console.log('Task clicked:', normalizedTask);
    setSelectedTask(normalizedTask);
    setIsTaskDetailModalOpen(true);
  };

  const handleCloseTaskDetailModal = () => {
    setIsTaskDetailModalOpen(false);
    setSelectedTask(null);
  };

  const handleTaskUpdate = useCallback((updatedTask) => {
    console.log('ADMIN DASHBOARD: Received task update event', updatedTask);
    const normalizedTask = mapTaskFromApi(updatedTask);

    setAllTasks(prevTasks => {
      const taskExists = prevTasks.some(t => t._id === normalizedTask._id);
      if (taskExists) {
        return prevTasks.map(task =>
          task._id === normalizedTask._id ? normalizedTask : task
        );
      } else {
        // If the task is new, add it to the list
        return [normalizedTask, ...prevTasks];
      }
    });

    if (selectedTask && selectedTask._id === normalizedTask._id) {
      setSelectedTask(normalizedTask);
    }
  }, [selectedTask]);

  useEffect(() => {
    if (!socket || !isConnected) return;

    const handleUpdate = (updatedTask) => {
      if (handleTaskUpdate) {
        handleTaskUpdate(updatedTask);
      }
    };

    socket.on('taskUpdated', handleUpdate);

    return () => {
      socket.off('taskUpdated', handleUpdate);
    };
  }, [socket, isConnected, handleTaskUpdate]);

  const handleEditTask = useCallback((taskData) => {
    // Navigate to the full-page edit view
    const normalizedTask = mapTaskFromApi(taskData);
    console.log('Editing task:', normalizedTask);
    navigate(`/admin/edit-task/${normalizedTask._id}`);
    setIsTaskDetailModalOpen(false); // Close the detail modal
  }, [navigate]);

  const handleDownloadReport = useCallback(async () => {
    // Download report functionality would go here
    toast.success("Report download started");
  }, []);

  // Note: filterTasksByStatus moved to taskUtils

  const handleStatusChange = useCallback((status) => {
    setFilterStatus(status);
  }, []);

  // Handle errors in task-related components
  const handleTaskError = useCallback(() => {
    toast.error("There was an error with the task display. We're working on fixing it.");
  }, []);

  const filteredTasks = useMemo(() => {
    return filterTasksByStatus(allTasks, filterStatus);
  }, [allTasks, filterStatus]);

  return (
    <DashboardLayout activeMenu="Tasks">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold">Manage Tasks</h1>
        <div className="flex space-x-2">
          <button
            onClick={getAllTasks}
            className="flex items-center px-4 py-2 bg-white border border-gray-300 rounded-lg shadow-sm hover:bg-gray-50 text-sm font-medium text-gray-700"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="mr-2 h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
            </svg>
            Refresh Tasks
          </button>
          <button
            onClick={handleDownloadReport}
            className="flex items-center px-4 py-2 bg-white border border-gray-300 rounded-lg shadow-sm hover:bg-gray-50 text-sm font-medium text-gray-700"
          >
            <LuFileSpreadsheet className="mr-2" /> Export Report
          </button>
          <button
            onClick={() => navigate("/admin/create-task")}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg shadow-sm hover:bg-blue-700 text-sm font-medium"
          >
            Create Task
          </button>
        </div>
      </div>

      <div className="mb-6">
        <TaskStatusTabs
          tabs={tabs}
          activeTab={filterStatus}
          onTabChange={handleStatusChange}
        />
        </div>

      <ErrorBoundary 
        fallback={(error, resetErrorBoundary) => (
          <div className="p-6 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center mb-4">
              <HiExclamation className="text-red-500 mr-2 h-6 w-6" />
              <h3 className="text-lg font-medium text-red-800">Task Display Error</h3>
            </div>
            <p className="text-sm text-red-600 mb-4">
              There was a problem displaying the tasks. This might be due to a React hooks issue.
            </p>
            <button
              onClick={resetErrorBoundary}
              className="px-4 py-2 bg-red-100 text-red-800 rounded-md hover:bg-red-200 transition-colors"
            >
              Try Again
            </button>
          </div>
        )}
        onError={handleTaskError}
        errorTitle="Task Display Error"
        errorMessage="There was a problem displaying the tasks."
      >
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {isLoading ? (
            // Loading state using reusable skeleton
            <LoadingSkeleton
              variant="card"
              count={6}
              showAvatar={true}
              className="col-span-full grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
            />
          ) : error ? (
            // Error state using enhanced error component
            <div className="col-span-full">
              <EnhancedErrorDisplay
                error={error}
                resetError={getAllTasks}
              />
            </div>
          ) : filteredTasks.length > 0 ? (
            // Tasks list
            filteredTasks.map((task) => (
              <TaskCard
                key={task._id}
                task={task}
                onClick={() => handleClick(task)}
                isSelected={selectedTask?._id === task._id}
              />
            ))
          ) : (
            // Empty state
            <div className="col-span-full text-center py-12 bg-gray-50 rounded-lg">
              <h3 className="text-lg font-medium text-gray-700 mb-2">No Tasks Found</h3>
              <p className="text-gray-500 mb-4">
                {filterStatus === "All"
                  ? "There are no tasks in the system yet."
                  : `There are no ${filterStatus} tasks.`}
              </p>
              <button
                onClick={() => navigate("/admin/create-task")}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg shadow-sm hover:bg-blue-700 text-sm font-medium"
              >
                Create Your First Task
              </button>
            </div>
          )}
        </div>
      </ErrorBoundary>

      {/* Task Detail Modal */}
      {selectedTask && (
        <TaskDetailModal
          isOpen={isTaskDetailModalOpen}
          onClose={handleCloseTaskDetailModal}
          task={selectedTask}
          onEdit={() => {
            setIsTaskDetailModalOpen(false);
            if (selectedTask?._id) {
              navigate(`/admin/edit-task/${selectedTask._id}`);
            }
          }}
          onTaskUpdate={handleTaskUpdate}
        />
      )}

    </DashboardLayout>
  );
};

export default ManageTasks;
