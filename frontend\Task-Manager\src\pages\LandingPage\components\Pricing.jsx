import React from 'react';

const Pricing = () => {
  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12" data-aos="fade-down">
          <h2 className="text-3xl font-bold mb-2">Flexible pricing for</h2>
          <h2 className="text-3xl font-bold mb-4">Every team</h2>
          <p className="text-gray-600 text-sm">Choose a plan that you need</p>
        </div>
        
        <div className="max-w-6xl mx-auto grid md:grid-cols-3 gap-6">
          {/* Free Plan */}
          <div className="bg-white rounded-lg p-6 border border-slate-200 shadow-md hover:shadow-xl transition-all duration-300 hover:scale-[1.03]" data-aos="fade-up" data-aos-delay="100">
            <div className="flex items-center mb-4">
              <div className="w-8 h-8 bg-emerald-100 rounded-lg flex items-center justify-center mr-3">
                <svg className="w-4 h-4 text-emerald-600" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                </svg>
              </div>
              <div>
                <h3 className="font-medium">Free</h3>
                <p className="text-sm text-gray-500">Basic</p>
              </div>
            </div>

            <ul className="space-y-3 mb-8">
              <li className="flex items-center text-sm text-gray-600">
                <svg className="w-4 h-4 text-emerald-500 mr-2" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                Only Task Creation
              </li>
              <li className="flex items-center text-sm text-gray-600">
                <svg className="w-4 h-4 text-emerald-500 mr-2" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                Basic reminders & due dates
              </li>
              <li className="flex items-center text-sm text-gray-600">
                <svg className="w-4 h-4 text-emerald-500 mr-2" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                Limited analytics
              </li>
            </ul>

            <div className="mt-auto">
              <p className="text-3xl font-bold mb-4">$0.0</p>
              <button className="w-full py-2.5 px-4 rounded-lg border border-slate-400 text-slate-700 font-semibold hover:bg-slate-100 hover:border-slate-500 transition-all duration-300">
                Purchase
              </button>
            </div>
          </div>

          {/* Business Plan */}
          <div className="bg-emerald-50 rounded-lg p-8 border-2 border-emerald-500 shadow-xl ring-4 ring-emerald-500/20 scale-[1.05] hover:scale-[1.08] transition-all duration-300 relative" data-aos="fade-up" data-aos-delay="200">
            <div className="flex items-center mb-4">
              <div className="w-8 h-8 bg-emerald-100 rounded-lg flex items-center justify-center mr-3">
                <svg className="w-4 h-4 text-emerald-600" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 7V3H2v18h20V7H12zM6 19H4v-2h2v2zm0-4H4v-2h2v2zm0-4H4V9h2v2zm0-4H4V5h2v2zm4 12H8v-2h2v2zm0-4H8v-2h2v2zm0-4H8V9h2v2zm0-4H8V5h2v2zm10 12h-8v-2h2v-2h-2v-2h2v-2h-2V9h8v10zm-2-8h-2v2h2v-2zm0 4h-2v2h2v-2z"/>
                </svg>
              </div>
              <div>
                <h3 className="font-medium">Business</h3>
                <p className="text-sm text-gray-500">Pro</p>
              </div>
            </div>

            <ul className="space-y-3 mb-8">
              <li className="flex items-center text-sm text-gray-600">
                <svg className="w-4 h-4 text-emerald-500 mr-2" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                Task Creation & management
              </li>
              <li className="flex items-center text-sm text-gray-600">
                <svg className="w-4 h-4 text-emerald-500 mr-2" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                Smart collaboration tools
              </li>
              <li className="flex items-center text-sm text-gray-600">
                <svg className="w-4 h-4 text-emerald-500 mr-2" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                Unlimited integrations
              </li>
            </ul>

            <div className="mt-auto">
              <p className="text-3xl font-bold mb-4">$15</p>
              <button className="w-full py-3 px-4 rounded-lg bg-emerald-500 text-white font-semibold hover:bg-emerald-600 transition-all duration-300 shadow-md hover:shadow-lg transform hover:-translate-y-0.5">
                Purchase
              </button>
            </div>
          </div>

          {/* Corporate Plan */}
          <div className="bg-white rounded-lg p-6 border border-slate-200 shadow-md hover:shadow-xl transition-all duration-300 hover:scale-[1.03]" data-aos="fade-up" data-aos-delay="300">
            <div className="flex items-center mb-4">
              <div className="w-8 h-8 bg-emerald-100 rounded-lg flex items-center justify-center mr-3">
                <svg className="w-4 h-4 text-emerald-600" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M16 11c1.66 0 2.99-1.34 2.99-3S17.66 5 16 5c-1.66 0-3 1.34-3 3s1.34 3 3 3zm-8 0c1.66 0 2.99-1.34 2.99-3S9.66 5 8 5C6.34 5 5 6.34 5 8s1.34 3 3 3zm0 2c-2.33 0-7 1.17-7 3.5V19h14v-2.5c0-2.33-4.67-3.5-7-3.5zm8 0c-.29 0-.62.02-.97.05 1.16.84 1.97 1.97 1.97 3.45V19h6v-2.5c0-2.33-4.67-3.5-7-3.5z"/>
                </svg>
              </div>
              <div>
                <h3 className="font-medium">Corporate</h3>
                <p className="text-sm text-gray-500">Team</p>
              </div>
            </div>

            <ul className="space-y-3 mb-8">
              <li className="flex items-center text-sm text-gray-600">
                <svg className="w-4 h-4 text-emerald-500 mr-2" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                Multi-user & role access
              </li>
              <li className="flex items-center text-sm text-gray-600">
                <svg className="w-4 h-4 text-emerald-500 mr-2" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                Custom onboarding
              </li>
              <li className="flex items-center text-sm text-gray-600">
                <svg className="w-4 h-4 text-emerald-500 mr-2" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                Enterprise-grade security
              </li>
            </ul>

            <div className="mt-auto">
              <p className="text-3xl font-bold mb-4">$99</p>
              <button className="w-full py-2.5 px-4 rounded-lg border border-slate-400 text-slate-700 font-semibold hover:bg-slate-100 hover:border-slate-500 transition-all duration-300">
                Purchase
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Pricing;
