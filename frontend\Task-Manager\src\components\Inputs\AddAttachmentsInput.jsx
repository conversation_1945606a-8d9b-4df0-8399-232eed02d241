import React, { useRef, useState, useCallback } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>X,
  LuFile,
  LuLink,
  LuImage,
  LuPencil,
} from "react-icons/lu";
import { UPLOADS_URL } from "../../utils/apiPaths";
import ProxyImage from "../common/ProxyImage";
import AttachmentPreviewGrid from "../common/AttachmentPreviewGrid";
import toast from "react-hot-toast";


const AddAttachmentsInput = ({ attachments = [], onAttachmentsChange, onRenameAttachment }) => {
  const fileInputRef = useRef(null);
  const [dragActive, setDragActive] = useState(false);
  const [showLinkInput, setShowLinkInput] = useState(false);
  const [linkUrl, setLinkUrl] = useState("");
  const [linkName, setLinkName] = useState("");
  const [editingAttachmentId, setEditingAttachmentId] = useState(null);
  const [currentEditName, setCurrentEditName] = useState("");

  const allowedFileTypes = [
    "image/jpeg",
    "image/png",
    "image/jpg",
    "application/pdf",
    "application/msword",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
  ];

  const MAX_FILE_SIZE = 2 * 1024 * 1024; // 2MB

  const validateFile = useCallback((file) => {
    if (!allowedFileTypes.includes(file.type)) {
      toast.error(
        `File type not allowed: ${file.name}. Only JPEG, PNG, PDF, DOC, and DOCX files are allowed.`
      );
      return false;
    }
    if (file.size > MAX_FILE_SIZE) {
      toast.error(`File too large: ${file.name}. Maximum size is 2MB.`);
      return false;
    }
    return true;
  }, []);

  const addNewAttachments = useCallback((newAttachments) => {
    const currentAttachments = attachments || [];
    const existingNames = new Set(currentAttachments.map(a => a.name));
    
    const attachmentsToAdd = newAttachments.filter(att => {
      if (existingNames.has(att.name)) {
        toast.error(`An attachment with the name "${att.name}" already exists.`);
        return false;
      }
      return true;
    });

    if (attachmentsToAdd.length > 0) {
      onAttachmentsChange([...currentAttachments, ...attachmentsToAdd]);
    }
  }, [attachments, onAttachmentsChange]);

  const handleFileChange = useCallback((e) => {
    const files = Array.from(e.target.files || []).filter(validateFile);
    if (files.length === 0) return;

    const newAttachments = files.map(file => ({
      id: `file-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      file,
      name: file.name,
      size: file.size,
      type: file.type,
      previewUrl: URL.createObjectURL(file),
      isExisting: false,
    }));

    addNewAttachments(newAttachments);

    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  }, [validateFile, addNewAttachments]);

  const handleDrop = useCallback((e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    const files = Array.from(e.dataTransfer.files || []).filter(validateFile);
    if (files.length === 0) return;

    const newAttachments = files.map(file => ({
      id: `file-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      file,
      name: file.name,
      size: file.size,
      type: file.type,
      previewUrl: URL.createObjectURL(file),
      isExisting: false,
    }));

    addNewAttachments(newAttachments);
  }, [validateFile, addNewAttachments]);

  const handleAddLink = useCallback(() => {
    if (!linkUrl.trim()) return;
    let url = linkUrl.trim();
    if (!/^https?:\/\//i.test(url)) {
      url = "https://" + url;
    }
    const newLink = {
      id: `link-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      name: linkName.trim() || url,
      url: url,
      isLink: true,
      isExisting: false,
    };
    addNewAttachments([newLink]);
    setLinkUrl("");
    setLinkName("");
    setShowLinkInput(false);
  }, [linkUrl, linkName, addNewAttachments]);

  const handleRemoveAttachment = useCallback((idToRemove) => {
    const updatedAttachments = (attachments || []).filter(att => {
      const currentId = att.id || att._id;
      return currentId !== idToRemove;
    });
    onAttachmentsChange(updatedAttachments);
  }, [attachments, onAttachmentsChange]);


  return (
    <div className="mt-2">
      <div className="flex gap-2 mb-3">
        <button
          type="button"
          className="flex items-center gap-1 px-3 py-2 text-sm text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md border border-gray-200"
          onClick={() => fileInputRef.current?.click()}
        >
          <LuPaperclip size={16} />
          <span>Add File</span>
        </button>
        <button
          type="button"
          className="flex items-center gap-1 px-3 py-2 text-sm text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md border border-gray-200"
          onClick={() => setShowLinkInput(!showLinkInput)}
        >
          <LuLink size={16} />
          <span>Add Link</span>
        </button>
      </div>

      {showLinkInput && (
        <div className="mb-3 p-3 border border-gray-200 rounded-md bg-gray-50">
          <div className="mb-2">
            <input
              type="text"
              placeholder="Enter URL"
              className="form-input mb-2"
              value={linkUrl}
              onChange={(e) => setLinkUrl(e.target.value)}
            />
            <input
              type="text"
              placeholder="Link name (optional)"
              className="form-input"
              value={linkName}
              onChange={(e) => setLinkName(e.target.value)}
            />
          </div>
          <div className="flex justify-end gap-2">
            <button
              type="button"
              className="px-3 py-2 text-sm text-gray-600 hover:bg-gray-200 rounded-md"
              onClick={() => {
                setShowLinkInput(false);
                setLinkUrl("");
                setLinkName("");
              }}
            >
              Cancel
            </button>
            <button
              type="button"
              className="px-3 py-2 text-sm text-white bg-blue-600 hover:bg-blue-700 rounded-md"
              onClick={handleAddLink}
            >
              Add Link
            </button>
          </div>
        </div>
      )}

      <div
        className={`border-2 border-dashed rounded-md p-4 text-center cursor-pointer transition-colors ${
          dragActive
            ? "border-primary bg-blue-50"
            : "border-gray-300 hover:border-gray-400"
        }`}
        onClick={() => fileInputRef.current?.click()}
        onDragOver={(e) => {
          e.preventDefault();
          e.stopPropagation();
          setDragActive(true);
        }}
        onDragLeave={(e) => {
          e.preventDefault();
          e.stopPropagation();
          setDragActive(false);
        }}
        onDrop={handleDrop}
      >
        <input
          type="file"
          multiple
          className="hidden"
          ref={fileInputRef}
          onChange={handleFileChange}
          accept={allowedFileTypes.join(",")}
        />
        <LuPaperclip className="mx-auto h-8 w-8 text-gray-400" />
        <p className="mt-1 text-sm text-gray-500">
          Click to upload or drag and drop files here
        </p>
        <p className="text-xs text-gray-400 mt-1">
          Supports images, documents, and other file types
        </p>
      </div>

      {(attachments || []).length > 0 && (
        <div className="mt-3">
          <AttachmentPreviewGrid
            attachments={attachments}
            onRemove={(att) => handleRemoveAttachment(att.id || att._id)}
            onRename={(att) => {
              setEditingAttachmentId(att.id || att._id);
              setCurrentEditName(att.name);
            }}
            showRemove={true}
            showRename={true}
            gridCols="grid-cols-2 sm:grid-cols-3 md:grid-cols-4"
          />
        </div>
      )}
    </div>
  );
};

export default AddAttachmentsInput;
