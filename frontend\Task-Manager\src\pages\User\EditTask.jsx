import React, { useState, useEffect, useCallback, useRef } from "react";
import DashboardLayout from "../../components/layout/DashboardLayout";
import { mapTaskFromApi } from '../../utils/taskUtils';
import axiosInstance from "../../utils/axiosInstance";
import { API_PATHS } from "../../utils/apiPaths";
import { PRIORITY_DATA } from "../../utils/data";
import TodoListInput from "../../components/Inputs/TodoListInput";
import AddAttachmentsInput from "../../components/Inputs/AddAttachmentsInput";
import toast from "react-hot-toast";
import { useNavigate, useParams } from "react-router-dom";
import { LuArrowLeft } from "react-icons/lu";
import { useUser } from "../../contexts/userContext";
import SelectDropdown from "../../components/Inputs/SelectDropdown";
import SelectUsers from "../../components/Inputs/SelectUsers";
import { normalizeAndValidateChecklist } from '../../utils/todoUtils';

// Use Vite env variable or fallback to localhost
const API_BASE_URL = import.meta.env?.VITE_API_BASE_URL || "http://localhost:5000";

const AttachmentPreviewModal = ({ attachment, onClose }) => {
  const url = attachment?.url || (attachment?.file instanceof Blob ? URL.createObjectURL(attachment.file) : null);
  const isImage = attachment?.type?.startsWith('image/') || attachment?.name?.match(/\.(jpeg|jpg|gif|png|webp)$/i);
  const isPdf = attachment?.type === 'application/pdf' || attachment?.name?.match(/\.pdf$/i);
  const isText = attachment?.type?.startsWith('text/') || attachment?.name?.match(/\.(txt|csv|log|md)$/i);

  useEffect(() => {
    if (!attachment) return;
    const blobUrl = url && url.startsWith('blob:') ? url : null;
    return () => {
      if (blobUrl) {
        URL.revokeObjectURL(blobUrl);
      }
    };
  }, [attachment, url]);

  if (!attachment) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/80 p-4" onClick={onClose}>
      <div className="relative max-w-4xl w-full max-h-[90vh]" onClick={e => e.stopPropagation()}>
        <button 
          onClick={onClose} 
          className="absolute -top-10 right-0 text-white hover:text-gray-300 text-2xl"
          aria-label="Close preview"
        >
          &times;
        </button>
        {isImage ? (
          <img 
            src={url} 
            alt={attachment.name || 'Preview'} 
            className="max-w-full max-h-[80vh] mx-auto object-contain"
          />
        ) : isPdf ? (
          <iframe 
            src={url} 
            title={attachment.name || 'Preview'} 
            className="w-full h-[80vh] bg-white"
            frameBorder="0"
          />
        ) : isText ? (
          <iframe 
            src={url} 
            title={attachment.name || 'Preview'} 
            className="w-full h-[80vh] bg-white"
            frameBorder="0"
          />
        ) : (
          <div className="flex flex-col items-center justify-center h-[80vh] bg-white rounded-lg">
            <p className="text-gray-700 text-lg font-semibold mb-2">Preview not available</p>
            <p className="text-gray-500 text-sm">This file type cannot be previewed. Please download to view.</p>
            <a
              href={url}
              download={attachment.name}
              className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition"
            >
              Download
            </a>
          </div>
        )}
      </div>
    </div>
  );
};

const EditTask = () => {
  const { id: taskId } = useParams();
  const navigate = useNavigate();
  const { user } = useUser();

  const [taskData, setTaskData] = useState({
    title: "",
    description: "",
    priority: "medium",
    dueDate: "",
    assignedTo: [],
    todoChecklist: [],
    attachments: [],
  });

  // Ensure all attachments have unique IDs
  const ensureAttachmentIds = (attachments) => {
    return (attachments || []).map(att => ({
      ...att,
      id: att.id || att._id || att.url || (att.name + '-' + Math.random().toString(36).substr(2, 9)),
    }));
  };

  // Handler for renaming attachments
  const handleRenameAttachment = (idToUpdate, newName) => {
    setTaskData(prev => ({
      ...prev,
      attachments: (prev.attachments || []).map(att =>
        (att.id === idToUpdate || att._id === idToUpdate) ? { ...att, name: newName } : att
      ),
    }));
  };

  
  const [loading, setLoading] = useState(false);
  const [fetching, setFetching] = useState(true);
  const [error, setError] = useState("");
  const [previewAttachment, setPreviewAttachment] = useState(null);
  const blobUrlsRef = useRef(new Set());

  const isReadOnly = user.role !== 'admin';

  // Fetch task data
  useEffect(() => {
    const fetchTask = async () => {
      try {
        const response = await axiosInstance.get(API_PATHS.TASKS.GET_BY_ID(taskId));
        const task = response.data.task || response.data;

        // Use mapTaskFromApi for robust normalization
        const normalizedTask = mapTaskFromApi(task);
        setTaskData({
          title: normalizedTask.title || '',
          description: normalizedTask.description || '',
          priority: normalizedTask.priority || 'medium',
          dueDate: normalizedTask.dueDate ? new Date(normalizedTask.dueDate).toISOString().split('T')[0] : '',
          assignedTo: normalizedTask.assignedTo || [],
          todoChecklist: normalizedTask.todoCheckList || [],
          attachments: ensureAttachmentIds(normalizedTask.attachments || []),
        });
      } catch (err) {
        console.error("Failed to fetch task:", err);
        setError("Failed to load task data. You may not have permission or the task may not exist.");
        toast.error("Failed to load task data.");
      } finally {
        setFetching(false);
      }
    };

    if (taskId) {
      fetchTask();
    }

    return () => {
      blobUrlsRef.current.forEach(url => URL.revokeObjectURL(url));
      blobUrlsRef.current.clear();
    };
  }, [taskId]);

  const handleValueChange = (key, value) => {
    setTaskData(prev => ({ ...prev, [key]: value }));
  };

  const handleAttachmentPreview = (attachment) => {
    if (attachment.isExisting) {
      setPreviewAttachment(attachment);
    } else {
      const url = URL.createObjectURL(attachment);
      blobUrlsRef.current.add(url);
      setPreviewAttachment({ ...attachment, url });
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    try {
      const validTodos = (taskData.todoChecklist || []).filter(item => item.text && item.text.trim() !== '');
      if ((taskData.todoChecklist || []).length > 0 && validTodos.length !== (taskData.todoChecklist || []).length) {
        toast.error('All todo items must have text. Please fill them in or remove empty ones.');
        setTaskData(prev => ({ ...prev, todoChecklist: validTodos }));
        setLoading(false);
        return;
      }
      const normalizedTodos = normalizeAndValidateChecklist(validTodos);
      const formData = new FormData();
      // Append fields based on permissions
      if (!isReadOnly) {
        formData.append("title", taskData.title);
        // Always send priority as lowercase
        formData.append("priority", (taskData.priority || '').toLowerCase());
        if (taskData.dueDate) {
          formData.append("dueDate", taskData.dueDate);
        }
        if (taskData.assignedTo && taskData.assignedTo.length > 0) {
          taskData.assignedTo.forEach(user => {
            const id = typeof user === 'object' && user._id ? user._id : user;
            if (id) formData.append('team', id);
          });
        }
      }
      formData.append("description", taskData.description);

      // --- Robust Attachment Handling ---
      // 1. Collect IDs of attachments to keep (existing)
      const existingAttachments = taskData.attachments.filter(a => a.isExisting && (a._id || a.id));
      // 2. Collect new files to upload
      const newAttachments = taskData.attachments.filter(a => !a.isExisting && a.file instanceof File);
      // 3. Collect new link attachments (not files, not existing)
      const newLinks = taskData.attachments.filter(a => !a.isExisting && a.isLink && a.url);

      // Always send the IDs of existing attachments (even if empty)
      formData.append(
        "existingAttachments",
        JSON.stringify(existingAttachments.map(att => att._id || att.id))
      );

      // Append new files as 'attachments[]'
      newAttachments.forEach(att => {
        if (att.file instanceof File && (att.name || att.file.name)) {
          formData.append("attachments[]", att.file, att.name || att.file.name);
        }
      });

      // Append new link attachments as JSON
      if (newLinks.length > 0) {
        formData.append("newLinks", JSON.stringify(newLinks.map(link => ({
          name: link.name || link.url,
          url: link.url,
          isLink: true
        }))));
      }

      // Instead of stringifying, append each todo item as an object
      normalizedTodos.forEach((todo, idx) => {
        Object.keys(todo).forEach(key => {
          formData.append(`todoCheckList[${idx}][${key}]`, todo[key]);
        });
      });

      // Override axios header for multipart form data
      const config = {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      };

      // Let the browser automatically set the correct multipart/form-data headers with boundary
      await axiosInstance.put(API_PATHS.TASKS.UPDATE(taskId), formData, config);

      toast.success("Task updated successfully!");
      navigate(`/user/tasks/${taskId}`, { state: { taskUpdated: true } });

    } catch (error) {
      console.error('Failed to update task:', error);
      const errorMessage = error.response?.data?.message || 'An error occurred while updating the task.';
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  if (fetching) {
    return (
      <DashboardLayout>
        <div className="flex justify-center items-center h-64">
          <p className="text-lg">Loading task details...</p>
        </div>
      </DashboardLayout>
    );
  }

  if (error) {
    return (
      <DashboardLayout>
        <div className="flex flex-col justify-center items-center h-64 bg-red-50 p-4 rounded-lg">
          <p className="text-red-600 text-lg font-semibold">Error</p>
          <p className="text-red-500 mt-2 text-center">{error}</p>
          <button 
            onClick={() => navigate(-1)} 
            className="mt-4 px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
          >
            Go Back
          </button>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="max-w-3xl mx-auto bg-white p-8 rounded-2xl shadow-lg">
        <h2 className="text-3xl font-bold mb-8 text-gray-800">Edit Task</h2>
        <form onSubmit={handleSubmit} className="space-y-6">
          
          {/* Task Title */}
          <div>
            <label htmlFor="title" className="block text-sm font-medium text-gray-600 mb-1">Title</label>
            <input
              type="text"
              id="title"
              value={taskData.title}
              onChange={(e) => handleValueChange('title', e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition disabled:bg-gray-100"
              required
              disabled={isReadOnly}
            />
            {isReadOnly && <p className="text-xs text-gray-500 mt-1">Only admins can edit the title.</p>}
          </div>

          {/* Task Description */}
          <div>
            <label htmlFor="description" className="block text-sm font-medium text-gray-600 mb-1">Description</label>
            <textarea
              id="description"
              rows="5"
              value={taskData.description}
              onChange={(e) => handleValueChange('description', e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition"
            ></textarea>
          </div>

          {/* Priority and Due Date */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label htmlFor="priority" className="block text-sm font-medium text-gray-600 mb-1">Priority</label>
              <SelectDropdown
                options={PRIORITY_DATA}
                selected={taskData.priority}
                onSelect={(priority) => handleValueChange('priority', priority.value)}
                disabled={isReadOnly}
              />
              {isReadOnly && <p className="text-xs text-gray-500 mt-1">Only admins can change priority.</p>}
            </div>
            <div>
              <label htmlFor="dueDate" className="block text-sm font-medium text-gray-600 mb-1">Due Date</label>
              <input
                type="date"
                id="dueDate"
                value={taskData.dueDate}
                onChange={(e) => handleValueChange('dueDate', e.target.value)}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition disabled:bg-gray-100"
                disabled={isReadOnly}
              />
              {isReadOnly && <p className="text-xs text-gray-500 mt-1">Only admins can set the due date.</p>}
            </div>
          </div>

          {/* Assign Users */}
          <div>
            <label className="block text-sm font-medium text-gray-600 mb-1">Assign To</label>
            <SelectUsers
              selectedUsers={taskData.assignedTo}
              onChange={(users) => handleValueChange('assignedTo', users)}
              disabled={isReadOnly}
            />
            {isReadOnly && <p className="text-xs text-gray-500 mt-1">Only admins can assign users.</p>}
          </div>

          {/* Todo Checklist */}
          <div>
            <label className="block text-sm font-medium text-gray-600 mb-2">Todo Checklist</label>
            <TodoListInput
              todos={taskData.todoChecklist}
              setTodos={(todos) => handleValueChange('todoChecklist', todos)}
            />
          </div>

          {/* Attachments */}
          <div>
            <label className="block text-sm font-medium text-gray-600 mb-2">Attachments</label>
            <AddAttachmentsInput
              attachments={taskData.attachments}
              onAttachmentsChange={(attachments) => handleValueChange('attachments', attachments)}
              onPreview={handleAttachmentPreview}
              onRenameAttachment={handleRenameAttachment}
            />
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end items-center pt-6 border-t border-gray-200 mt-8">
            <button
              type="button"
              onClick={() => navigate(-1)}
              className="mr-4 px-6 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-6 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition disabled:bg-blue-300"
              disabled={loading}
            >
              {loading ? 'Updating...' : 'Update Task'}
            </button>
          </div>
        </form>
      </div>

      {previewAttachment && (
        <AttachmentPreviewModal
          attachment={previewAttachment}
          onClose={() => setPreviewAttachment(null)}
        />
      )}
    </DashboardLayout>
  );
};

export default EditTask;
