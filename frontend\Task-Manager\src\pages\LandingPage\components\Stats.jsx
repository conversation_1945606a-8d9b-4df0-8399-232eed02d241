import React from 'react';
import CountUp from 'react-countup';

/**
 * Stats section component for the landing page
 * @returns {JSX.Element} - Rendered component
 */
const Stats = () => {
  return (
    <section className="py-20 sm:py-24 bg-gradient-to-r from-cyan-500 via-blue-500 to-indigo-600 text-white">
      <div className="container mx-auto px-6 sm:px-8 md:px-16">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-10 sm:gap-12 text-center">
          {/* Stat 1 */}
          <div data-aos="fade-up" data-aos-delay="0">
            <p className="text-5xl md:text-6xl font-extrabold">
              <CountUp end={3.5} decimals={1} duration={2.5} suffix="K+" enableScrollSpy scrollSpyOnce={true} />
            </p>
            <p className="mt-3 text-base opacity-90 leading-relaxed">Successfully delivered multiple projects.</p>
          </div>
          
          {/* Stat 2 */}
          <div data-aos="fade-up" data-aos-delay="100">
            <p className="text-5xl md:text-6xl font-extrabold">
              <CountUp end={12} duration={2.5} suffix="+" enableScrollSpy scrollSpyOnce={true} />
            </p>
            <p className="mt-3 text-base opacity-90 leading-relaxed">Professional experience delivering quality results.</p>
          </div>
          
          {/* Stat 3 */}
          <div data-aos="fade-up" data-aos-delay="200">
            <p className="text-5xl md:text-6xl font-extrabold">
              <CountUp end={1.6} decimals={1} duration={2.5} suffix="K+" enableScrollSpy scrollSpyOnce={true} />
            </p>
            <p className="mt-3 text-base opacity-90 leading-relaxed">Proudly serving clients across the globe solutions.</p>
          </div>
          
          {/* Stat 4 */}
          <div data-aos="fade-up" data-aos-delay="300">
            <p className="text-5xl md:text-6xl font-extrabold">
              <CountUp end={22} duration={2.5} suffix="+" enableScrollSpy scrollSpyOnce={true} />
            </p>
            <p className="mt-3 text-base opacity-90 leading-relaxed">A trusted company reliability and excellence.</p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Stats;
