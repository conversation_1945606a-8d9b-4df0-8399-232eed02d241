/**
 * Task Notification Service
 * Handles all task-related notifications
 * Separated from controller for better maintainability
 */

const Notification = require('../models/Notification');
const { getSocketInstance } = require('../socket/socketInstance');

/**
 * Emits a real-time notification to a user via WebSockets.
 * @param {Object} notification - The notification document to be sent.
 */
const emitNotification = async (notification) => {
  const io = getSocketInstance();
  if (io && notification && notification.recipient) {
    try {
      // Populate necessary fields for the client-side notification
      const populatedNotification = await Notification.findById(notification._id)
        .populate('triggeredBy', 'name profileImageUrl')
        .lean();

      if (populatedNotification) {
        io.to(notification.recipient.toString()).emit('newNotification', populatedNotification);
      }
    } catch (error) {
      console.error(`[Notification] Failed to emit socket notification for user ${notification.recipient}:`, error);
    }
  }
};

/**
 * Create task assignment notifications
 * @param {Object} task - Task object
 * @param {Array} assignedUserIds - Array of user IDs assigned to the task
 * @param {String} createdBy - User ID who created the task
 * @returns {Array} Created notifications
 */
const createTaskAssignmentNotifications = async (task, assignedUserIds, triggeredBy) => {
  try {
    const notifications = [];
    for (const userId of assignedUserIds) {
      // Skip notification for the user who triggered the action
      if (userId.toString() === triggeredBy.toString()) {
        continue;
      }
      const notification = await Notification.create({
        recipient: userId,
        triggeredBy: triggeredBy,
        type: 'task_assigned',
        title: 'Assigned to Task',
        message: `You have been assigned to the task: "${task.title}"`,
        data: {
          taskId: task._id,
          taskTitle: task.title,
        },
        isRead: false,
      });
      
      await emitNotification(notification);
      notifications.push(notification);
    }
    return notifications;
  } catch (error) {
    console.error('[Notification] Failed to create task assignment notifications:', {
      error: error.message,
      taskId: task?._id,
    });
    return [];
  }
};

/**
 * Create task status update notification
 * @param {Object} task - Task object
 * @param {String} oldStatus - Previous status
 * @param {String} newStatus - New status
 * @param {String} updatedBy - User ID who updated the status
 * @returns {Array} Created notifications
 */
const createTaskStatusUpdateNotifications = async (task, oldStatus, newStatus, updatedBy) => {
  try {
    const notifications = [];
    const assignedUsers = Array.isArray(task.team) ? task.team : [];
    const notified = new Set();

    for (const user of assignedUsers) {
      const userId = user._id ? user._id.toString() : user.toString();
      if (userId === updatedBy.toString() || notified.has(userId)) {
        continue;
      }
      notified.add(userId);

      const notification = await Notification.create({
        recipient: userId,
        triggeredBy: updatedBy,
        type: 'task_status_updated',
        title: 'Task Status Updated',
        message: `Task "${task.title}" status changed from ${oldStatus} to ${newStatus}`,
        data: {
          taskId: task._id,
          taskTitle: task.title,
          oldStatus,
          newStatus,
        },
        isRead: false,
      });
      await emitNotification(notification);
      notifications.push(notification);
    }
    return notifications;
  } catch (error) {
    console.error('[Notification] Failed to create task status update notifications:', {
      error: error.message,
      taskId: task?._id,
    });
    return [];
  }
};

/**
 * Create task due date reminder notifications
 * @param {Object} task - Task object
 * @returns {Array} Created notifications
 */
const createTaskDueDateReminders = async (task) => {
  try {
    const notifications = [];
    
    // Only create reminders for non-completed tasks
    if (task.status === 'Completed' || !task.dueDate) {
      return notifications;
    }
    
    const dueDate = new Date(task.dueDate);
    const now = new Date();
    const timeDiff = dueDate - now;
    const daysDiff = Math.ceil(timeDiff / (1000 * 60 * 60 * 24));
    
    let reminderType = '';
    let reminderMessage = '';
    
    if (daysDiff < 0) {
      reminderType = 'task_overdue';
      reminderMessage = `Task "${task.title}" is overdue by ${Math.abs(daysDiff)} day(s)`;
    } else if (daysDiff === 0) {
      reminderType = 'task_due_today';
      reminderMessage = `Task "${task.title}" is due today`;
    } else if (daysDiff === 1) {
      reminderType = 'task_due_tomorrow';
      reminderMessage = `Task "${task.title}" is due tomorrow`;
    } else if (daysDiff <= 3) {
      reminderType = 'task_due_soon';
      reminderMessage = `Task "${task.title}" is due in ${daysDiff} days`;
    }
    
    if (reminderType) {
      for (const userId of task.assignedTo) {
        const notification = await Notification.create({
          recipient: userId,
          type: reminderType,
          title: 'Task Due Date Reminder',
          message: reminderMessage,
          data: {
            taskId: task._id,
            taskTitle: task.title,
            taskDueDate: task.dueDate,
            daysDiff
          },
          isRead: false
        });
        
        notifications.push(notification);
      }
    }
    
    return notifications;
  } catch (error) {
    throw new Error(`Failed to create task due date reminders: ${error.message}`);
  }
};

/**
 * Create task comment notification
 * @param {Object} task - Task object
 * @param {String} comment - Comment text
 * @param {String} commentBy - User ID who made the comment
 * @returns {Array} Created notifications
 */
const createTaskCommentNotifications = async (task, comment, commentBy) => {
  try {
    const notifications = [];
    
    // Notify all assigned users except the commenter
    for (const userId of task.assignedTo) {
      if (userId.toString() === commentBy.toString()) {
        continue;
      }
      
      const notification = await Notification.create({
        recipient: userId,
        type: 'task_comment',
        title: 'New Task Comment',
        message: `New comment on task "${task.title}": ${comment.substring(0, 100)}${comment.length > 100 ? '...' : ''}`,
        data: {
          taskId: task._id,
          taskTitle: task.title,
          comment: comment.substring(0, 200), // Limit stored comment length
          commentBy
        },
        isRead: false
      });
      
      notifications.push(notification);
    }
    
    return notifications;
  } catch (error) {
    throw new Error(`Failed to create task comment notifications: ${error.message}`);
  }
};

/**
 * Create task deletion notification
 * @param {Object} task - Task object (before deletion)
 * @param {String} deletedBy - User ID who deleted the task
 * @returns {Array} Created notifications
 */
const createTaskDeletionNotifications = async (task, deletedBy) => {
  try {
    const notifications = [];
    const assignedUsers = Array.isArray(task.team) ? task.team : [];

    for (const user of assignedUsers) {
      const userId = user._id ? user._id.toString() : user.toString();
      if (userId === deletedBy.toString()) {
        continue;
      }

      const notification = await Notification.create({
        recipient: userId,
        triggeredBy: deletedBy,
        type: 'task_deleted',
        title: 'Task Deleted',
        message: `The task "${task.title}" was deleted.`,
        data: {
          taskTitle: task.title,
        },
        isRead: false,
      });
      await emitNotification(notification);
      notifications.push(notification);
    }
    return notifications;
  } catch (error) {
    console.error('[Notification] Failed to create task deletion notifications:', {
      error: error.message,
      taskId: task?._id,
    });
    return [];
  }
};

/**
 * Get all task-related notifications for a user
 * @param {String} userId - User ID
 * @param {Object} options - Query options
 * @returns {Array} Notifications
 */
const getTaskNotifications = async (userId, options = {}) => {
  try {
    const {
      limit = 20,
      skip = 0,
      unreadOnly = false
    } = options;
    
    const filter = {
      recipient: userId,
      type: {
        $in: [
          'task_assigned',
          'task_status_updated',
          'task_completed',
          'task_overdue',
          'task_due_today',
          'task_due_tomorrow',
          'task_due_soon',
          'task_comment',
          'task_deleted'
        ]
      }
    };
    
    if (unreadOnly) {
      filter.isRead = false;
    }
    
    const notifications = await Notification.find(filter)
      .sort({ createdAt: -1 })
      .limit(limit)
      .skip(skip);
    
    return notifications;
  } catch (error) {
    throw new Error(`Failed to get task notifications: ${error.message}`);
  }
};

/**
 * Mark task notifications as read
 * @param {String} userId - User ID
 * @param {Array} notificationIds - Array of notification IDs (optional)
 * @returns {Object} Update result
 */
const markTaskNotificationsAsRead = async (userId, notificationIds = null) => {
  try {
    const filter = { recipient: userId };
    
    if (notificationIds && notificationIds.length > 0) {
      filter._id = { $in: notificationIds };
    } else {
      // Mark all task-related notifications as read
      filter.type = {
        $in: [
          'task_assigned',
          'task_status_updated',
          'task_completed',
          'task_overdue',
          'task_due_today',
          'task_due_tomorrow',
          'task_due_soon',
          'task_comment',
          'task_deleted'
        ]
      };
    }
    
    const result = await Notification.updateMany(
      filter,
      { isRead: true, readAt: new Date() }
    );
    
    return result;
  } catch (error) {
    throw new Error(`Failed to mark task notifications as read: ${error.message}`);
  }
};

const createTaskUserRemovedNotification = async (task, removedUserId, updatedBy) => {
  try {
    const notification = await Notification.create({
      recipient: removedUserId,
      triggeredBy: updatedBy,
      type: 'task_updated',
      title: 'Removed from Task',
      message: `You were removed from the task: "${task.title}"`,
      data: {
        taskId: task._id,
        taskTitle: task.title,
      },
      isRead: false,
    });
    await emitNotification(notification);
    return [notification];
  } catch (error) {
    console.error('[Notification] Failed to create task user removed notification:', {
      error: error.message,
      taskId: task?._id,
    });
    return [];
  }
};



const createTaskUserReplacedNotification = async (task, removedUser, addedUser, updatedBy) => {
  try {
    const notifications = [];
    const remainingUsers = task.team.filter(
      (userId) => userId.toString() !== removedUser._id.toString() && userId.toString() !== addedUser._id.toString()
    );

    for (const userId of remainingUsers) {
      if (userId.toString() === updatedBy.toString()) {
        continue;
      }
      const notification = await Notification.create({
        recipient: userId,
        type: 'task_updated',
        title: 'Task assignment updated',
        message: `On task "${task.title}", ${removedUser.name} has been replaced with ${addedUser.name}.`,
        data: {
          taskId: task._id,
          taskTitle: task.title,
          removedUser,
          addedUser,
          updatedBy,
        },
        isRead: false,
      });
      notifications.push(notification);
    }
    return notifications;
  } catch (error) {
    console.error('[Notification] Failed to create task user replaced notification:', {
      error: error.message,
      task,
      removedUser,
      addedUser,
      updatedBy,
    });
    throw new Error(`Failed to create task user replaced notification: ${error.message}`);
  }
};

module.exports = {
  createTaskAssignmentNotifications,
  createTaskStatusUpdateNotifications,
  createTaskDueDateReminders,
  createTaskCommentNotifications,
  createTaskDeletionNotifications,
  getTaskNotifications,
  markTaskNotificationsAsRead,
  createTaskUserRemovedNotification,
  createTaskUserReplacedNotification,
};
