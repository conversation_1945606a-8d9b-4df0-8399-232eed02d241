import axios from 'axios';
import { <PERSON>ieJar } from 'tough-cookie';
import { wrapper } from 'axios-cookiejar-support';
import { 
    registerNewUser, 
    loginUser, 
    logoutUser,
    createTask, 
    updateTask, 
    getNotifications,
    cleanupUser, 
    promoteUserToAdmin,
    log, 
    logError,
    BASE_URL,
    ADMIN_INVITE_TOKEN
} from './test-helpers.mjs';

const runTest = async () => {
    log('--- Starting Notification Pagination Test ---');
    const jar = new CookieJar();
    const client = wrapper(axios.create({ baseURL: BASE_URL, jar, withCredentials: true }));

    let creator, assignee;

    try {
        // 1. Setup: Create two users
        log('Step 1: Registering two new users...');
        creator = await registerNewUser(client, 'creator');
        assignee = await registerNewUser(client, 'assignee');
        log(`Creator registered with ID: ${creator.userId}`);
        log(`Assignee registered with ID: ${assignee.userId}`);

        // 2. <PERSON>gin as creator and create a task
        log('Step 2: Logging in as creator and creating a task...');
        await loginUser(client, 'creator');
        const task = await createTask(client, 'Notification Test Task', creator.userId);
        log(`Task created with ID: ${task._id}`);

        // 3. Update task to trigger notifications
        log('Step 3: Assigning task and changing status to trigger notifications...');
        const updates = {
            team: [assignee.userId],
            status: 'in-progress'
        };
        await updateTask(client, task._id, updates);
        log('Task updated. Notifications should be generated for assignee.');

        await logoutUser(client);

        // 4. Login as assignee and fetch notifications
        log('Step 4: Logging in as assignee to verify notifications...');
        await loginUser(client, 'assignee');
        const { notifications, pagination } = await getNotifications(client);

        // 5. Verification
        log('Step 5: Verifying notifications...');
        if (notifications.length < 2) {
            throw new Error(`Expected at least 2 notifications for assignee, but found ${notifications.length}`);
        }

        const assignmentNotif = notifications.find(n => n.type === 'task-assigned');
        const statusNotif = notifications.find(n => n.type === 'status-change');

        if (!assignmentNotif) {
            throw new Error('Assignment notification not found!');
        }
        if (assignmentNotif.taskId._id.toString() !== task._id.toString()) {
            throw new Error('Assignment notification has incorrect task ID.');
        }
        log('  - Assignment notification found and verified.');

        if (!statusNotif) {
            throw new Error('Status change notification not found!');
        }
        if (statusNotif.details.newStatus !== 'in-progress') {
            throw new Error(`Status change notification has incorrect new status: ${statusNotif.details.newStatus}`);
        }
        log('  - Status change notification found and verified.');

        // Verify pagination object
        if (pagination.total < 2) {
             throw new Error(`Pagination total is incorrect. Expected >= 2, got ${pagination.total}`);
        }
        log('  - Pagination object seems correct.');

        log('--- Notification Pagination Test Passed! ---');

    } catch (error) {
        logError('Notification pagination test failed.');
        logError('Full error object:');
        console.error(error);
        process.exit(1); // Exit with error code
    } finally {
        // 6. Cleanup
        log('--- Starting Cleanup ---');
        if (creator) {
            await cleanupUser('creator');
        }
        if (assignee) {
            await cleanupUser('assignee');
        }
        log('--- Cleanup Complete ---');
    }
};

runTest();
