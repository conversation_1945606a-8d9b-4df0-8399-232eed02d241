const mongoose = require("mongoose");

const connectDB = async () => {
  try {
    console.log("Connecting to MongoDB...");
    await mongoose.connect(process.env.MONGO_URI, {
      serverSelectionTimeoutMS: 10000, // Timeout after 10s instead of 30s
      socketTimeoutMS: 45000, // Close sockets after 45 seconds of inactivity
    });
    console.log("MongoDB connected!");
  } catch (err) {
    console.error("Error connecting to MongoDB", err.message);
    console.log("Continuing without database connection for now...");
    // Don't exit the process, just log the error
    // process.exit(1);
  }
};

module.exports = connectDB;
