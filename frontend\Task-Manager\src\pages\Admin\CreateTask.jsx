import React, { useState, useEffect, useCallback } from "react";
import DashboardLayout from "../../components/layout/DashboardLayout";
import { PRIORITY_DATA } from "../../utils/data";
import axiosInstance from "../../utils/axiosInstance";
import { API_PATHS } from "../../utils/apiPaths";
import SelectDropdown from "../../components/Inputs/SelectDropdown";
import SelectUsers from "../../components/Inputs/SelectUsers";
import TodoListInput from "../../components/Inputs/TodoListInput";
import AddAttachmentsInput from "../../components/Inputs/AddAttachmentsInput";
import toast from "react-hot-toast";
import { useNavigate } from "react-router-dom";
import { BiLoaderAlt } from "react-icons/bi";
import { normalizeAndValidateChecklist } from '../../utils/todoUtils';

const CreateTask = () => {
  const navigate = useNavigate();
  const [allUsers, setAllUsers] = useState([]);
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(false);
  const [taskData, setTaskData] = useState({
    title: "",
    description: "",
    priority: PRIORITY_DATA[1].value, // Medium is the default
    dueDate: "",
    assignedTo: [],
    todoChecklist: [],
    attachments: [],
  });

  const handleValueChange = useCallback((key, value) => {
    setTaskData((prevData) => ({
      ...prevData,
      [key]: value,
    }));
  }, []);

  useEffect(() => {
    const fetchUsers = async () => {
      try {
        const usersResponse = await axiosInstance.get(API_PATHS.USERS.GET_ALL);
        const rawUsers = Array.isArray(usersResponse.data)
          ? usersResponse.data
          : usersResponse.data?.data || [];
        const users = rawUsers.map(user => ({ ...user, fullName: user.name || user.fullName }));
        setAllUsers(users);
      } catch (error) {
        console.error("Error loading users:", error);
        toast.error("Could not load users.");
      }
    };
    fetchUsers();
  }, []);

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (loading) return;
    if (!taskData.title) {
      setError('Title is required');
      return;
    }
    setError("");
    setLoading(true);

    try {
      const formData = new FormData();
      formData.append("title", taskData.title);
      formData.append("description", taskData.description);
      formData.append("priority", taskData.priority);
      if (taskData.dueDate) {
        formData.append("dueDate", taskData.dueDate);
      }

      taskData.assignedTo.forEach(userId => formData.append("assignedTo[]", userId));

      const validTodos = normalizeAndValidateChecklist(taskData.todoChecklist);
      validTodos.forEach((todo, index) => {
        formData.append(`todoChecklist[${index}][text]`, todo.text);
        formData.append(`todoChecklist[${index}][completed]`, todo.completed);
      });

      taskData.attachments.forEach(attachment => {
        if (attachment.file) {
          formData.append("attachments", attachment.file, attachment.name);
        }
      });

      await axiosInstance.post(API_PATHS.TASKS.CREATE, formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });

      toast.success('Task created successfully');
      navigate('/admin/tasks');
    } catch (error) {
      toast.error(error?.response?.data?.message || 'Failed to create task');
    } finally {
      setLoading(false);
    }
  };

  return (
    <DashboardLayout>
      <div className="mt-5">
        <form onSubmit={handleSubmit}>
          <div className="grid grid-cols-1 md:grid-cols-4 mt-4">
            <div className="form-card col-span-3">
              <h2 className="text-2xl font-semibold text-slate-800 mb-6">
                Create Task
              </h2>

              <div className="mt-4">
                <label className="text-xs font-medium text-slate-600">Task Title</label>
                <input
                  placeholder="Create App UI"
                  className="form-input"
                  value={taskData.title}
                  onChange={({ target }) => handleValueChange("title", target.value)}
                />
              </div>

              <div className="mt-3">
                <label className="text-xs font-medium text-slate-600">Description</label>
                <textarea
                  placeholder="Describe task"
                  className="form-input"
                  rows={4}
                  value={taskData.description}
                  onChange={({ target }) => handleValueChange("description", target.value)}
                />
              </div>

              <div className="grid grid-cols-12 gap-4 mt-2 items-start">
                <div className="col-span-6 md:col-span-4">
                  <label className="text-xs font-medium text-slate-600 mb-1">Priority</label>
                  <SelectDropdown
                    options={PRIORITY_DATA}
                    selected={PRIORITY_DATA.find(p => p.value.toLowerCase() === taskData.priority?.toLowerCase())}
                    onSelect={(option) => handleValueChange("priority", option.value)}
                    placeholder="Select Priority"
                  />
                </div>
                <div className="col-span-6 md:col-span-4">
                  <label className="text-xs font-medium text-slate-600 mb-1">Due Date</label>
                  <input
                    className="form-input h-10"
                    value={taskData.dueDate}
                    onChange={({ target }) => handleValueChange("dueDate", target.value)}
                    type="date"
                  />
                </div>
                <div className="col-span-6 md:col-span-4">
                  <label className="text-xs font-medium text-slate-600 mb-1">Assign To</label>
                  <SelectUsers
                    selectedUsers={taskData.assignedTo}
                    onChange={(value) => handleValueChange("assignedTo", value.map(u => u._id))}
                    allUsers={allUsers}
                  />
                </div>
              </div>

              <div className="mt-4">
                <label className="text-xs font-medium text-slate-600">Todo Checklist</label>
                <TodoListInput
                  todos={taskData.todoChecklist}
                  setTodos={(value) => handleValueChange("todoChecklist", value)}
                />
              </div>

              <div className="mt-3">
                <label className="text-xs font-medium text-slate-600">Add Attachments</label>
                <AddAttachmentsInput
                  attachments={taskData.attachments}
                  onAttachmentsChange={(newAttachments) => handleValueChange("attachments", newAttachments)}
                />
              </div>

              <div className="mt-7">
                {error && <div className="text-red-500 mb-2 text-left text-xs font-medium">{error}</div>}
                <button
                  type="submit"
                  className="w-full flex justify-center items-center gap-2 h-10 px-4 py-2 text-sm font-medium text-white bg-indigo-600 rounded-md shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-70 disabled:cursor-not-allowed"
                  disabled={loading}
                >
                  {loading && <BiLoaderAlt className="animate-spin" />}
                  Create Task
                </button>
              </div>
            </div>
          </div>
        </form>
      </div>
    </DashboardLayout>
  );
};

export default CreateTask;
