import React, { useState, useRef, useEffect, useMemo, useCallback } from 'react';
import { MdSend, MdEmojiEmotions, MdAttachFile, MdMic, MdPhone, MdVideoCall, MdMenu, MdGroup, MdRefresh, MdDeleteSweep } from "react-icons/md";
import { LuTrash2 } from "react-icons/lu";
import { useUser } from '../../contexts/userContext';
import { useSocket } from '../../contexts/SocketContext';
import UserAvatar from '../common/UserAvatar';
import MessageStatusIcon from './MessageStatusIcon';
import EmojiPicker from './EmojiPicker';
import { useMessageReadTracking } from '../../hooks/useMessageReadTracking';
import { toast } from 'react-hot-toast';
import chatService from '../../services/chatService';
import logger from '../../utils/logger';
import {
  getConversationDisplayName,
  getValidOtherParticipants,
  getUserDisplayName,
  getUserAvatarUrl
} from '../../utils/userNameUtils';

const ChatWindow = ({ conversation, messages, onSendMessage, loading }) => {
  const { user } = useUser();
  const { isUserOnline, sendTypingStatus, connected, reconnect, socket } = useSocket();
  const [newMessage, setNewMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [typingUsers, setTypingUsers] = useState([]);
  const [reconnecting, setReconnecting] = useState(false);
  const [sendingOfflineMessage] = useState(false);
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [showMenuDropdown, setShowMenuDropdown] = useState(false);
  const [showClearConfirm, setShowClearConfirm] = useState(false);
  const [clearing, setClearing] = useState(false);
  const messagesEndRef = useRef(null);
  const messageListRef = useRef(null);
  const typingTimeoutRef = useRef(null);
  const emojiPickerRef = useRef(null);
  const menuDropdownRef = useRef(null);
  const isSendingRef = useRef(false);

  // Hook for automatic message read tracking
  const { observeMessage } = useMessageReadTracking(messages, conversation);
  
  // Scroll to bottom of messages when messages change
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);
  
  // Listen for typing indicators
  useEffect(() => {
    if (!socket || !conversation) return undefined;

    const handleUserTyping = ({ userId, isTyping }) => {
      if (userId === user?._id) return; // Ignore own typing events
      
      if (isTyping) {
        setTypingUsers(prev => [...prev, userId]);
        // Clear previous timeout for this user
        if (typingTimeoutRef.current && typingTimeoutRef.current[userId]) {
          clearTimeout(typingTimeoutRef.current[userId]);
        }
        
        // Setup new timeout to remove typing indicator after 5 seconds
        if (!typingTimeoutRef.current) typingTimeoutRef.current = {};
        typingTimeoutRef.current[userId] = setTimeout(() => {
          setTypingUsers(prev => prev.filter(id => id !== userId));
        }, 5000);
        } else {
        setTypingUsers(prev => prev.filter(id => id !== userId));
        // Clear timeout for this user
        if (typingTimeoutRef.current && typingTimeoutRef.current[userId]) {
          clearTimeout(typingTimeoutRef.current[userId]);
          delete typingTimeoutRef.current[userId];
        }
      }
    };
    
      socket.on('typing', handleUserTyping);

      return () => {
        socket.off('typing', handleUserTyping);
      // Clean up all typing timeouts
      if (typingTimeoutRef.current) {
        Object.values(typingTimeoutRef.current).forEach(timeout => 
          clearTimeout(timeout)
        );
      }
    };
  }, [socket, conversation, user]);
  
  // Handle reconnect button click
  const handleReconnect = async () => {
    if (reconnect) {
    setReconnecting(true);
      try {
        reconnect();
        // Short delay to show the reconnecting state
        setTimeout(() => {
          setReconnecting(false);
        }, 1500);
      } catch (error) {
        console.error('Error reconnecting:', error);
        setReconnecting(false);
        toast.error('Failed to reconnect. Please try again.');
      }
    }
  };
  
  // Get formatted conversation details
  const getConversationDetails = () => {
    if (!conversation) {
      return { title: 'Loading...', status: 'offline', typingStatus: 'idle' };
    }

    // Determine if anyone is typing
    const typingStatus = typingUsers.length > 0 ? 'typing' : 'idle';
    
    // Format differently based on conversation type
    if (conversation.type === 'individual') {
      // Get the other participant (not current user)
      const otherParticipant = conversation.participants?.find(p => p._id !== user?._id);
      
      // Check if other participant is online
      const isOnline = isUserOnline && otherParticipant ? isUserOnline(otherParticipant._id) : false;
      
      return {
        title: otherParticipant?.name || 'Chat',
        subtitle: otherParticipant?.email || '',
        status: isOnline ? 'online' : 'offline',
        typingStatus: typingStatus,
        participants: conversation.participants || []
      };
    } else {
      // Group conversation
      return {
        title: conversation.name || 'Group Chat',
        subtitle: `${conversation.participants?.length || 0} members`,
        status: 'group',
        typingStatus: typingStatus,
        participants: conversation.participants || []
      };
    }
  };
  
  const conversationDetails = getConversationDetails();

  // Function to group messages by date
  const groupMessagesByDate = useCallback((messages) => {
    if (!messages || !Array.isArray(messages) || messages.length === 0) {
      return [];
    }

    const groups = {};

    messages.forEach(message => {
      if (!message.createdAt) return;

      const date = new Date(message.createdAt);
      const dateKey = date.toLocaleDateString(undefined, {
        weekday: 'long',
        month: 'short',
        day: 'numeric',
        year: date.getFullYear() !== new Date().getFullYear() ? 'numeric' : undefined
      });

      if (!groups[dateKey]) {
        groups[dateKey] = [];
      }

      groups[dateKey].push(message);
    });

    // Convert to array format for rendering
    return Object.keys(groups).map(date => ({
      date,
      messages: groups[date]
    }));
  }, []);

  // Group messages by date for better UI organization
  const messageGroups = useMemo(() => {
    return groupMessagesByDate(messages);
  }, [messages, groupMessagesByDate]);
  
  // Handle send message
  const handleSendMessage = async (e) => {
    e.preventDefault();

    if (isSendingRef.current) {
      return;
    }
    
    if (!newMessage.trim() || !conversation?._id) return;
    
    if (onSendMessage) {
      isSendingRef.current = true;
      try {
        await onSendMessage(newMessage);
        setNewMessage('');
      } catch (error) {
        console.error('Failed to send message:', error);
        toast.error('Failed to send message');
      } finally {
        setTimeout(() => {
          isSendingRef.current = false;
        }, 500); // Prevent double sends
      }
    }
  };
  
  // Handle typing indicator
  const handleTyping = (e) => {
    const text = e.target.value;
    setNewMessage(text);
    
    // Send typing indicator via socket if connected
    if (connected && socket && conversation && !isTyping && text.length > 0) {
      setIsTyping(true);
      sendTypingStatus(conversation._id, true);
      
      // Reset typing status after 3 seconds of no input
      setTimeout(() => {
        setIsTyping(false);
        if (socket && conversation) {
        sendTypingStatus(conversation._id, false);
      }
      }, 3000);
    }
  };

  // Handle emoji selection
  const handleEmojiSelect = (emoji) => {
    setNewMessage(prev => prev + emoji);
    setShowEmojiPicker(false);
  };

  // Toggle emoji picker
  const handleEmojiPickerToggle = () => {
    setShowEmojiPicker(prev => !prev);
  };

  // Close emoji picker when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (emojiPickerRef.current && !emojiPickerRef.current.contains(event.target)) {
        setShowEmojiPicker(false);
      }
      
      if (menuDropdownRef.current && !menuDropdownRef.current.contains(event.target)) {
        setShowMenuDropdown(false);
      }
    };

      document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);


  
  // Handle clearing chat history
  const handleClearChat = async () => {
    if (!conversation?._id || clearing) return;
    
    try {
      setClearing(true);
      const response = await chatService.clearConversation(conversation._id);
      
      if (response.success) {
        toast.success('Chat history cleared');
        setShowClearConfirm(false);
        // The parent component should refresh messages
        if (onSendMessage) {
          // Using onSendMessage as a proxy to refresh messages
          // A better approach would be to have a specific onClearChat callback
          onSendMessage('');
        }
      } else {
        toast.error(response.error || 'Failed to clear chat history');
      }
    } catch (error) {
      toast.error('Failed to clear chat history');
      console.error('Error clearing chat:', error);
    } finally {
      setClearing(false);
      setShowMenuDropdown(false);
    }
  };
  
  // Get participants where current user is filtered out
  const visibleParticipants = conversation?.participants?.filter(p => p._id !== user?._id) || [];
  const remainingCount = conversationDetails.participants?.length > 3 
    ? conversationDetails.participants.length - 3 
    : 0;
  
  return (
    <div className="flex-1 flex flex-col bg-gray-50 h-full">
      {/* Chat Header */}
      <div className="flex items-center justify-between px-6 py-3 border-b bg-white w-full shadow-sm">
        <div className="flex items-center space-x-4">
          <div className="relative">
            {conversation?.type === 'individual' ? (
              <UserAvatar
                user={conversation.participants?.find(p => p._id !== user?._id)}
                showStatus
                isOnline={conversationDetails.status === 'online'}
                size={48}
              />
            ) : (
              <div className="relative flex items-center justify-center w-12 h-12 bg-gradient-to-br from-blue-400 to-indigo-600 rounded-full text-white">
                
                  <div className="relative w-12 h-12">
                    {/* Show up to 3 participant avatars in overlapping circles */}
                    {conversation.participants?.[0] && (
                      <div className="absolute -top-1 -left-1 w-7 h-7 rounded-full bg-white p-0.5">
                        <UserAvatar 
                          user={conversation.participants[0]} 
                          size={24} 
                          showStatus={false} 
                        />
                      </div>
                    )}
                    {conversation.participants?.[1] && (
                      <div className="absolute -top-1 -right-1 w-7 h-7 rounded-full bg-white p-0.5">
                        <UserAvatar 
                          user={conversation.participants[1]} 
                          size={24} 
                          showStatus={false} 
                        />
                      </div>
                    )}
                    {conversation.participants?.length > 2 && conversation.participants?.[2] && (
                      <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-7 h-7 rounded-full bg-white p-0.5">
                        <UserAvatar 
                          user={conversation.participants[2]} 
                          size={24} 
                          showStatus={false} 
                        />
                      </div>
                    )}
                  </div>
                  {conversation.participants?.length > 0 ? (
                    <div className="absolute -bottom-1 -right-1 w-5 h-5 rounded-full bg-blue-500 flex items-center justify-center text-xs font-bold text-white border-2 border-white">
                      {conversation.participants?.length || 0}
                    </div>
                  ) : (
                    <MdGroup className="w-6 h-6" />
                  )}
                </div>
            )}
          </div>
          
          <div>
            <div className="font-semibold text-gray-900">{conversationDetails.title}</div>
            {conversation?.type === 'individual' ? (
              conversationDetails.typingStatus === 'typing' ? (
                <div className="text-sm text-green-500 flex items-center">
                  <span className="flex space-x-1">
                    <span className="animate-bounce">●</span>
                    <span className="animate-bounce" style={{animationDelay: '0.2s'}}>●</span>
                    <span className="animate-bounce" style={{animationDelay: '0.4s'}}>●</span>
                  </span>
                  <span className="ml-1">Typing...</span>
                </div>
            ) : conversationDetails.status === 'online' ? (
                <div className="text-sm text-green-500 flex items-center">
                  <span className="w-2 h-2 bg-green-500 rounded-full mr-1"></span>
                  Online
                </div>
            ) : (
                <div className="text-sm text-gray-500 flex items-center">
                  <span className="w-2 h-2 bg-gray-400 rounded-full mr-1"></span>
                  Offline
                </div>
              )
            ) : (
              <div className="text-sm text-gray-500">
                {conversation?.participants?.length || 0} members
              </div>
            )}
          </div>
        </div>
        
        <div className="flex items-center space-x-3">
          {conversation?.type === 'group' && visibleParticipants.length > 0 && (
            <div className="flex -space-x-2 mr-3">
              {visibleParticipants.slice(0, 3).map((participant, index) => (
                <UserAvatar
                  key={`header-participant-${participant?._id || index}-${index}`}
                  user={participant}
                  size={32}
                  showStatus={false}
                  className="border-2 border-white"
                />
              ))}
              {remainingCount > 0 && (
                <div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center text-xs font-semibold text-gray-600 border-2 border-white">
                  +{remainingCount}
                </div>
              )}
            </div>
          )}
          <button className="w-9 h-9 flex items-center justify-center rounded-full hover:bg-gray-100 transition-colors">
            <MdPhone className="w-5 h-5 text-gray-500" />
          </button>
          <button className="w-9 h-9 flex items-center justify-center rounded-full hover:bg-gray-100 transition-colors">
            <MdVideoCall className="w-5 h-5 text-gray-500" />
          </button>
          <div className="relative" ref={menuDropdownRef}>
            <button 
              className="w-9 h-9 flex items-center justify-center rounded-full hover:bg-gray-100 transition-colors"
              onClick={() => setShowMenuDropdown(!showMenuDropdown)}
            >
            <MdMenu className="w-5 h-5 text-gray-500" />
          </button>
            
            {/* Menu Dropdown */}
            {showMenuDropdown && (
              <div className="absolute right-0 top-full mt-1 w-48 bg-white rounded-lg shadow-xl border border-gray-200 py-1 z-50">
                <button
                  onClick={() => {
                    setShowClearConfirm(true);
                    setShowMenuDropdown(false);
                  }}
                  className="w-full px-4 py-2 text-left text-sm text-red-600 hover:bg-red-50 flex items-center transition-colors"
                >
                  <MdDeleteSweep className="w-5 h-5 mr-2" />
                  Clear Chat
                </button>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Clear Chat Confirmation Modal */}
      {showClearConfirm && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="bg-white rounded-lg shadow-xl p-6 max-w-md w-full mx-4">
            <div className="text-center mb-6">
              <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <LuTrash2 className="w-6 h-6 text-red-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900">Clear Chat History?</h3>
              <p className="mt-2 text-sm text-gray-500">
                This will delete all messages in this conversation for you. This action cannot be undone.
              </p>
            </div>
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowClearConfirm(false)}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors disabled:opacity-50"
                disabled={clearing}
              >
                Cancel
              </button>
              <button
                onClick={handleClearChat}
                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors flex items-center disabled:opacity-50"
                disabled={clearing}
              >
                {clearing ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Clearing...
                  </>
                ) : (
                  'Clear Chat'
                )}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Connection status banner */}
      {!connected && (
        <div className="bg-yellow-50 border-b border-yellow-200 px-4 py-2 flex items-center justify-between">
          <div className="text-yellow-700 text-sm flex items-center">
            <span className="w-2 h-2 bg-yellow-500 rounded-full mr-2 animate-pulse"></span>
            You're disconnected from the chat server. Messages will be sent via HTTP.
          </div>
          <button
            onClick={handleReconnect}
            disabled={reconnecting}
            className="px-3 py-1 bg-yellow-100 hover:bg-yellow-200 text-yellow-800 text-sm rounded flex items-center transition-colors"
          >
            {reconnecting ? (
              <>
                <MdRefresh className="w-4 h-4 mr-1 animate-spin" />
                Connecting...
              </>
            ) : (
              <>
                <MdRefresh className="w-4 h-4 mr-1" />
                Reconnect
              </>
            )}
          </button>
        </div>
      )}
      
      {/* Chat Body */}
      {loading ? (
        <div className="flex-1 flex justify-center items-center">
          <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500"></div>
        </div>
      ) : (
        <div 
          ref={messageListRef}
          className="flex-1 px-6 py-4 overflow-y-auto scrollbar-hide space-y-6"
        >
          {messageGroups.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-full text-gray-500">
              <div className="text-6xl mb-4">👋</div>
              <p>No messages yet. Start the conversation!</p>
            </div>
          ) : (
            messageGroups.map(({ date, messages }, groupIndex) => (
            <div key={`message-group-${date}-${groupIndex}`}>
              <div className="text-center text-sm text-gray-400 my-4">{date}</div>
              <div className="space-y-6">
                  {messages.map((message, index) => {
                    if (!message || !message.sender) {
                      logger.warn("ChatWindow: Invalid message or sender", { message, index });
                      return null;
                    }

                    const isOwnMessage = message.sender._id === (user?._id || user?.id);
                    const messageKey = `message-${message._id || `temp-${Date.now()}-${index}`}-${date}-${groupIndex}-${index}`;

                    // Use utility functions for consistent name handling
                    const senderName = getUserDisplayName(message.sender);

                    return isOwnMessage ? (
                      <div key={messageKey} className="flex justify-end">
                        <div className="flex items-end space-x-2">
                          <div className={`bg-blue-500 text-white text-sm p-3 rounded-lg shadow-sm relative ${message.sending ? 'opacity-70' : ''}`}>
                            <div>{message.text}</div>
                            <div className="flex items-center justify-end mt-1 space-x-1">
                              <span className="text-xs opacity-70">
                                {new Date(message.createdAt).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}
                              </span>
                              <MessageStatusIcon
                                message={message}
                                conversation={conversation}
                                className="opacity-70"
                              />
                            </div>
                          </div>
                          <UserAvatar
                            user={user}
                            size={40}
                            showStatus={false}
                          />
                        </div>
                      </div>
                    ) : (
                      <div
                        key={messageKey}
                        className="flex items-start space-x-4"
                        ref={(el) => {
                          if (el) {
                            el.dataset.messageId = message._id;
                            el.dataset.senderId = message.sender._id;
                            observeMessage(el);
                          }
                        }}
                      >
                        <UserAvatar
                          user={message.sender}
                          size={40}
                          showStatus={false}
                        />
                        <div className="flex-1">
                          <div className="flex items-baseline space-x-2">
                            <div className="font-semibold">{senderName}</div>
                            <div className="text-xs text-gray-400">
                              {new Date(message.createdAt).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}
                            </div>
                          </div>
                          <div className="bg-white text-sm p-3 rounded-lg shadow-sm mt-1 max-w-md">
                            {message.text}
                          </div>
                        </div>
                      </div>
                    );
                  })}
              </div>
            </div>
            ))
          )}
        <div ref={messagesEndRef} />
        </div>
      )}
      
      {/* Message input */}
      <div 
        className="w-full px-6 py-3 border-t bg-white flex items-center space-x-3"
      >
        <div className="flex space-x-1 relative">
          <button className="w-10 h-10 flex items-center justify-center rounded-full hover:bg-gray-100 transition-colors text-gray-500">
            <MdAttachFile className="w-5 h-5" />
          </button>
          <div className="relative" ref={emojiPickerRef}>
            <button
              onClick={handleEmojiPickerToggle}
              className={`w-10 h-10 flex items-center justify-center rounded-full hover:bg-gray-100 transition-colors ${
                showEmojiPicker ? 'bg-blue-100 text-blue-500' : 'text-gray-500'
              }`}
            >
              <MdEmojiEmotions className="w-5 h-5" />
            </button>
            {showEmojiPicker && (
              <div className="absolute bottom-12 left-0 z-50">
                <EmojiPicker
                  onEmojiSelect={handleEmojiSelect}
                  onClose={() => setShowEmojiPicker(false)}
                />
              </div>
            )}
          </div>
        </div>
        
        <div className="relative flex-1">
          <input
            type="text"
          value={newMessage}
          onChange={handleTyping}
            placeholder="Type a message..."
            className="w-full py-2.5 px-4 bg-gray-100 rounded-full focus:outline-none focus:ring-2 focus:ring-blue-400 pr-12"
            onKeyDown={(e) => e.key === 'Enter' && !e.shiftKey && handleSendMessage(e)}
          />
            <button 
            onClick={handleSendMessage}
            disabled={!newMessage.trim() || sendingOfflineMessage}
            className={`absolute right-1 top-1/2 transform -translate-y-1/2 w-9 h-9 flex items-center justify-center rounded-full transition-colors ${
              newMessage.trim() && !sendingOfflineMessage
                ? 'bg-blue-500 text-white hover:bg-blue-600' 
                : 'bg-gray-200 text-gray-400'
            }`}
          >
            {sendingOfflineMessage ? (
              <MdRefresh className="w-5 h-5 animate-spin" />
            ) : (
              <MdSend className="w-5 h-5" />
            )}
            </button>
          </div>
        </div>
    </div>
  );
};

export default ChatWindow; 