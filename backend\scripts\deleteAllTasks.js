// Script to delete all tasks in the database. Use with CAUTION!
const path = require('path');
require('dotenv').config({ path: path.resolve(__dirname, '../.env') });
const mongoose = require('mongoose');

const Task = require(path.join(__dirname, '../models/Task'));

async function main() {
  const db = process.env.MONGO_URI;

  if (!db) {
    console.error('ERROR: MONGO_URI is not defined in your .env file.');
    console.error('Please add MONGO_URI to the .env file in the backend directory.');
    process.exit(1);
  }

  console.log(`Connecting to database: ${db.substring(0, 20)}...`); // Log URI safely
  await mongoose.connect(db, { useNewUrlParser: true, useUnifiedTopology: true });
  console.log('Connection successful.');

  const result = await Task.deleteMany({});
  console.log(`Deleted ${result.deletedCount} tasks.`);
  await mongoose.disconnect();
  console.log('Disconnected from database.');
}

main().catch(err => {
  console.error('Error deleting tasks:', err);
  process.exit(1);
});
