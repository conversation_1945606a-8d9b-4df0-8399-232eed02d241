import { createPortal } from "react-dom";

const Portal = ({ children }) => {
  const portalRoot = document.getElementById("portal-root") || createPortalRoot();
  return createPortal(children, portalRoot);
};

function createPortalRoot() {
  const portalRoot = document.createElement("div");
  portalRoot.id = "portal-root";
  document.body.appendChild(portalRoot);
  return portalRoot;
}

export default Portal;
