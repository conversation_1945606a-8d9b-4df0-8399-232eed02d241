<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image Loading Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin: 5px;
            display: inline-block;
            border: 2px solid #fff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .avatar img {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            object-fit: cover;
        }
        .avatar-fallback {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: #3498db;
            color: white;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin: 5px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
    </style>
</head>
<body>
    <h1>🖼️ Image Loading Test for Task Manager</h1>
    
    <div class="test-section">
        <h2>1. Backend Connectivity Test</h2>
        <button onclick="testBackend()">Test Backend Connection</button>
        <div id="backend-result"></div>
    </div>

    <div class="test-section">
        <h2>2. Image URL Construction Test</h2>
        <button onclick="testImageUrls()">Test Image URL Construction</button>
        <div id="url-result"></div>
    </div>

    <div class="test-section">
        <h2>3. Avatar Fallback Test</h2>
        <button onclick="testAvatarFallbacks()">Test Avatar Fallbacks</button>
        <div id="avatar-result"></div>
    </div>

    <div class="test-section">
        <h2>4. Sample User Avatars</h2>
        <div id="sample-avatars"></div>
    </div>

    <script>
        const BASE_URL = 'http://localhost:3000';
        const UPLOADS_URL = `${BASE_URL}/uploads`;

        // Test backend connectivity
        async function testBackend() {
            const resultDiv = document.getElementById('backend-result');
            resultDiv.innerHTML = '<p>Testing backend connectivity...</p>';

            try {
                const response = await fetch(`${BASE_URL}/`);
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="test-result success">
                            ✅ Backend is accessible<br>
                            Status: ${response.status}<br>
                            Message: ${data.message || 'OK'}
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="test-result error">
                            ❌ Backend returned error<br>
                            Status: ${response.status}
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="test-result error">
                        ❌ Cannot connect to backend<br>
                        Error: ${error.message}<br>
                        Make sure the backend server is running on port 3000
                    </div>
                `;
            }
        }

        // Test image URL construction
        function testImageUrls() {
            const resultDiv = document.getElementById('url-result');
            
            const testCases = [
                { input: 'profile.jpg', expected: `${UPLOADS_URL}/profile.jpg` },
                { input: '/uploads/profile.jpg', expected: `${UPLOADS_URL}/profile.jpg` },
                { input: 'uploads/profile.jpg', expected: `${UPLOADS_URL}/profile.jpg` },
                { input: 'http://example.com/image.jpg', expected: 'http://example.com/image.jpg' },
                { input: 'https://example.com/image.jpg', expected: 'https://example.com/image.jpg' },
                { input: null, expected: null },
                { input: '', expected: null },
                { input: 'null', expected: null }
            ];

            let results = '<h3>URL Construction Results:</h3>';
            
            testCases.forEach((testCase, index) => {
                const result = normalizeImageUrl(testCase.input);
                const isCorrect = result === testCase.expected;
                
                results += `
                    <div class="test-result ${isCorrect ? 'success' : 'error'}">
                        Test ${index + 1}: ${isCorrect ? '✅' : '❌'}<br>
                        Input: ${testCase.input}<br>
                        Expected: ${testCase.expected}<br>
                        Got: ${result}
                    </div>
                `;
            });

            resultDiv.innerHTML = results;
        }

        // Normalize image URL function (copied from our utility)
        function normalizeImageUrl(imagePath) {
            if (!imagePath || 
                imagePath === 'null' || 
                imagePath === null || 
                typeof imagePath !== 'string' || 
                imagePath.trim() === '') {
                return null;
            }

            const trimmedPath = imagePath.trim();

            if (trimmedPath.startsWith('data:image/')) {
                return trimmedPath;
            }

            if (trimmedPath.startsWith('http://') || trimmedPath.startsWith('https://')) {
                try {
                    new URL(trimmedPath);
                    return trimmedPath;
                } catch (error) {
                    return null;
                }
            }

            let normalizedPath = trimmedPath;
            if (normalizedPath.startsWith('/')) {
                normalizedPath = normalizedPath.substring(1);
            }
            if (normalizedPath.startsWith('uploads/')) {
                normalizedPath = normalizedPath.substring('uploads/'.length);
            }

            if (!normalizedPath || normalizedPath.length === 0) {
                return null;
            }

            return `${UPLOADS_URL}/${normalizedPath}`;
        }

        // Test avatar fallbacks
        function testAvatarFallbacks() {
            const resultDiv = document.getElementById('avatar-result');
            
            const testUsers = [
                { name: 'John Doe', profileImageUrl: 'nonexistent.jpg' },
                { name: 'Jane Smith', profileImageUrl: null },
                { name: 'Bob Johnson', profileImageUrl: '' },
                { name: 'Alice Brown', profileImageUrl: 'https://httpstat.us/404' }
            ];

            let html = '<h3>Avatar Fallback Test:</h3>';
            
            testUsers.forEach(user => {
                const initials = getUserInitials(user);
                const color = getUserAvatarColor(user.name);
                const imageUrl = normalizeImageUrl(user.profileImageUrl);
                
                html += `
                    <div style="margin: 10px 0; padding: 10px; border: 1px solid #ddd;">
                        <strong>${user.name}</strong><br>
                        Image URL: ${imageUrl || 'None'}<br>
                        Initials: ${initials}<br>
                        Color: ${color}<br>
                        <div class="avatar">
                            ${imageUrl ? 
                                `<img src="${imageUrl}" alt="${user.name}" onerror="this.style.display='none'; this.nextSibling.style.display='flex';">
                                 <div class="avatar-fallback" style="display:none; background-color: ${color}">${initials}</div>` :
                                `<div class="avatar-fallback" style="background-color: ${color}">${initials}</div>`
                            }
                        </div>
                    </div>
                `;
            });

            resultDiv.innerHTML = html;
        }

        // Get user initials
        function getUserInitials(user) {
            if (!user) return '?';
            
            const name = user.fullName || user.name || user.displayName || '';
            if (!name || typeof name !== 'string') return '?';
            
            const trimmedName = name.trim();
            if (!trimmedName) return '?';
            
            const parts = trimmedName.split(' ').filter(part => part.length > 0);
            if (parts.length >= 2) {
                return `${parts[0][0]}${parts[1][0]}`.toUpperCase();
            }
            return trimmedName.substring(0, 2).toUpperCase();
        }

        // Get user avatar color
        function getUserAvatarColor(name) {
            const colors = [
                "#1abc9c", "#2ecc71", "#3498db", "#9b59b6", "#34495e",
                "#16a085", "#27ae60", "#2980b9", "#8e44ad", "#2c3e50",
                "#f1c40f", "#e67e22", "#e74c3c", "#95a5a6", "#f39c12",
                "#d35400", "#c0392b", "#bdc3c7", "#7f8c8d"
            ];
            
            if (!name || typeof name !== 'string') {
                return colors[0];
            }
            
            let hash = 0;
            for (let i = 0; i < name.length; i++) {
                hash = name.charCodeAt(i) + ((hash << 5) - hash);
            }
            
            const index = Math.abs(hash) % colors.length;
            return colors[index];
        }

        // Create sample avatars on page load
        window.onload = function() {
            const sampleDiv = document.getElementById('sample-avatars');

            // Test with actual database URLs from our analysis
            const sampleUsers = [
                { name: 'Madara Uchiha', profileImageUrl: '/uploads/1749077124012-375946.png' },
                { name: 'Pain Nagato', profileImageUrl: '/uploads/1749139036326-304443.png' },
                { name: 'Naruto Uzumaki', profileImageUrl: '/uploads/1749584510388-Naruto.webp' },
                { name: 'Test User', profileImageUrl: null },
                { name: 'External User', profileImageUrl: 'https://ui-avatars.com/api/?name=EU&background=e74c3c&color=fff' },
                { name: 'Invalid URL User', profileImageUrl: 'invalid-url' }
            ];

            let html = '<p>Sample user avatars with real database URLs:</p>';

            sampleUsers.forEach(user => {
                const initials = getUserInitials(user);
                const color = getUserAvatarColor(user.name);
                const imageUrl = normalizeImageUrl(user.profileImageUrl);

                html += `
                    <div style="display: inline-block; margin: 10px; text-align: center; vertical-align: top;">
                        <div class="avatar">
                            ${imageUrl ?
                                `<img src="${imageUrl}" alt="${user.name}" onerror="this.style.display='none'; this.nextSibling.style.display='flex';">
                                 <div class="avatar-fallback" style="display:none; background-color: ${color}">${initials}</div>` :
                                `<div class="avatar-fallback" style="background-color: ${color}">${initials}</div>`
                            }
                        </div>
                        <div style="font-size: 12px; margin-top: 5px; max-width: 80px; word-wrap: break-word;">${user.name}</div>
                        <div style="font-size: 10px; color: #666; margin-top: 2px;">
                            Original: ${user.profileImageUrl || 'None'}<br>
                            Processed: ${imageUrl || 'None'}
                        </div>
                    </div>
                `;
            });

            sampleDiv.innerHTML = html;
        };
    </script>
</body>
</html>
