// Removed StrictMode to prevent duplicate API calls in development
import { createRoot } from 'react-dom/client'
import './index.css'
import App from './App.jsx';

// Update remaining imports to use contexts folder instead of context folder
// Here's a list of files that need to be updated:
// 1. frontend/Task-Manager/src/pages/Chat.jsx
// 2. frontend/Task-Manager/src/hooks/useMessageReadTracking.js
// 3. frontend/Task-Manager/src/components/chat/NotificationSettings.jsx
// 4. frontend/Task-Manager/src/components/chat/CreateGroupDialog.jsx
// 5. frontend/Task-Manager/src/components/chat/ConversationList.jsx
// 6. frontend/Task-Manager/src/components/notifications/NotificationDropdown.jsx
// 7. frontend/Task-Manager/src/components/chat/ChatWindow.jsx
// 8. frontend/Task-Manager/src/components/layout/SideMenu.jsx
// 9. frontend/Task-Manager/src/components/layout/DashboardLayout.jsx

createRoot(document.getElementById('root')).render(<App />);
