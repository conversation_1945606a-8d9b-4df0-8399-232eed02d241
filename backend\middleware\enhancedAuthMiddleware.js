/**
 * Enhanced Authentication & Authorization Middleware
 * Implements comprehensive security measures for authentication
 */

const jwt = require('jsonwebtoken');
const bcrypt = require('bcrypt');
const rateLimit = require('express-rate-limit');
const User = require('../models/User');

/**
 * Enhanced JWT token verification with additional security checks
 */
const enhancedProtect = async (req, res, next) => {
    console.log(`[DEBUG] enhancedProtect middleware hit for: ${req.method} ${req.originalUrl}`);
  try {
    let token;
    
    // Extract token from accessToken cookie first
    token = req.cookies.accessToken;

    // Fallback: allow Bearer token in Authorization header (helps local dev when cookies are blocked)
    if (!token && req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
      token = req.headers.authorization.split(' ')[1];
    }
    
    if (!token) {
      return res.status(401).json({
        success: false,
        message: 'Access denied. No token provided.'
      });
    }
    
    // Verify token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // Check if token is expired (additional check)
    const currentTime = Math.floor(Date.now() / 1000);
    if (decoded.exp && decoded.exp < currentTime) {
      return res.status(401).json({
        success: false,
        message: 'Token has expired'
      });
    }
    
    // Get user from database
    const user = await User.findById(decoded.id).select('-password');
    
    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'User not found'
      });
    }
    
    // Check if user account is active
    if (user.status === 'inactive' || user.status === 'suspended') {
      return res.status(401).json({
        success: false,
        message: 'Account is inactive or suspended'
      });
    }
    
    // Add security headers
    res.set({
      'X-Content-Type-Options': 'nosniff',
      'X-Frame-Options': 'DENY',
      'X-XSS-Protection': '1; mode=block'
    });
    
    // Store user info in request
    req.user = user;
    req.token = token;
    
    next();
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        success: false,
        message: 'Invalid token'
      });
    }
    
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        message: 'Token has expired'
      });
    }
    
    return res.status(500).json({
      success: false,
      message: 'Authentication error'
    });
  }
};

/**
 * Enhanced admin authorization with additional checks
 */
const enhancedAdminOnly = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({
      success: false,
      message: 'Authentication required'
    });
  }
  
  if (req.user.role !== 'admin') {
    return res.status(403).json({
      success: false,
      message: 'Admin access required'
    });
  }
  
  // Log admin actions for audit trail
  if (process.env.NODE_ENV === 'production') {
    console.log(`Admin action: ${req.method} ${req.path} by user ${req.user._id} at ${new Date().toISOString()}`);
  }
  
  next();
};

/**
 * Role-based access control middleware
 */
const requireRole = (roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }
    
    const userRoles = Array.isArray(req.user.role) ? req.user.role : [req.user.role];
    const requiredRoles = Array.isArray(roles) ? roles : [roles];
    
    const hasRequiredRole = requiredRoles.some(role => userRoles.includes(role));
    
    if (!hasRequiredRole) {
      return res.status(403).json({
        success: false,
        message: `Access denied. Required roles: ${requiredRoles.join(', ')}`
      });
    }
    
    next();
  };
};

/**
 * Resource ownership verification
 */
const requireOwnership = (resourceIdParam = 'id', userIdField = 'createdBy') => {
  return async (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          message: 'Authentication required'
        });
      }
      
      // Admin can access all resources
      if (req.user.role === 'admin') {
        return next();
      }
      
      const resourceId = req.params[resourceIdParam];
      
      if (!resourceId) {
        return res.status(400).json({
          success: false,
          message: 'Resource ID required'
        });
      }
      
      // This would need to be customized based on the resource type
      // For now, we'll check if the user ID matches
      if (resourceId === req.user._id.toString()) {
        return next();
      }
      
      return res.status(403).json({
        success: false,
        message: 'Access denied. You can only access your own resources.'
      });
    } catch (error) {
      return res.status(500).json({
        success: false,
        message: 'Authorization error'
      });
    }
  };
};

/**
 * Rate limiting for authentication endpoints
 */
const authRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // Limit each IP to 5 requests per windowMs
  message: {
    success: false,
    message: 'Too many authentication attempts. Please try again later.'
  },
  standardHeaders: true,
  legacyHeaders: false,
  // Skip successful requests
  skipSuccessfulRequests: true
});

/**
 * Rate limiting for password reset
 */
const passwordResetRateLimit = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 3, // Limit each IP to 3 password reset requests per hour
  message: {
    success: false,
    message: 'Too many password reset attempts. Please try again later.'
  }
});

/**
 * Session security middleware
 */
const sessionSecurity = (req, res, next) => {
  // Set secure session cookies
  if (req.session) {
    req.session.cookie.secure = process.env.NODE_ENV === 'production';
    req.session.cookie.httpOnly = true;
    req.session.cookie.maxAge = 24 * 60 * 60 * 1000; // 24 hours
    req.session.cookie.sameSite = 'strict';
  }
  
  next();
};

/**
 * Password strength validation
 */
const validatePasswordStrength = (password) => {
  const minLength = 8;
  const hasUpperCase = /[A-Z]/.test(password);
  const hasLowerCase = /[a-z]/.test(password);
  const hasNumbers = /\d/.test(password);
  const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);
  
  const errors = [];
  
  if (password.length < minLength) {
    errors.push(`Password must be at least ${minLength} characters long`);
  }
  
  if (!hasUpperCase) {
    errors.push('Password must contain at least one uppercase letter');
  }
  
  if (!hasLowerCase) {
    errors.push('Password must contain at least one lowercase letter');
  }
  
  if (!hasNumbers) {
    errors.push('Password must contain at least one number');
  }
  
  if (!hasSpecialChar) {
    errors.push('Password must contain at least one special character');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Secure password hashing
 */
const hashPassword = async (password) => {
  const saltRounds = 12; // Increased from default 10 for better security
  return await bcrypt.hash(password, saltRounds);
};

/**
 * Secure token generation
 */
const generateSecureToken = (userId, expiresIn = '24h') => {
  const payload = {
    id: userId,
    iat: Math.floor(Date.now() / 1000),
    // Add additional claims for security
    iss: 'taskmanager-app',
    aud: 'taskmanager-users'
  };
  
  return jwt.sign(payload, process.env.JWT_SECRET, { 
    expiresIn,
    algorithm: 'HS256' // Explicitly specify algorithm
  });
};

/**
 * Token blacklist middleware (for logout functionality)
 */
const tokenBlacklist = new Set();

const checkTokenBlacklist = (req, res, next) => {
  const token = req.token;
  
  if (token && tokenBlacklist.has(token)) {
    return res.status(401).json({
      success: false,
      message: 'Token has been revoked'
    });
  }
  
  next();
};

const addToBlacklist = (token) => {
  tokenBlacklist.add(token);
  
  // Clean up expired tokens periodically
  if (tokenBlacklist.size > 1000) {
    // In a production environment, you'd want to use Redis or a database
    // for token blacklisting instead of in-memory storage
    tokenBlacklist.clear();
  }
};

/**
 * Account lockout protection
 */
const accountLockout = {
  attempts: new Map(),
  
  recordFailedAttempt: (identifier) => {
    const current = accountLockout.attempts.get(identifier) || { count: 0, lockedUntil: null };
    current.count += 1;
    
    if (current.count >= 5) {
      current.lockedUntil = Date.now() + (30 * 60 * 1000); // Lock for 30 minutes
    }
    
    accountLockout.attempts.set(identifier, current);
  },
  
  isLocked: (identifier) => {
    const attempt = accountLockout.attempts.get(identifier);
    if (!attempt || !attempt.lockedUntil) return false;
    
    if (Date.now() > attempt.lockedUntil) {
      accountLockout.attempts.delete(identifier);
      return false;
    }
    
    return true;
  },
  
  clearAttempts: (identifier) => {
    accountLockout.attempts.delete(identifier);
  }
};

module.exports = {
  enhancedProtect,
  enhancedAdminOnly,
  requireRole,
  requireOwnership,
  authRateLimit,
  passwordResetRateLimit,
  sessionSecurity,
  validatePasswordStrength,
  hashPassword,
  generateSecureToken,
  checkTokenBlacklist,
  addToBlacklist,
  accountLockout
};
