import React, { useState, useRef } from 'react';
import { toast } from 'react-toastify';
import api from '../utils/api';
import { API_PATHS, BASE_URL } from '../utils/apiPaths';
import { LuFile, LuImage, LuTrash2, LuUpload, LuDownload, LuEdit, LuSave, LuX } from 'react-icons/lu';
import uploadFile from '../utils/uploadFile';

const AttachmentManager = ({ task, onTaskUpdate, onPreviewOpen, isEditing }) => {
  const [editingAttachmentId, setEditingAttachmentId] = useState(null);
  const [newName, setNewName] = useState('');
  const fileInputRef = useRef(null);

  const handleUpload = async (event) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    try {
      const uploadedFiles = await Promise.all(Array.from(files).map(file => uploadFile(file)));
      const newAttachments = uploadedFiles.map(uploadedFile => ({
        ...uploadedFile,
        originalName: uploadedFile.name,
        _id: `new_${Date.now()}_${Math.random()}` // Temporary unique ID
      }));

      const updatedTask = { ...task, attachments: [...task.attachments, ...newAttachments] };
      onTaskUpdate(updatedTask);
      toast.success(`${files.length} file(s) uploaded and ready to be saved.`);
    } catch (error) {
      console.error('Error uploading files:', error);
      toast.error('File upload failed.');
    }
  };

  const handleDelete = (attachmentId) => {
    const updatedAttachments = task.attachments.filter(att => att._id !== attachmentId);
    const updatedTask = { ...task, attachments: updatedAttachments };
    onTaskUpdate(updatedTask);
    toast.info('Attachment marked for deletion. Save changes to confirm.');
  };

  const handleRename = (attachment) => {
    setEditingAttachmentId(attachment._id);
    setNewName(attachment.originalName);
  };

  const handleSaveRename = (attachmentId) => {
    const updatedAttachments = task.attachments.map(att => 
      att._id === attachmentId ? { ...att, originalName: newName.trim() } : att
    );
    const updatedTask = { ...task, attachments: updatedAttachments };
    onTaskUpdate(updatedTask);
    setEditingAttachmentId(null);
    setNewName('');
    toast.success('Attachment renamed. Save changes to confirm.');
  };

  const getPreviewUrl = (attachment) => {
    if (attachment.url) return attachment.url; // For newly uploaded files
    if (!attachment.filePath) return null;
    // Replace backslashes with forward slashes for URL compatibility using shared BASE_URL
    return `${BASE_URL}/${attachment.filePath}`.replace(/\\/g, '/');
  };

  const isImageFile = (filename = '') => {
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];
    return imageExtensions.some(ext => filename.toLowerCase().endsWith(ext));
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h4 className="font-medium text-gray-900">Attachments</h4>
        {isEditing && (
          <button 
            onClick={() => fileInputRef.current?.click()}
            className="flex items-center gap-2 px-3 py-1.5 text-sm bg-blue-500 text-white rounded-md hover:bg-blue-600"
          >
            <LuUpload size={16} />
            <span>Upload</span>
          </button>
        )}
        <input type="file" ref={fileInputRef} onChange={handleUpload} multiple className="hidden" />
      </div>

      {task.attachments.length === 0 ? (
        <p className="text-gray-500 text-sm italic">No attachments found.</p>
      ) : (
        <div className="space-y-2">
          {task.attachments.map((attachment) => (
            <div key={attachment._id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-150">
              <div className="flex items-center space-x-3 flex-1 min-w-0">
                <div className="flex-shrink-0 w-10 h-10 rounded flex items-center justify-center bg-gray-200">
                  {isImageFile(attachment.originalName) ? <LuImage size={20} className="text-gray-600" /> : <LuFile size={20} className="text-gray-600" />}
                </div>
                <div className="flex-1 min-w-0">
                  {editingAttachmentId === attachment._id ? (
                    <div className="flex items-center gap-2">
                      <input type="text" value={newName} onChange={(e) => setNewName(e.target.value)} className="flex-1 px-2 py-1 border border-gray-300 rounded text-sm" autoFocus onKeyPress={(e) => e.key === 'Enter' && handleSaveRename(attachment._id)} />
                      <button onClick={() => handleSaveRename(attachment._id)} className="p-1.5 text-green-600 hover:text-green-800"><LuSave size={18} /></button>
                      <button onClick={() => setEditingAttachmentId(null)} className="p-1.5 text-red-600 hover:text-red-800"><LuX size={18} /></button>
                    </div>
                  ) : (
                    <div>
                      <p className="text-sm font-medium text-gray-900 truncate">{attachment.originalName}</p>
                      <p className="text-xs text-gray-500">{attachment.size ? `${(attachment.size / 1024).toFixed(1)} KB` : ''}</p>
                    </div>
                  )}
                </div>
              </div>
              <div className="flex items-center space-x-2 ml-4">
                <a href={getPreviewUrl(attachment)} target="_blank" rel="noopener noreferrer" className="p-1.5 text-gray-600 hover:text-blue-600"><LuDownload size={18} /></a>
                {isEditing && (
                  <>
                    <button onClick={() => handleRename(attachment)} className="p-1.5 text-gray-600 hover:text-blue-600"><LuEdit size={18} /></button>
                    <button onClick={() => handleDelete(attachment._id)} className="p-1.5 text-red-500 hover:text-red-700"><LuTrash2 size={18} /></button>
                  </>
                )}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default AttachmentManager;