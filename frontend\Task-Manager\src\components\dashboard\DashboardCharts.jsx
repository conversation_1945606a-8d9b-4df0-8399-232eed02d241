/**
 * Dashboard Charts Component
 * Displays task distribution and priority charts
 * Extracted from Dashboard.jsx for better maintainability
 */

import React from 'react';
import CustomPieChart from '../charts/CustomPieCharts';
import CustomBarChart from '../charts/CustomBarChart';

const COLORS = ["#8b5cf6", "#0051FF", "#7ED600"];

const DashboardCharts = ({ dashboardData, loading = false }) => {
  if (loading) {
    return (
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        {/* Chart skeletons */}
        {Array.from({ length: 2 }).map((_, index) => (
          <div
            key={index}
            className="bg-white p-6 rounded-lg shadow-sm border border-gray-200 animate-pulse"
          >
            <div className="h-6 bg-gray-200 rounded w-1/3 mb-4" />
            <div className="h-64 bg-gray-200 rounded" />
          </div>
        ))}
      </div>
    );
  }

  // Prepare task distribution data for pie chart
  const taskDistributionData = [
    {
      name: "Pending",
      value: dashboardData?.charts?.taskDistribution?.Pending || 0,
      color: COLORS[0]
    },
    {
      name: "In Progress",
      value: dashboardData?.charts?.taskDistribution?.InProgress || 
             dashboardData?.charts?.taskDistribution?.["In Progress"] || 0,
      color: COLORS[1]
    },
    {
      name: "Completed",
      value: dashboardData?.charts?.taskDistribution?.Completed || 0,
      color: COLORS[2]
    }
  ].filter(item => item.value > 0); // Only show non-zero values

  // Prepare priority data for bar chart
  const priorityData = [
    {
      name: "Low",
      value: dashboardData?.charts?.taskPriorityLevels?.Low || 0,
      color: "#10B981" // Green
    },
    {
      name: "Medium",
      value: dashboardData?.charts?.taskPriorityLevels?.Medium || 0,
      color: "#F59E0B" // Yellow
    },
    {
      name: "High",
      value: dashboardData?.charts?.taskPriorityLevels?.High || 0,
      color: "#EF4444" // Red
    }
  ];

  const hasTaskData = taskDistributionData.length > 0;
  const hasPriorityData = priorityData.some(item => item.value > 0);

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
      {/* Task Distribution Chart */}
      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">
          Task Distribution
        </h3>
        {hasTaskData ? (
          <div className="h-64">
            <CustomPieChart 
              data={taskDistributionData}
              colors={COLORS}
            />
          </div>
        ) : (
          <div className="h-64 flex items-center justify-center text-gray-500">
            <div className="text-center">
              <div className="text-4xl mb-2">📊</div>
              <p>No task data available</p>
            </div>
          </div>
        )}
      </div>

      {/* Task Priority Chart */}
      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">
          Task Priority Levels
        </h3>
        {hasPriorityData ? (
          <div className="h-64">
            <CustomBarChart 
              data={priorityData}
            />
          </div>
        ) : (
          <div className="h-64 flex items-center justify-center text-gray-500">
            <div className="text-center">
              <div className="text-4xl mb-2">📈</div>
              <p>No priority data available</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default DashboardCharts;
