# Stage 1: Build the React application
FROM node:22-alpine AS build

WORKDIR /app

# Copy package.json and install dependencies.
# We copy only package.json (not package-lock.json) to avoid issues with
# optional dependencies between different OSes (like Windows host and Linux container).
# This forces npm to generate a fresh lock file compatible with the container's OS.
COPY package.json ./
RUN npm install --cache /app/.npm

# Copy configuration files
COPY vite.config.js postcss.config.cjs tailwind.config.cjs eslint.config.js ./

# Copy the rest of the application source code
COPY public ./public
COPY src ./src
COPY index.html ./

# Build the application
# This command will only re-run if the source code or config files have changed
RUN npm run build

# Stage 2: Serve the static files with Nginx
FROM nginx:stable-alpine

# Copy the built static files from the build stage
COPY --from=build /app/dist /usr/share/nginx/html

# Copy the custom Nginx configuration for SPA routing
COPY nginx.conf /etc/nginx/conf.d/default.conf

# Expose port 80
EXPOSE 80

# Start Nginx in the foreground
CMD ["nginx", "-g", "daemon off;"]
