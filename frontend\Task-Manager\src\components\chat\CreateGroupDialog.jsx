import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>ch, LuX, <PERSON><PERSON><PERSON><PERSON>, LuPlus, <PERSON><PERSON>he<PERSON> } from "react-icons/lu";
import { useUser } from '../../contexts/userContext';
import UserAvatar from '../common/UserAvatar';
import userService from '../../services/userService';
import chatService from '../../services/chatService';
import { useSocket } from '../../contexts/SocketContext';

const CreateGroupDialog = ({ open, onClose, onGroupCreated }) => {
  const { user } = useUser();
  const { isUserOnline } = useSocket();
  
  const [groupName, setGroupName] = useState('');
  const [selectedUsers, setSelectedUsers] = useState([]);
  const [availableUsers, setAvailableUsers] = useState([]);
  const [filteredUsers, setFilteredUsers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [errors, setErrors] = useState({});
  const [creatingGroup, setCreatingGroup] = useState(false);

  useEffect(() => {
    if (open) {
      fetchUsers();
      // Reset states
      setGroupName('');
      setSelectedUsers([]);
      setSearchTerm('');
      setErrors({});
    }
  }, [open]);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      const response = await userService.getAllUsers();
      if (response.success) {
        // Filter out current user
        const otherUsers = response.users.filter(u => u._id !== user?._id);
        setAvailableUsers(otherUsers);
        setFilteredUsers(otherUsers);
      }
    } catch (error) {
      console.error('Error fetching users:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSearchChange = (e) => {
    const term = e.target.value;
    setSearchTerm(term);
    
    if (term.trim() === '') {
      setFilteredUsers(availableUsers);
    } else {
      setFilteredUsers(
        availableUsers.filter(user => 
          user.name.toLowerCase().includes(term.toLowerCase()) ||
          (user.email && user.email.toLowerCase().includes(term.toLowerCase()))
        )
      );
    }
  };

  const toggleUserSelection = (selectedUser) => {
    setSelectedUsers(prev => {
      const isAlreadySelected = prev.some(u => u._id === selectedUser._id);
      
      if (isAlreadySelected) {
        return prev.filter(u => u._id !== selectedUser._id);
      } else {
        return [...prev, selectedUser];
      }
    });
    
    // Clear errors when users are selected
    if (errors.users) {
      setErrors(prev => ({ ...prev, users: '' }));
    }
  };

  const handleCreateGroup = async () => {
    // Validate inputs
    const newErrors = {};
    if (!groupName.trim()) {
      newErrors.groupName = 'Group name is required';
    }
    if (selectedUsers.length < 2) {
      newErrors.users = 'Please select at least 2 users for a group chat';
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    try {
      setCreatingGroup(true);
      const response = await chatService.createGroupConversation(
        groupName,
        selectedUsers.map(user => user._id)
      );

      if (response.success) {
        if (onGroupCreated) {
          onGroupCreated(response.conversation);
        }
        handleClose();
      }
    } catch (error) {
      console.error('Error creating group:', error);
      setErrors({ submit: 'Failed to create group. Please try again.' });
    } finally {
      setCreatingGroup(false);
    }
  };

  const handleClose = () => {
    setGroupName('');
    setSelectedUsers([]);
    setSearchTerm('');
    setErrors({});
    onClose();
  };

  if (!open) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md max-h-[90vh] flex flex-col overflow-hidden animate-fadeIn">
        {/* Header */}
        <div className="flex justify-between items-center p-4 border-b">
          <h2 className="text-xl font-bold">Create New Group Chat</h2>
          <button 
            onClick={handleClose} 
            className="text-gray-500 hover:text-gray-700 focus:outline-none"
          >
            <LuX size={20} />
          </button>
        </div>

        {/* Group Name Input */}
        <div className="p-4 border-b">
          <div className="mb-1 font-medium text-gray-700">Group Name</div>
          <input
            type="text"
            placeholder="Enter group name"
            className={`w-full p-2 border rounded-md ${errors.groupName ? 'border-red-500' : 'border-gray-300'}`}
            value={groupName}
            onChange={(e) => {
              setGroupName(e.target.value);
              if (errors.groupName) setErrors({...errors, groupName: ''});
            }}
            disabled={creatingGroup}
          />
          {errors.groupName && (
            <p className="mt-1 text-red-500 text-sm">{errors.groupName}</p>
          )}
        </div>

        {/* Search Input */}
        <div className="p-4 border-b">
          <div className="relative">
            <input
              type="text"
              placeholder="Search users"
              className="w-full p-2 pl-10 border border-gray-300 rounded-md"
              value={searchTerm}
              onChange={handleSearchChange}
              disabled={creatingGroup}
            />
            <LuSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
          </div>
          {errors.users && (
            <p className="mt-1 text-red-500 text-sm">{errors.users}</p>
          )}
        </div>

        {/* Selected Users */}
        {selectedUsers.length > 0 && (
          <div className="px-4 py-2 border-b bg-gray-50">
            <div className="flex flex-wrap gap-2">
              {selectedUsers.map(user => (
                <div 
                  key={user._id}
                  className="flex items-center gap-1 bg-blue-100 text-blue-700 rounded-full pl-1 pr-2 py-1"
                >
                  <UserAvatar user={user} size={24} showStatus={false} />
                  <span className="text-sm">{user.name}</span>
                  <button 
                    onClick={() => toggleUserSelection(user)}
                    className="text-blue-700 hover:text-blue-900 ml-1 focus:outline-none"
                    disabled={creatingGroup}
                  >
                    <LuX size={16} />
                  </button>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* User List */}
        <div className="overflow-y-auto flex-1">
          {loading ? (
            <div className="flex justify-center items-center h-32">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            </div>
          ) : filteredUsers.length === 0 ? (
            <div className="p-4 text-center text-gray-500">
              {searchTerm ? 'No users found' : 'No users available'}
            </div>
          ) : (
            <ul className="divide-y divide-gray-200">
              {filteredUsers.map(user => {
                const isSelected = selectedUsers.some(u => u._id === user._id);
                return (
                  <li 
                    key={user._id}
                    className={`p-3 hover:bg-gray-50 cursor-pointer flex items-center justify-between ${isSelected ? 'bg-blue-50' : ''}`}
                    onClick={() => toggleUserSelection(user)}
                  >
                    <div className="flex items-center space-x-3">
                      <UserAvatar 
                        user={user}
                        showStatus
                        isOnline={isUserOnline(user._id)}
                        size={40}
                      />
                      <div>
                        <div className="font-medium">{user.name}</div>
                        <div className="text-sm text-gray-500">{user.email}</div>
                      </div>
                    </div>
                    {isSelected && (
                      <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white">
                        <LuCheck size={14} />
                      </div>
                    )}
                  </li>
                );
              })}
            </ul>
          )}
        </div>

        {/* Footer */}
        <div className="p-4 border-t flex justify-between">
          <button
            onClick={handleClose}
            className="px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-md focus:outline-none"
            disabled={creatingGroup}
          >
            Cancel
          </button>
          <button
            onClick={handleCreateGroup}
            disabled={creatingGroup || selectedUsers.length < 2 || !groupName.trim()}
            className={`px-4 py-2 rounded-md flex items-center ${
              creatingGroup || selectedUsers.length < 2 || !groupName.trim()
                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                : 'bg-blue-500 text-white hover:bg-blue-600'
            }`}
          >
            {creatingGroup ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2"></div>
                Creating...
              </>
            ) : (
              <>
                <LuPlus size={18} className="mr-1" />
                Create Group
              </>
            )}
          </button>
        </div>

        {/* Error Message */}
        {errors.submit && (
          <div className="p-4 bg-red-100 text-red-700 text-center">
            {errors.submit}
          </div>
        )}
      </div>
    </div>
  );
};

export default CreateGroupDialog; 