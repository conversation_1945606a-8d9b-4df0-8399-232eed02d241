import React from 'react';
import PropTypes from 'prop-types';

/**
 * Task status tabs component for filtering tasks by status
 * 
 * @param {Object} props - Component props
 * @param {Array} props.tabs - Array of tab objects with label and count
 * @param {string} props.activeTab - Currently active tab label
 * @param {Function} props.onTabChange - Callback when tab is changed
 * @returns {JSX.Element} Rendered component
 */
const TaskStatusTabs = ({ tabs, activeTab, onTabChange }) => {
  return (
    <div className="flex justify-center border-b border-gray-200">
      {tabs.map((tab) => (
        <button
          key={tab.label}
          className={`relative px-4 py-3 text-sm font-medium ${
            activeTab === tab.label
              ? 'text-blue-600 border-b-2 border-blue-600'
              : 'text-gray-500 hover:text-gray-700 hover:border-b-2 hover:border-gray-300'
          } cursor-pointer transition-colors`}
          onClick={() => onTabChange(tab.label)}
        >
          <div className='flex items-center'>
            <span>{tab.label}</span>
            <span
              className={`ml-2 px-2 py-0.5 rounded-full text-xs ${
                activeTab === tab.label
                  ? 'bg-blue-100 text-blue-800'
                  : 'bg-gray-100 text-gray-600'
              }`}
            >
              {tab.count}
            </span>
          </div>
        </button>
      ))}
    </div>
  );
};

TaskStatusTabs.propTypes = {
  tabs: PropTypes.arrayOf(
    PropTypes.shape({
      label: PropTypes.string.isRequired,
      count: PropTypes.number.isRequired
    })
  ).isRequired,
  activeTab: PropTypes.string.isRequired,
  onTabChange: PropTypes.func.isRequired
};

export default TaskStatusTabs;
