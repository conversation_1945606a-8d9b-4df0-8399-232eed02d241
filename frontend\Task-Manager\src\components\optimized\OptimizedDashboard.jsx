/**
 * Optimized Dashboard Component
 * Uses React.memo, useMemo, and lazy loading for better performance
 */

import React, { memo, useMemo, Suspense, lazy } from 'react';
import { ErrorBoundary } from 'react-error-boundary';
import DashboardLayout from '../layout/DashboardLayout';
import LoadingSkeleton from '../common/LoadingSkeleton';
import { ComponentErrorFallback } from '../common/ErrorFallback';
import useDashboardData from '../../hooks/useDashboardData';
import { useUser } from '../../contexts/userContext';

// Lazy load heavy components
const DashboardStats = lazy(() => import('../dashboard/DashboardStats'));
const DashboardCharts = lazy(() => import('../dashboard/DashboardCharts'));
const RecentTasksSection = lazy(() => import('../dashboard/RecentTasksSection'));

const OptimizedDashboard = memo(() => {
  const { user } = useUser();
  const { 
    dashboardData, 
    loading, 
    error, 
    statistics,
    refreshData 
  } = useDashboardData(user?.role);

  // Memoize welcome message to prevent unnecessary recalculations
  const welcomeMessage = useMemo(() => {
    const currentHour = new Date().getHours();
    let greeting = 'Good morning';
    
    if (currentHour >= 12 && currentHour < 17) {
      greeting = 'Good afternoon';
    } else if (currentHour >= 17) {
      greeting = 'Good evening';
    }
    
    return `${greeting}, ${user?.name || 'User'}!`;
  }, [user?.name]);

  // Memoize dashboard sections to prevent unnecessary re-renders
  const dashboardSections = useMemo(() => {
    if (loading) {
      return (
        <div className="space-y-6">
          <LoadingSkeleton variant="stats" count={4} />
          <LoadingSkeleton variant="chart" count={2} />
          <LoadingSkeleton variant="table" count={5} />
        </div>
      );
    }

    if (error) {
      return (
        <div className="text-center py-12">
          <div className="text-red-500 text-lg mb-4">Failed to load dashboard data</div>
          <button
            onClick={refreshData}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Retry
          </button>
        </div>
      );
    }

    return (
      <>
        {/* Statistics Cards */}
        <ErrorBoundary
          FallbackComponent={ComponentErrorFallback}
          onReset={refreshData}
        >
          <Suspense fallback={<LoadingSkeleton variant="stats" count={4} />}>
            <DashboardStats 
              dashboardData={dashboardData}
              loading={loading}
            />
          </Suspense>
        </ErrorBoundary>

        {/* Charts Section */}
        <ErrorBoundary
          FallbackComponent={ComponentErrorFallback}
          onReset={refreshData}
        >
          <Suspense fallback={<LoadingSkeleton variant="chart" count={2} />}>
            <DashboardCharts 
              dashboardData={dashboardData}
              loading={loading}
            />
          </Suspense>
        </ErrorBoundary>

        {/* Recent Tasks */}
        <ErrorBoundary
          FallbackComponent={ComponentErrorFallback}
          onReset={refreshData}
        >
          <Suspense fallback={<LoadingSkeleton variant="table" count={5} />}>
            <RecentTasksSection 
              dashboardData={dashboardData}
              loading={loading}
              onTaskDeleted={refreshData}
              onSelectionChange={() => {}} // No-op for dashboard view
            />
          </Suspense>
        </ErrorBoundary>
      </>
    );
  }, [dashboardData, loading, error, refreshData]);

  // Memoize quick stats for header
  const quickStats = useMemo(() => {
    if (loading || !statistics) return null;

    return (
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
        <div>
          <div className="text-2xl font-bold text-blue-600">{statistics.totalTasks}</div>
          <div className="text-sm text-gray-600">Total Tasks</div>
        </div>
        <div>
          <div className="text-2xl font-bold text-green-600">{statistics.completionRate}%</div>
          <div className="text-sm text-gray-600">Completion Rate</div>
        </div>
        <div>
          <div className="text-2xl font-bold text-amber-600">{statistics.pendingPercentage}%</div>
          <div className="text-sm text-gray-600">Pending</div>
        </div>
        <div>
          <div className="text-2xl font-bold text-indigo-600">{statistics.inProgressPercentage}%</div>
          <div className="text-sm text-gray-600">In Progress</div>
        </div>
      </div>
    );
  }, [statistics, loading]);

  return (
    <DashboardLayout activeMenu="Dashboard">
      <div className="space-y-6">
        {/* Welcome Section */}
        <div className="bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl p-6 text-white">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold mb-2">{welcomeMessage}</h1>
              <p className="text-blue-100">
                Here's what's happening with your tasks today.
              </p>
            </div>
            <div className="hidden md:block">
              {quickStats}
            </div>
          </div>
        </div>

        {/* Mobile Quick Stats */}
        <div className="md:hidden bg-white rounded-lg p-4 shadow-sm border border-gray-200">
          {quickStats}
        </div>

        {/* Dashboard Content */}
        {dashboardSections}
      </div>
    </DashboardLayout>
  );
});

// Set display name for debugging
OptimizedDashboard.displayName = 'OptimizedDashboard';

export default OptimizedDashboard;
