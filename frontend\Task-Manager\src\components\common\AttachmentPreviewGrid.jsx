import React, { useState } from "react";
import ProxyImage from "./ProxyImage";
import { getAttachmentPreviewUrl } from "../../utils/attachmentUtils";
import { FaFileAlt } from "react-icons/fa";

const AttachmentPreviewGrid = ({
  attachments = [],
  onRemove,
  onRename,
  showRemove = false,
  showRename = false,
  gridCols = "grid-cols-2 sm:grid-cols-3 md:grid-cols-4",
  style = {},
}) => {
  const [selectedAttachment, setSelectedAttachment] = useState(null);

  return (
    <div>
      <div className={`grid ${gridCols} gap-4`} style={style}>
        {attachments.map((attachment, index) => {
          const src = attachment.previewUrl || getAttachmentPreviewUrl(attachment);
          const isImage = (attachment.mimeType && attachment.mimeType.startsWith('image/')) || (attachment.type && attachment.type.startsWith('image/')) || /\.(jpg|jpeg|png|gif|webp)$/i.test(attachment.url || '');
          const isPdf = (attachment.mimeType === 'application/pdf') || (attachment.type === 'application/pdf') || /\.pdf$/i.test(attachment.url || '');

          return (
            <div key={attachment.id || attachment._id || index} className="relative">
              <button
                type="button"
                onClick={() => setSelectedAttachment({ ...attachment, previewUrl: src, isImage, isPdf })}
                className="group aspect-square bg-gray-100 dark:bg-gray-700 rounded-lg overflow-hidden transition-transform transform hover:scale-105 focus:outline-none w-full h-full"
                title={attachment.name}
              >
                {isImage && src ? (
                  <ProxyImage
                    src={src}
                    alt={attachment.name}
                    className="w-full h-full object-cover"
                    containerClassName="w-full h-full"
                  />
                ) : isPdf && src ? (
                  <iframe
                    src={src}
                    title={attachment.name}
                    className="w-full h-full object-cover bg-white"
                    style={{ minHeight: 0, minWidth: 0, border: 'none' }}
                  />
                ) : (
                  <div className="w-full h-full flex flex-col items-center justify-center p-2">
                    <FaFileAlt className="text-4xl text-gray-400 dark:text-gray-500 mb-2" />
                    <span className="text-xs text-center text-gray-600 dark:text-gray-300 truncate">
                      {attachment.name}
                    </span>
                  </div>
                )}
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-colors" />
              </button>
              {showRemove && (
                <button
                  type="button"
                  onClick={() => onRemove?.(attachment)}
                  className="absolute top-1 right-1 bg-white bg-opacity-80 rounded-full p-1 text-red-500 hover:bg-opacity-100 z-10"
                  title="Remove"
                >
                  ×
                </button>
              )}
              {showRename && (
                <button
                  type="button"
                  onClick={() => onRename?.(attachment)}
                  className="absolute bottom-1 right-1 bg-white bg-opacity-80 rounded-full p-1 text-blue-500 hover:bg-opacity-100 z-10"
                  title="Rename"
                >
                  ✎
                </button>
              )}
            </div>
          );
        })}
      </div>
      {/* Large Preview Modal */}
      {selectedAttachment && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-60">
          <div className="bg-white dark:bg-gray-900 rounded-lg shadow-lg max-w-full max-h-full p-4 relative flex flex-col items-center">
            <button
              className="absolute top-2 right-2 text-gray-700 dark:text-gray-200 bg-gray-200 dark:bg-gray-700 rounded-full p-1 hover:bg-gray-300 dark:hover:bg-gray-600 transition"
              onClick={() => setSelectedAttachment(null)}
              aria-label="Close"
            >
              ✕
            </button>
            <div className="max-w-[80vw] max-h-[80vh] flex flex-col items-center justify-center">
              {selectedAttachment.isImage ? (
                <img
                  src={selectedAttachment.previewUrl}
                  alt={selectedAttachment.name}
                  className="max-w-full max-h-[70vh] rounded"
                />
              ) : selectedAttachment.isPdf ? (
                <iframe
                  src={selectedAttachment.previewUrl}
                  title={selectedAttachment.name}
                  className="w-[70vw] h-[70vh] rounded border bg-white"
                  style={{ background: '#fff' }}
                />
              ) : (
                <div className="flex flex-col items-center">
                  <FaFileAlt className="text-6xl text-gray-400 dark:text-gray-500 mb-4" />
                  <span className="text-lg font-semibold mb-2">{selectedAttachment.name}</span>
                  <a
                    href={selectedAttachment.previewUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 underline"
                  >
                    Open or Download
                  </a>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AttachmentPreviewGrid;
