/**
 * Enhanced Security middleware configuration for Express app
 */
const helmet = require('helmet');

/**
 * Sets up comprehensive security middleware for the Express app
 * @param {object} app - Express application instance
 */
const setupSecurityMiddleware = (app) => {
  const isDevelopment = process.env.NODE_ENV === 'development';
  const isProduction = process.env.NODE_ENV === 'production';

  // Enhanced Helmet configuration
  app.use(helmet({
    // Content Security Policy
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
        fontSrc: ["'self'", "https://fonts.gstatic.com"],
        imgSrc: ["'self'", "data:", "https:", "blob:"],
        scriptSrc: isDevelopment
          ? ["'self'", "'unsafe-eval'", "'unsafe-inline'"]
          : ["'self'"],
        connectSrc: ["'self'"],
        frameSrc: ["'none'"],
        objectSrc: ["'none'"],
        mediaSrc: ["'self'"],
        manifestSrc: ["'self'"],
        workerSrc: ["'self'", "blob:"]
      }
    },

    // Cross-Origin Embedder Policy
    crossOriginEmbedderPolicy: isProduction,

    // Cross-Origin Resource Policy
    crossOriginResourcePolicy: { policy: "cross-origin" },

    // DNS Prefetch Control
    dnsPrefetchControl: { allow: false },

    // Frame Options
    frameguard: { action: 'deny' },

    // Hide Powered By
    hidePoweredBy: true,

    // HSTS (HTTP Strict Transport Security)
    hsts: {
      maxAge: 31536000, // 1 year
      includeSubDomains: true,
      preload: true
    },

    // IE No Open
    ieNoOpen: true,

    // No Sniff
    noSniff: true,

    // Origin Agent Cluster
    originAgentCluster: true,

    // Permitted Cross Domain Policies
    permittedCrossDomainPolicies: false,

    // Referrer Policy
    referrerPolicy: { policy: "no-referrer" },

    // XSS Filter
    xssFilter: true
  }));

  // Additional security headers
  app.use((req, res, next) => {
    // Security headers
    res.setHeader('X-Content-Type-Options', 'nosniff');
    res.setHeader('X-Frame-Options', 'DENY');
    res.setHeader('X-XSS-Protection', '1; mode=block');
    res.setHeader('Referrer-Policy', 'no-referrer');
    res.setHeader('Permissions-Policy', 'geolocation=(), microphone=(), camera=()');

    // Remove server information
    res.removeHeader('X-Powered-By');
    res.removeHeader('Server');

    // Cache control for sensitive endpoints
    if (req.path.includes('/api/auth') || req.path.includes('/api/admin')) {
      res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
      res.setHeader('Pragma', 'no-cache');
      res.setHeader('Expires', '0');
    }

    next();
  });
};

/**
 * HTTPS redirect middleware for production
 */
const httpsRedirect = (req, res, next) => {
  if (process.env.NODE_ENV === 'production' && !req.secure && req.get('x-forwarded-proto') !== 'https') {
    return res.redirect(301, `https://${req.get('host')}${req.url}`);
  }
  next();
};

/**
 * Security logging middleware
 */
const securityLogger = (req, res, next) => {
  // Log suspicious activities
  const suspiciousPatterns = [
    /\.\./,
    /\/etc\/passwd/,
    /\/proc\/self\/environ/,
    /<script/i,
    /javascript:/i,
    /vbscript:/i,
    /\$\w+/,
    /union.*select/i,
    /drop.*table/i
  ];

  const requestData = JSON.stringify({
    url: req.url,
    body: req.body,
    query: req.query,
    headers: req.headers
  });

  for (const pattern of suspiciousPatterns) {
    if (pattern.test(requestData)) {
      console.warn(`Suspicious request detected from ${req.ip}: ${req.method} ${req.url}`);
      break;
    }
  }

  next();
};

module.exports = {
  setupSecurityMiddleware,
  httpsRedirect,
  securityLogger
};