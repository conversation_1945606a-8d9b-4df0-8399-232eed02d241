import { UPLOADS_URL } from './apiPaths';

export const getAttachmentPreviewUrl = (attachment) => {
  if (!attachment || !attachment.url) return null;

  // If already a full URL, return as is
  if (attachment.url.startsWith('http')) {
    return attachment.url;
  }

  // If already starts with /uploads/, just prepend BASE_URL (not UPLOADS_URL)
  if (attachment.url.startsWith('/uploads/')) {
    // Remove trailing slash from BASE_URL if present
    const base = (UPLOADS_URL.endsWith('/uploads')) ? UPLOADS_URL.slice(0, -8) : UPLOADS_URL;
    return `${base}${attachment.url}`;
  }

  // Otherwise, ensure single slash between UPLOADS_URL and filename
  return `${UPLOADS_URL.replace(/\/$/, '')}/${attachment.url.replace(/^\//, '')}`;
};
