import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useSearchParams } from 'react-router-dom';
import DashboardLayout from '../components/layout/DashboardLayout';
import ConversationList from '../components/chat/ConversationList';
import ChatWindow from '../components/chat/ChatWindow';
import UsersList from '../components/chat/UsersList';


import { useSocket } from '../contexts/SocketContext';
import chatService from '../services/chatService';
import { useUser } from '../contexts/userContext';
import { useNotifications } from '../contexts/NotificationContext';
import '../styles/chat.css';
import { toast } from 'react-hot-toast';

const Chat = () => {
  const { user } = useUser();
  const { notifications, markAsRead: markNotificationAsRead, markMultipleAsRead } = useNotifications();
  const {
    socket,
    connected,
    joinConversation,
    reconnect,
    markMessageAsDelivered,
    incrementUnreadChatCount,
    resetUnreadChatCount,
    resetConversationUnreadCount
  } = useSocket();
  const [searchParams] = useSearchParams();
  
  const [conversations, setConversations] = useState([]);
  const [selectedConversation, setSelectedConversation] = useState(null);
  const [messages, setMessages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [messageLoading, setMessageLoading] = useState(false);
  const [showUsersList, setShowUsersList] = useState(false);
  const joinedConversations = useRef(new Set());

  // Track pending messages to help with replacement logic
  const pendingMessages = useRef(new Map());
  
  // Fetch conversations on component mount
  useEffect(() => {
    if (user && user._id) {
      fetchConversations();
    } else {
      // If user is not loaded, set loading to false to show empty state
      setLoading(false);
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user]);

  // Handle URL parameters separately to avoid dependency issues
  const processedUserIds = useRef(new Set());
  const processedConversationIds = useRef(new Set());

  useEffect(() => {
    const userId = searchParams.get('userId');
    const conversationId = searchParams.get('conversationId');

    // Handle userId parameter (start new conversation)
    if (userId && user && !processedUserIds.current.has(userId)) {
      processedUserIds.current.add(userId);
      handleStartConversation(userId);

      // Clear the URL parameter after processing
      const newSearchParams = new URLSearchParams(searchParams);
      newSearchParams.delete('userId');
      window.history.replaceState({}, '', `/chat${newSearchParams.toString() ? '?' + newSearchParams.toString() : ''}`);
    }

    // Handle conversationId parameter (open existing conversation)
    if (conversationId && user && conversations.length > 0 && !processedConversationIds.current.has(conversationId)) {
      processedConversationIds.current.add(conversationId);
      const conversation = conversations.find(conv => conv._id === conversationId);
      if (conversation) {
        handleSelectConversation(conversation);
        // Clear the URL parameter only after successfully finding and selecting the conversation
        const newSearchParams = new URLSearchParams(searchParams);
        newSearchParams.delete('conversationId');
        window.history.replaceState({}, '', `/chat${newSearchParams.toString() ? '?' + newSearchParams.toString() : ''}`);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user, searchParams, conversations]);

  // Socket connection management
  useEffect(() => {
    if (!connected && reconnect && user) {
      reconnect();
    }
  }, [connected, reconnect, user]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      processedUserIds.current.clear();
      processedConversationIds.current.clear();
      joinedConversations.current.clear();
      pendingMessages.current.clear();
    };
  }, []);
  
  // Fetch conversations function
  const fetchConversations = useCallback(async (forceRefresh = false) => {
    if (!user || !user._id) {
      return;
    }

    try {
      setLoading(true);
      const response = await chatService.getConversations(forceRefresh);

      if (response.success) {
        // Filter out invalid conversations
        const validConversations = response.conversations.filter(conv => {
          // For individual chats, make sure there's another participant
          if (conv.type === 'individual') {
            const otherParticipant = conv.participants?.find(p => p && p._id !== user?._id);
            return !!otherParticipant; // Keep only if there is another participant
          }
          return true; // Keep all group conversations
        });

        setConversations(validConversations);
      } else {
        toast.error(response.error || 'Failed to load conversations');
      }
    } catch (error) {
      toast.error(error.response?.data?.message || 'Failed to load conversations');
    } finally {
      setLoading(false);
    }
  }, [user]);

  // Remove duplicate useEffect - fetchConversations is already called in the first useEffect

  // Safety timeout to reset loading state if it gets stuck
  useEffect(() => {
    const timeout = setTimeout(() => {
      if (loading) {
        setLoading(false);
      }
    }, 10000); // 10 second timeout

    return () => clearTimeout(timeout);
  }, [loading]);

  // Add a refresh function that can be called to reload conversations
  const refreshConversations = useCallback(() => {
    fetchConversations(true); // Force refresh to bypass cache
    toast.success('Conversations refreshed!');
  }, [fetchConversations]);

  // Join all conversation rooms when socket connects and conversations are loaded
  useEffect(() => {
    if (connected && socket && conversations.length > 0) {
      conversations.forEach(conversation => {
        if (!joinedConversations.current.has(conversation._id)) {
          joinedConversations.current.add(conversation._id);
          joinConversation(conversation._id);
        }
      });
    }
  }, [connected, socket, conversations.length, joinConversation]);
  
  // Listen for new messages from socket
  useEffect(() => {
    if (!socket) {
      return;
    }

    const handleNewMessage = (data) => {
      // Handle both old format (direct message) and new format ({ message, conversation })
      const message = data.message || data;

      // Add new message to state if it belongs to the current conversation
      if (selectedConversation && message.conversationId === selectedConversation._id) {
        setMessages(prevMessages => {
          // Check if message already exists in the array (by _id)
          const exists = prevMessages.some(msg => msg._id === message._id);
          if (exists) {
            return prevMessages;
          }

          // For messages from current user, try to replace temp message
          if (message.sender._id === user?._id) {
            // Method 1: Try tempId matching first
            if (message.tempId) {
              const tempIndex = prevMessages.findIndex(msg => msg.tempId === message.tempId);
              if (tempIndex !== -1) {
                pendingMessages.current.delete(message.tempId);
                const newMessages = [...prevMessages];
                newMessages[tempIndex] = { ...message, tempId: undefined };
                return newMessages;
              }
            }

            // Method 2: Find most recent temp message with same text
            const tempMessageIndex = prevMessages.findIndex(msg =>
              msg.sending === true &&
              msg.sender._id === user._id &&
              msg.text === message.text &&
              msg.conversationId === message.conversationId
            );

            if (tempMessageIndex !== -1) {
              const tempMsg = prevMessages[tempMessageIndex];
              if (tempMsg.tempId) {
                pendingMessages.current.delete(tempMsg.tempId);
              }
              const newMessages = [...prevMessages];
              newMessages[tempMessageIndex] = { ...message, tempId: undefined };
              return newMessages;
            } else {
              // Check if we have any sending messages from this user - if so, replace the most recent one
              const lastSendingIndex = prevMessages.map((msg, index) => ({ msg, index }))
                .reverse()
                .find(({ msg }) => msg.sending && msg.sender._id === user._id)?.index;

              if (lastSendingIndex !== undefined) {
                const newMessages = [...prevMessages];
                newMessages[lastSendingIndex] = { ...message, tempId: undefined };
                return newMessages;
              } else {
                return prevMessages;
              }
            }
          }

          // If message is from someone else, add as new message
          return [...prevMessages, message];
        });

        // Auto-mark message as delivered if it's from someone else
        if (message.sender._id !== user?._id && markMessageAsDelivered) {
          markMessageAsDelivered(message._id, message.conversationId);
        }
      } else if (message.sender._id !== user?._id) {
        // If message is not for current conversation and from someone else,
        // increment the unread count
        incrementUnreadChatCount();
      }

      // Update conversation list to show latest message
      setConversations(prevConversations => {
        return prevConversations.map(conv => {
          if (conv._id === message.conversationId) {
            return {
              ...conv,
              lastMessage: message,
              unreadCount: conv._id === selectedConversation?._id
                ? 0
                : (conv.unreadCount || 0) + 1
            };
          }
          return conv;
        });
      });
    };
    
    const handleMessageRead = ({ messageId, userId }) => {
      // Update read status for messages
      setMessages(prevMessages =>
        prevMessages.map(msg =>
          msg._id === messageId
            ? { ...msg, readBy: [...(msg.readBy || []), { user: userId }] }
            : msg
        )
      );
    };

    const handleMessageDelivered = ({ messageId, userId }) => {
      // Update delivery status for messages
      setMessages(prevMessages =>
        prevMessages.map(msg =>
          msg._id === messageId
            ? { ...msg, deliveredTo: [...(msg.deliveredTo || []), { user: userId }] }
            : msg
        )
      );
    };

    const handleMessageError = ({ error: errorMessage }) => {
      toast.error(errorMessage || 'Failed to send message');
      // Remove any pending messages
      setMessages(prev => prev.filter(msg => !msg.sending));
    };

    socket.on('newMessage', handleNewMessage);
    socket.on('messageRead', handleMessageRead);
    socket.on('messageDelivered', handleMessageDelivered);
    socket.on('messageError', handleMessageError);

    return () => {
      socket.off('newMessage', handleNewMessage);
      socket.off('messageRead', handleMessageRead);
      socket.off('messageDelivered', handleMessageDelivered);
      socket.off('messageError', handleMessageError);
    };
  }, [socket, selectedConversation, connected, user, markMessageAsDelivered, incrementUnreadChatCount]);
  
  // Join conversation room when selecting a conversation
  useEffect(() => {
    if (!selectedConversation || !user) return;
    
    const fetchMessages = async () => {
      try {
        setMessageLoading(true);
        
        // Try to join conversation if socket is connected
        if (connected && socket && !joinedConversations.current.has(selectedConversation._id)) {
          joinedConversations.current.add(selectedConversation._id);
          joinConversation(selectedConversation._id);
        }
        
        const response = await chatService.getMessages(selectedConversation._id);
        if (response.success) {
          setMessages(response.messages || []);
        }
        
        // Update unread count to zero for selected conversation
        setConversations(prevConversations => {
          return prevConversations.map(conv => {
            if (conv._id === selectedConversation._id) {
              return { ...conv, unreadCount: 0 };
            }
            return conv;
          });
        });

        // Mark related chat notifications as read when messages are fetched
        const relatedNotifications = notifications.filter(
          n => n.type === 'chat_message' &&
               n.conversationId === selectedConversation._id &&
               !n.isRead
        );

        const notificationIds = relatedNotifications.map(n => n._id);
        markMultipleAsRead(notificationIds);
      } catch (error) {
        toast.error(error.response?.data?.message || 'Failed to load messages');
      } finally {
        setMessageLoading(false);
      }
    };
    
    fetchMessages();
  }, [selectedConversation, connected, joinConversation, socket, user]);
  
  // Handle selecting a conversation
  const handleSelectConversation = useCallback((conversation) => {
    setSelectedConversation(conversation);
    setShowUsersList(false);

    // Reset unread count for this conversation when it's selected
    if (conversation?._id) {
      resetConversationUnreadCount(conversation._id);

      // Mark related chat notifications as read
      const relatedNotifications = notifications.filter(
        n => n.type === 'chat_message' &&
             n.conversationId === conversation._id &&
             !n.isRead
      );

      if (relatedNotifications.length > 0) {
        // Use batch function to mark all notifications as read at once
        const notificationIds = relatedNotifications.map(n => n._id);
        markMultipleAsRead(notificationIds);
      }
    }
  }, [resetConversationUnreadCount, notifications, markNotificationAsRead]);
  
  // Track ongoing conversation creation to prevent duplicates
  const creatingConversations = useRef(new Set());

  // Handle starting a new conversation with a user
  const handleStartConversation = useCallback(async (userId) => {
    if (!userId || !user) {
      toast.error('Unable to start conversation. Please try again.');
      return;
    }

    // Prevent starting conversation with self
    if (userId === user._id) {
      toast.error('Cannot start a conversation with yourself');
      return;
    }

    // Prevent duplicate requests for the same user
    if (creatingConversations.current.has(userId)) {
      return;
    }

    // Check if conversation already exists
    const existingConversation = conversations.find(conv =>
      conv.type === 'individual' &&
      conv.participants?.some(p => p._id === userId)
    );

    if (existingConversation) {
      setSelectedConversation(existingConversation);
      setShowUsersList(false);

      // Reset unread count for this conversation
      if (existingConversation._id) {
        resetConversationUnreadCount(existingConversation._id);
      }

      return;
    }

    try {
      creatingConversations.current.add(userId);
      const response = await chatService.getOrCreateIndividualConversation(userId);

      if (response.success && response.conversation) {
        // Validate conversation has proper participants
        const otherParticipant = response.conversation.participants?.find(p => p && p._id !== user._id);
        if (!otherParticipant) {
          toast.error('Failed to create conversation - invalid participants');
          return;
        }

        // Check if conversation already exists in the list
        const existingConvIndex = conversations.findIndex(
          conv => conv._id === response.conversation._id
        );

        if (existingConvIndex >= 0) {
          // Update existing conversation in list
          setConversations(prevConversations => {
            const updatedConversations = [...prevConversations];
            updatedConversations[existingConvIndex] = response.conversation;
            return updatedConversations;
          });
        } else {
          // Add new conversation to list
          setConversations(prevConversations => [
            response.conversation,
            ...prevConversations
          ]);
        }

        // Select the conversation
        setSelectedConversation(response.conversation);
        setShowUsersList(false);

        // Reset unread count for this new conversation
        if (response.conversation._id) {
          resetConversationUnreadCount(response.conversation._id);
        }

        // Conversation started successfully - no toast needed
      } else {
        const errorMessage = response.message || 'Failed to start conversation';
        toast.error(errorMessage);
      }
    } catch (error) {
      const errorMessage = error.response?.data?.message || error.message || 'Failed to start conversation. Please try again.';
      toast.error(errorMessage);
    } finally {
      // Always remove from creating set
      creatingConversations.current.delete(userId);
    }
  }, [conversations, user]);
  
  // Handle sending a new message
  const handleSendMessage = useCallback(async (text, attachments = [], taskReference = null) => {
    if (!selectedConversation) return;
    
    // If text is empty but we have a selected conversation, this is a signal to refresh messages
    if (!text.trim() && selectedConversation._id) {
      try {
        setMessageLoading(true);
        const response = await chatService.getMessages(selectedConversation._id);
        if (response.success) {
          setMessages(response.messages || []);
          // Also refresh conversation list
          fetchConversations();
        }
      } catch (error) {
        console.error('Error refreshing messages:', error);
      } finally {
        setMessageLoading(false);
      }
      return;
    }
    
    const messageData = {
      text: text.trim(),
      attachments: attachments || [],
      taskReference: taskReference || null
    };
    
    try {
      // Create temporary message for UI
      const tempId = `temp-${Date.now()}-${Math.random().toString(36).substring(2, 10)}`;
      const tempMessage = {
        _id: tempId,
        tempId, // Add this field to track the temporary message
        conversationId: selectedConversation._id,
        sender: {
          _id: user._id,
          name: user.name,
          profileImageUrl: user.profileImageUrl
        },
        text: messageData.text,
        attachments: messageData.attachments,
        taskReference: messageData.taskReference,
        createdAt: new Date().toISOString(),
        readBy: [{ user: user._id }],
        sending: true
      };

      // Track this pending message
      pendingMessages.current.set(tempId, {
        tempMessage,
        timestamp: Date.now(),
        text: messageData.text
      });




      // Add to UI immediately
      setMessages(prev => {

        return [...prev, tempMessage];
      });

      // Set a timeout to remove temp message if it's not replaced within 10 seconds
      setTimeout(() => {
        setMessages(prev => {
          const stillExists = prev.find(msg => msg._id === tempId && msg.sending);
          if (stillExists) {
            toast.error('Message failed to send');
            pendingMessages.current.delete(tempId);
            return prev.filter(msg => msg._id !== tempId);
          }
          return prev;
        });
      }, 10000);

      // Send message via socket if connected, otherwise use HTTP
      if (connected && socket) {
        // Send via socket for real-time delivery
        socket.emit('sendMessage', {
          conversationId: selectedConversation._id,
          tempId, // Send tempId with the socket message for tracking
          ...messageData
        });

        // Note: The real message will come back via the 'newMessage' socket event
        // and will replace the temporary message using the tempId
      } else {
        // Fallback to HTTP API
        const response = await chatService.sendMessage(
          selectedConversation._id,
          messageData
        );

        if (response.success) {
          // Replace temp message with real one
          setMessages(prev =>
            prev.map(msg =>
              msg._id === tempMessage._id ? response.message : msg
            )
          );

          // Update the conversation list to reflect the new message
          setConversations(prevConversations => {
            return prevConversations.map(conv => {
              if (conv._id === selectedConversation._id) {
                return {
                  ...conv,
                  lastMessage: response.message,
                  updatedAt: response.message.createdAt
                };
              }
              return conv;
            });
          });
        } else {
          // Remove temp message on failure
          setMessages(prev => prev.filter(msg => msg._id !== tempMessage._id));
          toast.error('Failed to send message');
        }
      }
    } catch (error) {
      toast.error(error.response?.data?.message || 'Error sending message');
    }
  }, [selectedConversation, user, connected, socket]);
  
  // Handle newly created group conversations
  const handleGroupCreated = useCallback((conversation) => {
    // Add the new group to the conversations list and select it
    setConversations(prev => [conversation, ...prev]);
    setSelectedConversation(conversation);
    setShowUsersList(false);

    // Reset unread count for this new group conversation
    if (conversation._id) {
      resetConversationUnreadCount(conversation._id);
    }

    toast.success('Group created successfully');
  }, [resetConversationUnreadCount]);

  // Handle conversation cleared
  const handleConversationCleared = useCallback((conversationId) => {
    // Clear messages if the cleared conversation is currently selected
    if (selectedConversation?._id === conversationId) {
      setMessages([]);
      setSelectedConversation(null);
    }

    // Update the conversation in the list to remove lastMessage
    setConversations(prev =>
      prev.map(conv =>
        conv._id === conversationId
          ? { ...conv, lastMessage: null, updatedAt: new Date() }
          : conv
      )
    );
  }, [selectedConversation]);
  
  // Handle new chat button click
  const handleNewChat = useCallback(() => {
    setShowUsersList(true);
    setSelectedConversation(null);
  }, []);
  
  // When the component mounts, reset the unread count
  useEffect(() => {
    resetUnreadChatCount();
    // Clean up on unmount
    return () => {};
  }, [resetUnreadChatCount]);

  // Effect to mark chat notifications as read when on chat page
  useEffect(() => {
    if (selectedConversation && notifications.length > 0) {
      const unreadChatNotifications = notifications.filter(
        n => n.type === 'chat_message' &&
             n.conversationId === selectedConversation._id &&
             !n.isRead
      );

      if (unreadChatNotifications.length > 0) {
        const notificationIds = unreadChatNotifications.map(n => n._id);
        markMultipleAsRead(notificationIds);
      }
    }
  }, [selectedConversation, notifications, markNotificationAsRead]);

  return (
    <DashboardLayout activeMenu="Chat">


      <div className="flex h-[calc(100vh-80px)]">
        {/* Left sidebar - Conversations */}
        <div className="w-96 border-r border-gray-200">
          <ConversationList
            conversations={conversations}
            selectedConversation={selectedConversation}
            onSelectConversation={handleSelectConversation}
            loading={loading}
            onNewChat={handleNewChat}
            onRefresh={refreshConversations}
            onConversationCleared={handleConversationCleared}
          />
        </div>

        {/* Main chat area */}
        <div className="flex-1">
          {selectedConversation ? (
            <ChatWindow
              conversation={selectedConversation}
              messages={messages}
              onSendMessage={handleSendMessage}
              loading={messageLoading}
            />
          ) : showUsersList ? (
            <UsersList
              onStartConversation={handleStartConversation}
              onGroupCreated={handleGroupCreated}
            />
          ) : (
            <div className="flex flex-col justify-center items-center h-full bg-gray-50">
              <div className="text-center">
                <div className="text-gray-400 text-8xl mb-4">💬</div>
                <h3 className="text-xl font-medium text-gray-700">Start a conversation</h3>
                <p className="text-gray-500 mt-2 mb-6">
                  Select a conversation from the sidebar or start a new one
                </p>
                <button
                  onClick={handleNewChat}
                  className="px-6 py-3 bg-blue-500 text-white rounded-full hover:bg-blue-600 transition-colors"
                >
                  New Message
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

    </DashboardLayout>
  );
};

export default Chat; 