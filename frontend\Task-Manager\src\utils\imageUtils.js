import { UPLOADS_URL, BASE_URL } from './apiPaths';

/**
 * Centralized image URL processing utility
 * Handles various image URL formats and provides consistent validation
 */

/**
 * Validates and normalizes an image URL
 * @param {string|null|undefined} imagePath - The raw image path/URL
 * @returns {string|null} - Normalized URL or null if invalid
 */
export const normalizeImageUrl = (imagePath) => {
  // Comprehensive validation
  if (!imagePath || 
      imagePath === 'null' || 
      imagePath === null || 
      typeof imagePath !== 'string' || 
      imagePath.trim() === '') {
    return null;
  }

  const trimmedPath = imagePath.trim();

  // Handle data URLs (base64 images)
  if (trimmedPath.startsWith('data:image/')) {
    return trimmedPath;
  }

  // If it's already a full URL, validate and return
  if (trimmedPath.startsWith('http://') || trimmedPath.startsWith('https://')) {
    try {
      new URL(trimmedPath); // Validate URL format
      return trimmedPath;
    } catch (error) {
      if (import.meta.env.DEV) {
        console.warn('⚠️ Invalid URL format:', trimmedPath);
      }
      return null;
    }
  }

  // Handle paths that are already absolute
  if (trimmedPath.startsWith('/api/') || trimmedPath.startsWith('/uploads/')) {
    return `${BASE_URL}${trimmedPath}`;
  }

  // Normalize path: remove leading slash if present, and remove 'uploads/' prefix if present
  let normalizedPath = trimmedPath;
  if (normalizedPath.startsWith('/')) {
    normalizedPath = normalizedPath.substring(1);
  }
  if (normalizedPath.startsWith('uploads/')) {
    normalizedPath = normalizedPath.substring('uploads/'.length);
  }

  // Validate that we have a filename
  if (!normalizedPath || normalizedPath.length === 0) {
    if (import.meta.env.DEV) {
      console.warn('⚠️ Empty path after normalization:', imagePath);
    }
    return null;
  }

  // Construct the full URL
  const fullUrl = `${UPLOADS_URL}/${normalizedPath}`.replace(/:(?=\d)/, '/');
  return fullUrl;
};

/**
 * Extracts profile image URL from user object with multiple fallbacks
 * @param {object} user - User object
 * @returns {string|null} - Normalized image URL or null
 */
export const getUserProfileImageUrl = (user) => {
  if (!user || typeof user !== 'object') {
    return null;
  }

  // Try multiple image sources with priority order
  const imageUrl = user.profileImageUrl ||
                  user.profileImage ||
                  user.avatar ||
                  user.picture ||
                  user.image;

  return normalizeImageUrl(imageUrl);
};

/**
 * Generates user initials from name
 * @param {object} user - User object
 * @returns {string} - User initials
 */
export const getUserInitials = (user) => {
  if (!user) return '?';
  
  const name = user.fullName || user.name || user.displayName || '';
  if (!name || typeof name !== 'string') return '?';
  
  const trimmedName = name.trim();
  if (!trimmedName) return '?';
  
  const parts = trimmedName.split(' ').filter(part => part.length > 0);
  if (parts.length >= 2) {
    return `${parts[0][0]}${parts[1][0]}`.toUpperCase();
  }
  return trimmedName.substring(0, 2).toUpperCase();
};

/**
 * Generates a consistent color based on user name
 * @param {string} name - User name
 * @returns {string} - Hex color code
 */
export const getUserAvatarColor = (name) => {
  const colors = [
    "#1abc9c", "#2ecc71", "#3498db", "#9b59b6", "#34495e",
    "#16a085", "#27ae60", "#2980b9", "#8e44ad", "#2c3e50",
    "#f1c40f", "#e67e22", "#e74c3c", "#95a5a6", "#f39c12",
    "#d35400", "#c0392b", "#bdc3c7", "#7f8c8d"
  ];
  
  if (!name || typeof name !== 'string') {
    return colors[0]; // Default color
  }
  
  let hash = 0;
  for (let i = 0; i < name.length; i++) {
    hash = name.charCodeAt(i) + ((hash << 5) - hash);
  }
  
  const index = Math.abs(hash) % colors.length;
  return colors[index];
};

/**
 * Creates a fallback avatar URL using a service like UI Avatars
 * @param {object} user - User object
 * @returns {string} - Fallback avatar URL
 */
export const getFallbackAvatarUrl = (user) => {
  const name = user?.fullName || user?.name || user?.displayName || 'User';
  const initials = getUserInitials(user);
  const color = getUserAvatarColor(name).replace('#', '');
  
  // Use UI Avatars service as fallback
  return `https://ui-avatars.com/api/?name=${encodeURIComponent(initials)}&background=${color}&color=fff&size=128&bold=true`;
};

/**
 * Validates if an image URL is accessible
 * @param {string} imageUrl - Image URL to validate
 * @returns {Promise<boolean>} - Promise that resolves to true if image is accessible
 */
export const validateImageUrl = (imageUrl) => {
  return new Promise((resolve) => {
    if (!imageUrl) {
      resolve(false);
      return;
    }

    const img = new Image();
    img.onload = () => resolve(true);
    img.onerror = () => resolve(false);
    img.src = imageUrl;
    
    // Timeout after 5 seconds
    setTimeout(() => resolve(false), 5000);
  });
};

/**
 * Preloads an image and returns a promise
 * @param {string} imageUrl - Image URL to preload
 * @returns {Promise<string>} - Promise that resolves with the URL if successful
 */
export const preloadImage = (imageUrl) => {
  return new Promise((resolve, reject) => {
    if (!imageUrl) {
      reject(new Error('No image URL provided'));
      return;
    }

    const img = new Image();
    img.onload = () => resolve(imageUrl);
    img.onerror = (error) => reject(error);
    img.src = imageUrl;
  });
};
