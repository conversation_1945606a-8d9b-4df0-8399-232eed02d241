// Script to delete all test users from the database.
const path = require('path');
require('dotenv').config({ path: path.resolve(__dirname, '../.env') });
const mongoose = require('mongoose');
const User = require(path.join(__dirname, '../models/User'));

async function main() {
  const db = process.env.MONGO_URI;

  if (!db) {
    console.error('ERROR: MONGO_URI is not defined in your .env file.');
    console.error('Please add MONGO_URI to the .env file in the backend directory.');
    process.exit(1);
  }

  console.log(`Connecting to database...`);
  await mongoose.connect(db, { useNewUrlParser: true, useUnifiedTopology: true });
  console.log('Connection successful.');

  const query = {
    email: { $regex: /^(paginationadmin|testuser).*@example\.com$/ }
  };

  const result = await User.deleteMany(query);
  console.log(`Deleted ${result.deletedCount} test users.`);
  await mongoose.disconnect();
  console.log('Disconnected from database.');
}

main().catch(err => {
  console.error(err);
  process.exit(1);
});
