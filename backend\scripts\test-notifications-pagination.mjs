// Automated script to test notifications pagination (ESM)
import axios from 'axios';
import { CookieJar } from 'tough-cookie';
import { wrapper } from 'axios-cookiejar-support';
import { BASE_URL, ADMIN_INVITE_TOKEN } from './test-helpers.mjs';
const ADMIN_USER = {
  email: `notifadmin${Date.now()}@example.com`,
  password: 'TestAdminUser123',
  name: `Notification Admin ${Date.now()}`
};
const TARGET_USER = {
  email: `notifrecipient${Date.now()}@example.com`,
  password: 'TestRecipientUser123',
  name: `Notification Recipient ${Date.now()}`
};
const NUM_STATUS_UPDATES = 4; // Assignment (1) + Updates (4) = 5 notifications

async function main() {
  const jar = new CookieJar();
  const client = wrapper(axios.create({ baseURL: BASE_URL, jar, withCredentials: true }));
  let adminAccessToken = null;
  let targetUserAccessToken = null;
  let adminUserId = null;
  let targetUserId = null;
  let testTaskId = null;

  try {
    // 1. Register both users
    let res = await client.get('/auth/csrf-token');
    let csrfToken = res.data.csrfToken;
    const adminRegRes = await client.post('/auth/register', { ...ADMIN_USER, adminInviteToken: ADMIN_INVITE_TOKEN }, { headers: { 'x-csrf-token': csrfToken } });
    adminUserId = adminRegRes.data.id;
    console.log('Registered admin user.');

    res = await client.get('/auth/csrf-token');
    csrfToken = res.data.csrfToken;
    const targetRegRes = await client.post('/auth/register', TARGET_USER, { headers: { 'x-csrf-token': csrfToken } });
    targetUserId = targetRegRes.data.id;
    console.log('Registered target user.');

    // 2. Login as Admin and generate notifications
    client.defaults.jar = new CookieJar(); // New jar for admin
    res = await client.get('/auth/csrf-token');
    csrfToken = res.data.csrfToken;
    await client.post('/auth/login', { email: ADMIN_USER.email, password: ADMIN_USER.password }, { headers: { 'x-csrf-token': csrfToken } });
    adminAccessToken = (await client.defaults.jar.getCookies(BASE_URL)).find(c => c.key === 'accessToken').value;
    console.log('Logged in as admin.');

    // Create a task
    res = await client.get('/auth/csrf-token');
    csrfToken = res.data.csrfToken;
    const taskRes = await client.post('/tasks', { title: 'Notification Test Task' }, { headers: { 'Authorization': `Bearer ${adminAccessToken}`, 'x-csrf-token': csrfToken } });
    testTaskId = taskRes.data.task._id;
    console.log(`Created task ${testTaskId}.`);

    // Assign task to target user (1st notification)
    res = await client.get('/auth/csrf-token');
    csrfToken = res.data.csrfToken;
    await client.put(`/tasks/${testTaskId}`, { assignedTo: [targetUserId] }, { headers: { 'Authorization': `Bearer ${adminAccessToken}`, 'x-csrf-token': csrfToken } });
    console.log('Assigned task to generate notification.');

    // Update status multiple times to generate more notifications
    const statuses = ['in-progress', 'completed', 'in-progress', 'completed'];
    for (let i = 0; i < NUM_STATUS_UPDATES; i++) {
        res = await client.get('/auth/csrf-token');
        csrfToken = res.data.csrfToken;
        await client.put(`/tasks/${testTaskId}/status`, { status: statuses[i % statuses.length] }, { headers: { 'Authorization': `Bearer ${adminAccessToken}`, 'x-csrf-token': csrfToken } });
    }
    console.log(`Updated task status ${NUM_STATUS_UPDATES} times.`);

    // 3. Login as Target User and test pagination
    client.defaults.jar = new CookieJar(); // New jar for target user
    res = await client.get('/auth/csrf-token');
    csrfToken = res.data.csrfToken;
    await client.post('/auth/login', { email: TARGET_USER.email, password: TARGET_USER.password }, { headers: { 'x-csrf-token': csrfToken } });
    targetUserAccessToken = (await client.defaults.jar.getCookies(BASE_URL)).find(c => c.key === 'accessToken').value;
    console.log('Logged in as target user.');

    const TOTAL_NOTIFICATIONS = NUM_STATUS_UPDATES + 1;

    // Page 1, limit 2
    res = await client.get('/notifications?page=1&limit=2', { headers: { 'Authorization': `Bearer ${targetUserAccessToken}` } });
    if (res.data.notifications.length !== 2) throw new Error(`Page 1: Expected 2 notifications, got ${res.data.notifications.length}`);
    if (res.data.pagination.total !== TOTAL_NOTIFICATIONS) throw new Error(`Page 1: Total is incorrect. Expected ${TOTAL_NOTIFICATIONS}, got ${res.data.pagination.total}`);
    console.log('Page 1 OK.');

    // Page 2, limit 2
    res = await client.get('/notifications?page=2&limit=2', { headers: { 'Authorization': `Bearer ${targetUserAccessToken}` } });
    if (res.data.notifications.length !== 2) throw new Error(`Page 2: Expected 2 notifications, got ${res.data.notifications.length}`);
    console.log('Page 2 OK.');

    // Page 3, limit 2 (should have 1)
    res = await client.get('/notifications?page=3&limit=2', { headers: { 'Authorization': `Bearer ${targetUserAccessToken}` } });
    if (res.data.notifications.length !== 1) throw new Error(`Page 3: Expected 1 notification, got ${res.data.notifications.length}`);
    console.log('Page 3 OK.');

    console.log('All notification pagination tests passed!');

  } catch (e) {
    if (e.response) {
        console.error('Test failed with response:', { status: e.response.status, data: e.response.data, config: { url: e.response.config.url, method: e.response.config.method } });
    } else {
        console.error('Test failed:', e.message);
    }
    process.exit(1);
  } finally {
    // 4. Cleanup
    console.log('Cleaning up...');
    if (adminAccessToken) {
        client.defaults.jar = new CookieJar(); // New jar for admin cleanup
        let res = await client.get('/auth/csrf-token');
        let csrfToken = res.data.csrfToken;
        await client.post('/auth/login', { email: ADMIN_USER.email, password: ADMIN_USER.password }, { headers: { 'x-csrf-token': csrfToken } });
        adminAccessToken = (await client.defaults.jar.getCookies(BASE_URL)).find(c => c.key === 'accessToken').value;

        if (testTaskId) {
            res = await client.get('/auth/csrf-token');
            csrfToken = res.data.csrfToken;
            await client.delete(`/tasks/${testTaskId}`, { headers: { 'Authorization': `Bearer ${adminAccessToken}`, 'x-csrf-token': csrfToken } }).catch(e => console.warn('Could not delete task.'));
        }
        if (targetUserId) {
            res = await client.get('/auth/csrf-token');
            csrfToken = res.data.csrfToken;
            await client.delete(`/users/${targetUserId}`, { headers: { 'Authorization': `Bearer ${adminAccessToken}`, 'x-csrf-token': csrfToken } }).catch(e => console.warn('Could not delete target user.'));
        }
        if (adminUserId) {
            res = await client.get('/auth/csrf-token');
            csrfToken = res.data.csrfToken;
            await client.delete(`/users/${adminUserId}`, { headers: { 'Authorization': `Bearer ${adminAccessToken}`, 'x-csrf-token': csrfToken } }).catch(e => console.warn('Could not delete admin user.'));
        }
    }
    console.log('Cleanup complete.');
  }
}

main();
