const { protect, adminOnly } = require('../authMiddleware');
const jwt = require('jsonwebtoken');

// Mock User model
jest.mock('../../models/User', () => ({
  findById: jest.fn()
}));
const User = require('../../models/User');

// Helper to mock req, res, next
const mockReq = (headers = {}, user = undefined) => ({
  headers,
  user
});
const mockRes = () => {
  const res = {};
  res.status = jest.fn().mockReturnValue(res);
  res.json = jest.fn().mockReturnValue(res);
  return res;
};
const mockNext = jest.fn();

describe('authMiddleware', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('protect', () => {
    it('should return 401 if no token', async () => {
      const req = mockReq();
      const res = mockRes();
      await protect(req, res, mockNext);
      expect(res.status).toHaveBeenCalledWith(401);
      expect(res.json).toHaveBeenCalledWith({ message: 'Not authorized, no token' });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should return 401 if token invalid', async () => {
      const req = mockReq({ authorization: 'Bearer badtoken' });
      const res = mockRes();
      jest.spyOn(jwt, 'verify').mockImplementation(() => { throw new Error('bad token'); });
      await protect(req, res, mockNext);
      expect(res.status).toHaveBeenCalledWith(401);
      expect(res.json).toHaveBeenCalledWith({ message: 'Token failed', error: 'bad token' });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should return 401 if user not found', async () => {
      const token = jwt.sign({ id: 'fakeid' }, 'secret');
      const req = mockReq({ authorization: `Bearer ${token}` });
      const res = mockRes();
      jest.spyOn(jwt, 'verify').mockReturnValue({ id: 'fakeid' });
      User.findById.mockReturnValue({
  select: jest.fn().mockResolvedValue(null)
});
      await protect(req, res, mockNext);
      expect(res.status).toHaveBeenCalledWith(401);
      expect(res.json).toHaveBeenCalledWith({ message: 'Not authorized, user not found' });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should call next if token and user valid', async () => {
      const token = jwt.sign({ id: 'goodid' }, 'secret');
      const req = mockReq({ authorization: `Bearer ${token}` });
      const res = mockRes();
      jest.spyOn(jwt, 'verify').mockReturnValue({ id: 'goodid' });
      User.findById.mockReturnValue({
  select: jest.fn().mockResolvedValue({ _id: 'goodid', role: 'user' })
});
      await protect(req, res, mockNext);
      expect(mockNext).toHaveBeenCalled();
    });
  });

  describe('adminOnly', () => {
    it('should call next if user is admin', async () => {
      const req = mockReq({}, { _id: 'id', role: 'admin' });
      const res = mockRes();
      await adminOnly(req, res, mockNext);
      expect(mockNext).toHaveBeenCalled();
    });
    it('should return 403 if user is not admin', async () => {
      const req = mockReq({}, { _id: 'id', role: 'user' });
      const res = mockRes();
      await adminOnly(req, res, mockNext);
      expect(res.status).toHaveBeenCalledWith(403);
      expect(res.json).toHaveBeenCalledWith({ message: 'Access denied, admin only' });
    });
    it('should return 403 if no user', async () => {
      const req = mockReq();
      const res = mockRes();
      await adminOnly(req, res, mockNext);
      expect(res.status).toHaveBeenCalledWith(403);
      expect(res.json).toHaveBeenCalledWith({ message: 'Access denied, admin only' });
    });
  });
});
