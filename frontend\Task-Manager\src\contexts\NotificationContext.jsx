import React, { createContext, useContext, useState, useEffect, useCallback, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import axiosInstance from '../utils/axiosInstance';
import { API_PATHS } from '../utils/apiPaths';
import { useSocket } from './SocketContext';
import { useUser } from './userContext';
import toast from 'react-hot-toast';

const NotificationContext = createContext();

// Define a custom event for notification state synchronization
const NOTIFICATION_STATE_EVENT = 'notification_state_update';

export const useNotifications = () => {
  const context = useContext(NotificationContext);

  // Return default values if context is not available (user not authenticated)
  if (!context) {
    return {
      notifications: [],
      unreadCount: 0,
      loading: false,
      error: null,
      updateTrigger: 0,
      pagination: { page: 1, pages: 1, limit: 20, total: 0 },
      hasMore: false,
      fetchNotifications: () => Promise.resolve(),
      loadMoreNotifications: () => Promise.resolve(),
      markAsRead: () => Promise.resolve(),
      markMultipleAsRead: () => Promise.resolve(),
      markAllAsRead: () => Promise.resolve(),
      deleteNotification: () => Promise.resolve(),
      deleteAllNotifications: () => Promise.resolve(),
      clearAllNotifications: () => Promise.resolve(),
      getNotificationById: () => null
    };
  }

  return context;
};

export const NotificationProvider = ({ children }) => {
  const navigate = useNavigate();
  const [notifications, setNotifications] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [pagination, setPagination] = useState({
    page: 1,
    pages: 1,
    limit: 20, // Default notification limit per page
    total: 0,
  });
  const [hasMore, setHasMore] = useState(true);
  const [updateTrigger, setUpdateTrigger] = useState(0);
  const [lastFetchTime, setLastFetchTime] = useState(0);
  const socketContext = useSocket();
  const { user, loading: userLoading } = useUser();

  // Unique id for this tab to avoid handling its own broadcast events
  const broadcastId = useRef(Math.random().toString(36).substring(2, 9));



  // Function to broadcast notification state changes across tabs
  const broadcastNotificationState = useCallback((state) => {
    try {
      const payload = { ...state, senderId: broadcastId.current };

      localStorage.setItem('notification_state', JSON.stringify({
        timestamp: Date.now(),
        state: payload
      }));
      
      // Dispatch a custom event that *other* tabs will handle. This tab will ignore it.
      window.dispatchEvent(new CustomEvent(NOTIFICATION_STATE_EVENT, { 
        detail: payload
      }));
    } catch (err) {
      // Silently handle broadcast errors
    }
  }, [broadcastId]);

  // Listen for notification state updates from other tabs
  useEffect(() => {
    const handleNotificationStateUpdate = (event) => {
      const { detail } = event;
      
      // Ignore events emitted by this same tab
      if (!detail || (detail.senderId && detail.senderId === broadcastId.current)) return;
      
      if (!detail.type) return;
      

      
      switch (detail.type) {
        case 'unread_count':
          setUnreadCount(detail.count);
          break;
        case 'mark_read':
          if (detail.notificationId) {
            setNotifications(prev =>
              prev.map(notif =>
                notif._id === detail.notificationId
                  ? { ...notif, isRead: true, readAt: new Date() }
                  : notif
              )
            );
          }
          break;
        case 'mark_multiple_read':
          if (detail.notificationIds && Array.isArray(detail.notificationIds)) {
            setNotifications(prev =>
              prev.map(notif =>
                detail.notificationIds.includes(notif._id)
                  ? { ...notif, isRead: true, readAt: new Date() }
                  : notif
              )
            );
          }
          break;
        case 'mark_all_read':
          setNotifications(prev =>
            prev.map(notif => ({ ...notif, isRead: true, readAt: new Date() }))
          );
          setUnreadCount(0);
          break;
        case 'delete_notification':
          if (detail.notificationId) {
            setNotifications(prev => prev.filter(notif => notif._id !== detail.notificationId));
          }
          break;
        case 'clear_all':
          setNotifications([]);
          setUnreadCount(0);
          break;
        case 'new_notification':
          if (detail.notification) {
            setNotifications(prev => [detail.notification, ...prev]);
            setUnreadCount(prev => prev + 1);
          }
          break;
        default:
          break;
      }
    };
    
    window.addEventListener(NOTIFICATION_STATE_EVENT, handleNotificationStateUpdate);
    
    return () => {
      window.removeEventListener(NOTIFICATION_STATE_EVENT, handleNotificationStateUpdate);
    };
  }, []);

  // Safely extract socket properties with fallbacks
  const { socket, onNotification } = socketContext || { socket: null, onNotification: () => () => {} };

  // Fetch notifications with pagination
  const fetchNotifications = useCallback(async (page = 1, limit = 20, loadMore = false) => {
    if (!user || !user._id || loading) {
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await axiosInstance.get(API_PATHS.NOTIFICATIONS.GET_NOTIFICATIONS, {
        params: { page, limit },
      });

      if (response.data && response.data.success) {
        const { 
          notifications: newNotifications, 
          pagination: newPagination, 
          unreadCount: newUnreadCount 
        } = response.data;

        setNotifications(prev => {
          const existingIds = new Set(prev.map(n => n._id));
          const filteredNew = newNotifications.filter(n => !existingIds.has(n._id));
          return loadMore ? [...prev, ...filteredNew] : newNotifications;
        });
        
        setPagination(newPagination);
        setUnreadCount(newUnreadCount);
        setHasMore(newPagination.page < newPagination.pages);

        // Broadcast the updated state to other tabs
        broadcastNotificationState({
          type: 'update_notifications',
          notifications: newNotifications,
          count: newUnreadCount,
          pagination: newPagination
        });
      }
    } catch (err) {
      setError('Failed to fetch notifications');
      toast.error('Failed to fetch notifications');
    } finally {
      setLoading(false);
    }
  }, [user, loading, broadcastNotificationState]);

  const loadMoreNotifications = useCallback(() => {
    if (hasMore && !loading) {
      fetchNotifications(pagination.page + 1, pagination.limit, true);
    }
  }, [hasMore, loading, pagination, fetchNotifications]);

  // Initial notification fetch when user becomes available
  const initialNotificationFetch = useRef(false);

  useEffect(() => {
    if (!userLoading && user && user._id && !initialNotificationFetch.current) {
      fetchNotifications(1, 20, false); // Fetch first page
      initialNotificationFetch.current = true;
    }
  }, [userLoading, user, fetchNotifications]);

  // Watch for socket context availability and re-setup listeners
  useEffect(() => {
    if (!userLoading && user && user._id && socketContext && onNotification && typeof onNotification === 'function') {
      // Socket context is now available, but don't fetch here as it's already fetched in initial effect
      // This effect is just for socket setup
    }
  }, [socketContext, onNotification, userLoading, user]); // Removed fetchNotifications dependency

  // Subscribe to real-time notifications via custom event
  useEffect(() => {
    // Wait for user loading to complete and user to be authenticated
    if (userLoading || !user || !user._id) {
      return () => {};
    }

    // When a notification is received via custom event
    const handleNewNotification = (event) => {
      const notification = event.detail;

      if (!notification || !notification._id) {
        return;
      }

      // Check if notification already exists to prevent duplicates
      let isNewNotification = false;
      setNotifications(prev => {
        const exists = prev.some(n => n._id === notification._id);
        if (exists) {
          return prev;
        }

        isNewNotification = true;
        return [notification, ...prev];
      });

      // Update unread count only if notification is truly new
      if (isNewNotification) {
        setUnreadCount(prev => prev + 1);
      }

      // Broadcast to other tabs and show toast only if it's a new notification
      if (isNewNotification) {
        broadcastNotificationState({
          type: 'new_notification',
          notification,
          count: unreadCount + 1
        });

        // Show toast notification
        const message = notification.message || 'You have a new notification';
        toast(message, {
          icon: '🔔',
          duration: 5000,
          onClick: () => {
            // Mark as read when clicked
            markAsRead(notification._id);

            // Navigate to task details (user view)
            if (notification.data?.taskId) {
              navigate(`/user/view-task-details/${notification.data.taskId}`);
            }
          }
        });
      }
    };

    // Set up custom event listener for new notifications

    window.addEventListener('socketNewNotification', handleNewNotification);

    // Set up socket listener for notification read events
    const handleNotificationRead = ({ notificationId, notificationIds }) => {
      if (notificationIds && Array.isArray(notificationIds)) {
        // Batch update
        setNotifications(prev => {
          const updated = prev.map(notif =>
            notificationIds.includes(notif._id)
              ? { ...notif, isRead: true, readAt: new Date() }
              : notif
          );
          return updated;
        });

        // Count how many were actually unread and update count
        setNotifications(currentNotifications => {
          const actualUnreadCount = notificationIds.filter(id => {
            const notif = currentNotifications.find(n => n._id === id);
            return notif && !notif.isRead;
          }).length;

          setUnreadCount(prev => {
            const newCount = Math.max(0, prev - actualUnreadCount);
            // Broadcast the update to other tabs
            broadcastNotificationState({
              type: 'mark_multiple_read',
              notificationIds,
              count: newCount
            });
            return newCount;
          });

          return currentNotifications;
        });
      } else if (notificationId) {
        // Single update
        setNotifications(prev =>
          prev.map(notif =>
            notif._id === notificationId
              ? { ...notif, isRead: true, readAt: new Date() }
              : notif
          )
        );

        setNotifications(currentNotifications => {
          const notif = currentNotifications.find(n => n._id === notificationId);
          if (notif && !notif.isRead) {
            setUnreadCount(prev => {
              const newCount = Math.max(0, prev - 1);
              // Broadcast the update to other tabs
              broadcastNotificationState({
                type: 'mark_read',
                notificationId,
                count: newCount
              });
              return newCount;
            });
          }
          return currentNotifications;
        });
      }

      setUpdateTrigger(prev => prev + 1);
    };

    if (socket && socket.on) {
      socket.on('notificationRead', handleNotificationRead);
    }

    // Remove duplicate fetch - notifications are already fetched in initial effect above

    return () => {

      window.removeEventListener('socketNewNotification', handleNewNotification);
      if (socket && socket.off) {
        socket.off('notificationRead', handleNotificationRead);
      }
    };
  }, [socket, user, userLoading, broadcastNotificationState]); // Removed fetchNotifications dependency

  // Mark a notification as read
  const markAsRead = useCallback(async (notificationId) => {
    try {
      // Optimistically update the UI first
      setNotifications(prev =>
        prev.map(notif =>
          notif._id === notificationId
            ? { ...notif, isRead: true }
            : notif
        )
      );


      // Broadcast to other tabs
      setUnreadCount(currentCount => {
        const newCount = Math.max(0, currentCount - 1);
        broadcastNotificationState({
          type: 'mark_read',
          notificationId,
          count: newCount
        });
        return newCount;
      });

      // Force re-render
      setUpdateTrigger(prev => prev + 1);

      const response = await axiosInstance.put(`/api/notifications/${notificationId}/read`);

      if (response.data && response.data.success) {
        // Emit socket event to notify other components
        if (socket && socket.emit) {
          socket.emit('notificationRead', { notificationId });
        }

        return { success: true, message: response.data.message };
      } else {
        // Revert optimistic update on failure
        setNotifications(prev =>
          prev.map(notif =>
            notif._id === notificationId
              ? { ...notif, isRead: false }
              : notif
          )
        );

        
        // Broadcast reversion to other tabs
        setUnreadCount(currentCount => {
          const newCount = currentCount + 1;
          broadcastNotificationState({
            type: 'unread_count',
            count: newCount
          });
          return newCount;
        });
        
        return { success: false, error: 'Failed to mark notification as read' };
      }
    } catch (err) {
      // Revert optimistic update on error
      setNotifications(prev =>
        prev.map(notif =>
          notif._id === notificationId
            ? { ...notif, isRead: false }
            : notif
        )
      );

      
      // Broadcast reversion to other tabs
      setUnreadCount(currentCount => {
        const newCount = currentCount + 1;
        broadcastNotificationState({
          type: 'unread_count',
          count: newCount
        });
        return newCount;
      });
      
      return { success: false, error: err.response?.data?.message || err.message || 'Failed to mark notification as read' };
    }
  }, [socket, broadcastNotificationState]);

  // Mark multiple notifications as read (batch operation)
  const markMultipleAsRead = useCallback(async (notificationIds) => {
    if (!notificationIds || notificationIds.length === 0) return { success: true };

    try {
      // Count how many notifications are actually unread using current state
      let actualUnreadCount = 0;
      setNotifications(currentNotifications => {
        actualUnreadCount = notificationIds.filter(id => {
          const notif = currentNotifications.find(n => n._id === id);
          return notif && !notif.isRead;
        }).length;
        return currentNotifications;
      });

      // Optimistically update the UI first
      setNotifications(prev => {
        const updated = prev.map(notif =>
          notificationIds.includes(notif._id)
            ? { ...notif, isRead: true }
            : notif
        );
        return updated;
      });

      setUnreadCount(prev => {
        const newCount = Math.max(0, prev - actualUnreadCount);
        // Broadcast to other tabs
        broadcastNotificationState({
          type: 'mark_multiple_read',
          notificationIds,
          count: newCount
        });
        return newCount;
      });

      // Force re-render
      setUpdateTrigger(prev => prev + 1);

      // Mark all notifications as read on the backend
      const promises = notificationIds.map(id =>
        axiosInstance.put(`/api/notifications/${id}/read`)
      );

      const responses = await Promise.all(promises);
      const allSuccessful = responses.every(response => response.data?.success);

      if (allSuccessful) {
        // Emit socket event for batch update
        if (socket && socket.emit) {
          socket.emit('notificationRead', { notificationIds });
        }
        return { success: true, message: 'All notifications marked as read' };
      } else {
        // Revert optimistic updates on failure
        setNotifications(prev =>
          prev.map(notif =>
            notificationIds.includes(notif._id)
              ? { ...notif, isRead: false }
              : notif
          )
        );

        
        // Broadcast reversion to other tabs
        setUnreadCount(currentCount => {
          const newCount = currentCount + actualUnreadCount;
          broadcastNotificationState({
            type: 'unread_count',
            count: newCount
          });
          return newCount;
        });
        
        return { success: false, error: 'Failed to mark some notifications as read' };
      }
    } catch (err) {
      // Revert optimistic updates on error
      let actualUnreadCount = 0;
      setNotifications(currentNotifications => {
        actualUnreadCount = notificationIds.filter(id => {
          const notif = currentNotifications.find(n => n._id === id);
          return notif && !notif.isRead;
        }).length;
        return currentNotifications;
      });
      
      setNotifications(prev =>
        prev.map(notif =>
          notificationIds.includes(notif._id)
            ? { ...notif, isRead: false }
            : notif
        )
      );
      setUnreadCount(prev => {
        const newCount = prev + actualUnreadCount;
        // Broadcast reversion to other tabs
        broadcastNotificationState({
          type: 'unread_count',
          count: newCount
        });
        return newCount;
      });
      
      return { success: false, error: err.response?.data?.message || err.message || 'Failed to mark notifications as read' };
    }
  }, [socket, broadcastNotificationState]);

  // Mark all notifications as read
  const markAllAsRead = useCallback(async () => {
    try {
      // Optimistically update UI first for instant feedback
      setNotifications(prev => prev.map(notif => ({ ...notif, isRead: true, readAt: new Date() })));
      setUnreadCount(0);

      // Broadcast to other tabs immediately
      broadcastNotificationState({
        type: 'mark_all_read',
        count: 0
      });

      // Force re-render
      setUpdateTrigger(prev => prev + 1);

      // Call API in background
      const response = await axiosInstance.put('/api/notifications/mark-all-read');

      if (response.data && response.data.success) {
        return { success: true, message: response.data.message };
      } else {
        // Revert optimistic update on failure
        fetchNotifications();
        return { success: false, error: 'Failed to mark all notifications as read' };
      }
    } catch (err) {
      // Revert optimistic update on error
      fetchNotifications();
      return { success: false, error: err.response?.data?.message || err.message || 'Failed to mark all notifications as read' };
    }
  }, [broadcastNotificationState, fetchNotifications]);

  // Delete a notification
  const deleteNotification = useCallback(async (notificationId) => {
    try {
      const response = await axiosInstance.delete(`/api/notifications/${notificationId}`);

      if (response.data && response.data.success) {
        // Update local state
        setNotifications(prev => {
          const deletedNotif = prev.find(notif => notif._id === notificationId);
          if (deletedNotif && !deletedNotif.isRead) {
            setUnreadCount(count => {
              const newCount = Math.max(0, count - 1);
              // Broadcast the updated count
              broadcastNotificationState({
                type: 'unread_count',
                count: newCount
              });
              return newCount;
            });
          }
          return prev.filter(notif => notif._id !== notificationId);
        });
        
        // Broadcast deletion to other tabs
        broadcastNotificationState({
          type: 'delete_notification',
          notificationId
        });
        
        return { success: true, message: response.data.message };
      } else {
        return { success: false, error: 'Failed to delete notification' };
      }
    } catch (err) {
      return { success: false, error: err.response?.data?.message || err.message || 'Failed to delete notification' };
    }
  }, [broadcastNotificationState]);

  // Delete all notifications
  const deleteAllNotifications = useCallback(async () => {
    try {
      // Optimistically update UI first for instant feedback
      setNotifications([]);
      setUnreadCount(0);

      // Broadcast to other tabs immediately
      broadcastNotificationState({
        type: 'clear_all',
        count: 0
      });

      // Force re-render
      setUpdateTrigger(prev => prev + 1);

      // Call API in background
      const response = await axiosInstance.delete('/api/notifications/clear-all');

      if (response.data && response.data.success) {
        return { success: true, message: response.data.message };
      } else {
        // Revert optimistic update on failure
        fetchNotifications();
        return { success: false, error: 'Failed to clear all notifications' };
      }
    } catch (err) {
      // Revert optimistic update on error
      fetchNotifications();
      return { success: false, error: err.response?.data?.message || err.message || 'Failed to clear all notifications' };
    }
  }, [broadcastNotificationState, fetchNotifications]);

  // Get notification by ID - using regular function to avoid dependency issues
  const getNotificationById = (notificationId) => {
    return notifications.find(notif => notif._id === notificationId) || null;
  };

  return (
    <NotificationContext.Provider
      value={{
        notifications,
        unreadCount,
        loading,
        error,
        pagination,
        hasMore,
        updateTrigger, // Include trigger to force re-renders
        fetchNotifications,
        loadMoreNotifications,
        markAsRead,
        markMultipleAsRead,
        markAllAsRead,
        deleteNotification,
        deleteAllNotifications,
        clearAllNotifications: deleteAllNotifications, // Alias for backward compatibility
        getNotificationById
      }}
    >
      {children}
    </NotificationContext.Provider>
  );
}; 