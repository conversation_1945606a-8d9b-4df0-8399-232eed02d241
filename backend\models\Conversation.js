const mongoose = require('mongoose');

// Do not require Message model here to prevent circular dependency issues.
// It will be required inside the static method where it's needed.

const conversationSchema = new mongoose.Schema(
  {
    type: {
      type: String,
      enum: ['individual', 'group'],
      required: true
    },
    participants: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
      }
    ],
    name: {
      type: String,
      trim: true,
      default: null // Used for group chats
    },
    lastMessage: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Message',
      default: null
    },
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    isActive: {
      type: Boolean,
      default: true
    }
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

// Virtual field to get unread counts for a user
conversationSchema.virtual('unreadCount').get(function () {
  return this._unreadCount || 0;
});

// Set unread count for a specific user
conversationSchema.methods.setUnreadCount = function (count) {
  this._unreadCount = count;
  return this;
};

// Helper method to find or create a conversation between two users
conversationSchema.statics.findOrCreateIndividualConversation = async function (
  userId1,
  userId2
) {
  const Conversation = this;
  // Require the Message model here, inside the function, to avoid circular dependencies.
  const Message = mongoose.model("Message");

  // Make sure we're comparing strings, not ObjectIDs
  const user1 = userId1.toString();
  const user2 = userId2.toString();

  // Find any conversation where both users are participants and it's an individual type
  const existingConversations = await Conversation.find({
    type: 'individual',
    participants: { $all: [userId1, userId2] }
  }).populate('participants');
  
  // If multiple conversations found, use the first one and remove others (cleanup)
  if (existingConversations.length > 1) {
    console.log(`Found ${existingConversations.length} duplicate conversations between users ${userId1} and ${userId2}. Cleaning up...`);
    
    // Keep the first one, delete others
    const conversationToKeep = existingConversations[0];
    
    for (let i = 1; i < existingConversations.length; i++) {
      console.log(`Removing duplicate conversation: ${existingConversations[i]._id}`);
      await Conversation.deleteOne({ _id: existingConversations[i]._id });
      
      // If the one we're deleting has messages, migrate them to the one we're keeping
      await Message.updateMany(
        { conversationId: existingConversations[i]._id },
        { conversationId: conversationToKeep._id }
      );
    }
    
    return conversationToKeep;
  }
  
  // Return existing conversation if found
  if (existingConversations.length === 1) {
    return existingConversations[0];
  }

  console.log(`Creating new conversation between users ${userId1} and ${userId2}`);
  
  // Create a new conversation if none exists
  const newConversation = new Conversation({
    type: 'individual',
    participants: [userId1, userId2],
    createdBy: userId1
  });

  await newConversation.save();
  return newConversation;
};

const Conversation = mongoose.model('Conversation', conversationSchema);

module.exports = Conversation; 