import { useState, useCallback } from 'react';
import axiosInstance from '../utils/axiosInstance';
import toast from 'react-hot-toast';

/**
 * Custom hook for making API requests with axios
 * @returns {Object} Object containing loading state, error state, and fetch functions
 */
const useAxios = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  /**
   * Make a request to the API
   * @param {Object} config - Axios request configuration
   * @param {string} config.url - API endpoint
   * @param {string} config.method - HTTP method (GET, POST, PUT, DELETE)
   * @param {Object} config.data - Request data (for POST, PUT)
   * @param {boolean} config.showSuccessToast - Whether to show success toast
   * @param {boolean} config.showErrorToast - Whether to show error toast
   * @param {string} config.successMessage - Custom success message
   * @param {string} config.errorMessage - Custom error message
   * @returns {Promise} Promise that resolves to the response data
   */
  const fetchData = useCallback(async ({
    url,
    method = 'GET',
    data = null,
    showSuccessToast = false,
    showErrorToast = true,
    successMessage = 'Operation successful',
    errorMessage = 'Something went wrong'
  }) => {
    setLoading(true);
    setError(null);

    try {
      const response = await axiosInstance({
        url,
        method,
        data,
      });

      if (showSuccessToast) {
        toast.success(successMessage);
      }

      return response;
    } catch (err) {
      console.error(`API Error (${method} ${url}):`, err);
      
      const errorMsg = err.response?.data?.message || errorMessage;
      setError(errorMsg);
      
      if (showErrorToast) {
        toast.error(errorMsg);
      }
      
      return { error: err, data: null };
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Make a GET request to the API
   * @param {string} url - API endpoint
   * @param {Object} options - Additional options
   * @returns {Promise} Promise that resolves to the response data
   */
  const get = useCallback((url, options = {}) => {
    return fetchData({ url, method: 'GET', ...options });
  }, [fetchData]);

  /**
   * Make a POST request to the API
   * @param {string} url - API endpoint
   * @param {Object} data - Request data
   * @param {Object} options - Additional options
   * @returns {Promise} Promise that resolves to the response data
   */
  const post = useCallback((url, data, options = {}) => {
    return fetchData({ url, method: 'POST', data, ...options });
  }, [fetchData]);

  /**
   * Make a PUT request to the API
   * @param {string} url - API endpoint
   * @param {Object} data - Request data
   * @param {Object} options - Additional options
   * @returns {Promise} Promise that resolves to the response data
   */
  const put = useCallback((url, data, options = {}) => {
    return fetchData({ url, method: 'PUT', data, ...options });
  }, [fetchData]);

  /**
   * Make a DELETE request to the API
   * @param {string} url - API endpoint
   * @param {Object} options - Additional options
   * @returns {Promise} Promise that resolves to the response data
   */
  const remove = useCallback((url, options = {}) => {
    return fetchData({ url, method: 'DELETE', ...options });
  }, [fetchData]);

  return {
    loading,
    error,
    fetchData,
    get,
    post,
    put,
    delete: remove
  };
};

export default useAxios; 