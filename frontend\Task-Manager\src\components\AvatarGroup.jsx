import React from "react";
import ProxyImage from "./common/ProxyImage"; // Import ProxyImage
import {
  getUserProfileImageUrl,
  getUserInitials,
  getUserAvatarColor,
  getFallbackAvatarUrl
} from "../utils/imageUtils";

const AvatarGroup = ({ avatars = [], maxVisible = 4, size = "sm", onClick }) => {
  const sizeClasses = {
    xs: "w-6 h-6",
    sm: "w-8 h-8",
    md: "w-10 h-10",
    lg: "w-12 h-12",
  };

  // Handle case where avatars might be null or undefined
  if (!avatars || !Array.isArray(avatars) || avatars.length === 0) {
    return null;
  }

  // Debug output
  console.log('AvatarGroup received:', {
    avatars: avatars.map(a => ({
      _id: a._id,
      name: a.name || a.fullName,
      imageUrl: getUserProfileImageUrl(a)
    }))
  });

  const visibleAvatars = avatars.slice(0, maxVisible);
  const remainingCount = Math.max(0, avatars.length - maxVisible);

  return (
    <div
      className={`flex -space-x-2 overflow-hidden ${onClick ? "cursor-pointer" : ""}`}
      onClick={onClick}
    >
      {visibleAvatars.map((avatar, index) => {
        // Get user info regardless of avatar format
        const user = typeof avatar === 'string' ? { name: avatar } : avatar;
        const name = user?.fullName || user?.name || "";
        const initials = getUserInitials(user);
        const avatarColor = getUserAvatarColor(name);

        // Debug user data
        console.log('Processing avatar:', { user, name, initials });

        // Use centralized image URL processing
        const imageUrl = getUserProfileImageUrl(user);
        const fallbackUrl = getFallbackAvatarUrl(user);

        // Debug image URLs
        console.log('Image URLs:', { imageUrl, fallbackUrl });



        return (
          <div
            key={index}
            className={`${sizeClasses[size]} rounded-full border border-white overflow-hidden relative`}
            title={name || "User"}
          >
            {imageUrl ? (
              <ProxyImage
                src={imageUrl}
                alt={name || "User"}
                className="w-full h-full object-cover transition-opacity duration-200"
                containerClassName="w-full h-full" // Ensure ProxyImage fills its container
                fallbackText={initials} // Pass initials as fallback text
                style={{ backgroundColor: avatarColor }} // Apply background color for fallback
              />
            ) : (
              <div
                className="w-full h-full flex items-center justify-center"
                style={{ backgroundColor: avatarColor }}
              >
                <span className="text-white text-xs font-medium">{initials}</span>
              </div>
            )}
          </div>
        );
      })}
      {remainingCount > 0 && (
        <div className={`${sizeClasses[size]} rounded-full bg-gray-200 border border-white flex items-center justify-center text-xs font-medium text-gray-600`}>
          +{remainingCount}
        </div>
      )}
    </div>
  );
};

export default AvatarGroup;
