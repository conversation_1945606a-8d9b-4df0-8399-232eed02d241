@import url("https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap");

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --font-display: "Poppins", sans-serif;
    --breakpoint-3xl: 1920px;
    --color-primary: #1368ec;
  }

  html {
    font-family: var(--font-display);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  body {
    background-color: #fcfbfc;
    color: #1e293b; /* This is the hex value for slate-800 */
    overflow-x: hidden;
  }
}

@layer components {
  .hero-gradient {
    background-image: linear-gradient(
      to bottom right,
      #eff6ff,
      #ecfeff,
      #f0f9ff
    );
  }

  .btn-primary {
    background-color: #0f172a; /* slate-900 */
    color: #ffffff;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 600;
    font-size: 0.875rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
      0 4px 6px -2px rgba(0, 0, 0, 0.05);
    transition-property: all;
    transition-duration: 300ms;
    transform: translateZ(0);
  }

  .form-card {
    @apply bg-white p-6 rounded-lg shadow-md shadow-gray-100 border border-gray-200/50;
  }

  .form-input {
    @apply w-full text-sm text-black outline-none bg-white border border-slate-100 px-2.5 py-3 rounded-md mt-2 placeholder:text-gray-500;
  }

  .input-box {
    @apply w-full flex justify-between gap-3 text-sm text-black bg-slate-100/50 rounded px-4 py-3 mb-4 mt-3 border border-slate-200 outline-none;
  }

  .auth-btn {
    @apply w-full bg-auth-btn hover:bg-auth-btn-hover text-white py-2 px-4 rounded-md font-semibold focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-auth-btn-focus;
  }

  .card {
    @apply bg-white p-6 rounded-2xl shadow-md shadow-gray-100 border border-gray-200/50;
  }

  .card-btn {
    @apply flex items-center gap-3 text-[12px] font-medium text-gray-700 hover:text-primary bg-gray-50 hover:bg-blue-50 px-4 py-1.5 rounded-lg border border-gray-200/50 cursor-pointer;
  }
  
  .user-card {
    @apply bg-white p-4 rounded-xl shadow-md shadow-gray-100 border border-gray-200/50;
  }
  
  .modal-btn-cancel {
    @apply w-full sm:w-auto px-5 py-2.5 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary;
  }
  
  .modal-btn-confirm {
    @apply w-full sm:w-auto px-5 py-2.5 text-sm font-medium text-white bg-primary border border-transparent rounded-lg shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 flex items-center justify-center;
  }

  .btn-primary:hover {
    background-color: #334155; /* slate-700 */
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
      0 10px 10px -5px rgba(0, 0, 0, 0.04);
    transform: scale(1.05);
  }

  .btn-primary:active {
    background-color: #0f172a; /* slate-900 */
    transform: scale(0.95);
  }

  .btn-secondary {
    color: #334155; /* slate-700 */
    font-weight: 600;
    padding: 0.5rem 0.75rem;
    transition-property: color;
    font-size: 1rem;
  }

  .btn-secondary:hover {
    color: #0f172a; /* slate-900 */
  }

  .content-card {
    background-color: #ffffff;
    border-radius: 0.75rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
      0 2px 4px -1px rgba(0, 0, 0, 0.06);
    transition-property: all;
    transition-duration: 300ms;
  }

  .content-card:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
      0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }
}

/* Custom animations and complex styles */
.hero-ui-card-base {
  background-color: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  border-radius: 0.875rem;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.08),
    0 8px 10px -6px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: transform 0.4s cubic-bezier(0.165, 0.84, 0.44, 1),
    box-shadow 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
}

.hero-ui-card-base:hover {
  transform: translateY(-10px) scale(1.03);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15),
    0 10px 15px -8px rgba(0, 0, 0, 0.15);
}

.hero-floating-avatar {
  position: absolute;
  border-radius: 9999px;
  box-shadow: 0px 10px 25px rgba(0, 0, 0, 0.15), 0px 5px 10px rgba(0, 0, 0, 0.1);
  border: 3px solid white;
  transition: transform 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  object-fit: cover;
}

.hero-floating-avatar:hover {
  transform: scale(1.2) rotate(5deg);
}

.partner-logo-group {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: #9ca3af;
  font-weight: 600;
  font-size: 1.5rem;
  opacity: 0.7;
  transition: color 0.3s ease, opacity 0.3s ease, transform 0.3s ease;
}

.partner-logo-group:hover {
  color: #374151;
  opacity: 1;
  transform: translateY(-3px);
}

/* More custom styles as needed */
.faq-answer {
  transition: max-height 0.5s cubic-bezier(0.23, 1, 0.32, 1),
    opacity 0.4s ease-in-out 0.05s,
    padding-bottom 0.5s cubic-bezier(0.23, 1, 0.32, 1);
  max-height: 0;
  overflow: hidden;
  opacity: 0;
  padding-bottom: 0;
}

.faq-answer.open {
  max-height: 600px;
  opacity: 1;
  padding-bottom: 1rem;
}

.faq-icon {
  transition: transform 0.4s cubic-bezier(0.23, 1, 0.32, 1);
}

.faq-icon.rotated {
  transform: rotate(135deg);
}

html.scroll-smooth {
  scroll-behavior: smooth;
}

/* Additional global styles can be added here */