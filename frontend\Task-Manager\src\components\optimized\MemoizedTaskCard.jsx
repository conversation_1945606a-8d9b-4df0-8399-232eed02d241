/**
 * Optimized Task Card Component
 * Uses React.memo and optimized rendering for better performance
 */

import React, { memo, useMemo, useCallback } from 'react';
import { <PERSON><PERSON><PERSON>nda<PERSON>, LuUser, Lu<PERSON>aperclip, LuMoreVertical } from 'react-icons/lu';
import { getStatusBadgeColor, getPriorityBadgeColor, formatDueDate, isTaskOverdue } from '../../utils/taskUtils';

const MemoizedTaskCard = memo(({ 
  task, 
  onClick, 
  onEdit, 
  onDelete,
  showActions = true 
}) => {
  // Memoize computed values to prevent recalculation on every render
  const computedValues = useMemo(() => {
    const isOverdue = isTaskOverdue(task.dueDate, task.status);
    const formattedDueDate = formatDueDate(task.dueDate);
    const statusBadgeColor = getStatusBadgeColor(task.status);
    const priorityBadgeColor = getPriorityBadgeColor(task.priority);
    const completedTodos = task.todoCheckList?.filter(item => item.completed).length || 0;
    const totalTodos = task.todoCheckList?.length || 0;
    const progressPercentage = totalTodos > 0 ? Math.round((completedTodos / totalTodos) * 100) : 0;
    
    return {
      isOverdue,
      formattedDueDate,
      statusBadgeColor,
      priorityBadgeColor,
      completedTodos,
      totalTodos,
      progressPercentage
    };
  }, [task.dueDate, task.status, task.priority, task.todoCheckList]);

  // Memoize assigned users display
  const assignedUsersDisplay = useMemo(() => {
    const assignedTo = task.assignedTo || task.team || [];
    if (!assignedTo || assignedTo.length === 0) {
      return <span className="text-gray-400 text-sm">Unassigned</span>;
    }

    const displayUsers = assignedTo.slice(0, 3);
    const remainingCount = assignedTo.length - 3;

    return (
      <div className="flex items-center space-x-1">
        {displayUsers.map((user, index) => (
          <div
            key={user._id || index}
            className="w-6 h-6 rounded-full bg-gray-300 flex items-center justify-center text-xs font-medium text-gray-600 border-2 border-white"
            title={user.name || user.email}
          >
            {(user.name || user.email || 'U').charAt(0).toUpperCase()}
          </div>
        ))}
        {remainingCount > 0 && (
          <div className="w-6 h-6 rounded-full bg-gray-400 flex items-center justify-center text-xs font-medium text-white border-2 border-white">
            +{remainingCount}
          </div>
        )}
      </div>
    );
  }, [task.assignedTo, task.team]);

  // Memoize event handlers to prevent unnecessary re-renders
  const handleClick = useCallback((e) => {
    e.stopPropagation();
    onClick?.(task);
  }, [onClick, task]);

  const handleEdit = useCallback((e) => {
    e.stopPropagation();
    onEdit?.(task);
  }, [onEdit, task]);

  const handleDelete = useCallback((e) => {
    e.stopPropagation();
    onDelete?.(task);
  }, [onDelete, task]);

  return (
    <div 
      className={`
        bg-white rounded-lg shadow-sm border border-gray-200 p-4 
        hover:shadow-md transition-shadow duration-200 cursor-pointer
        ${computedValues.isOverdue ? 'border-l-4 border-l-red-500' : ''}
      `}
      onClick={handleClick}
    >
      {/* Header */}
      <div className="flex items-start justify-between mb-3">
        <h3 className="font-medium text-gray-900 line-clamp-2 flex-1 mr-2">
          {task.title}
        </h3>
        {showActions && (
          <div className="flex items-center space-x-1">
            <button
              onClick={handleEdit}
              className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
              title="Edit task"
            >
              <LuMoreVertical className="w-4 h-4" />
            </button>
          </div>
        )}
      </div>

      {/* Description */}
      {task.description && (
        <p className="text-sm text-gray-600 mb-3 line-clamp-2">
          {task.description}
        </p>
      )}

      {/* Status and Priority Badges */}
      <div className="flex items-center space-x-2 mb-3">
        <span className={`px-2 py-1 rounded-full text-xs font-medium border ${computedValues.statusBadgeColor}`}>
          {task.status}
        </span>
        <span className={`px-2 py-1 rounded-full text-xs font-medium border ${computedValues.priorityBadgeColor}`}>
          {task.priority}
        </span>
      </div>

      {/* Progress Bar */}
      {computedValues.totalTodos > 0 && (
        <div className="mb-3">
          <div className="flex items-center justify-between text-xs text-gray-600 mb-1">
            <span>Progress</span>
            <span>{computedValues.completedTodos}/{computedValues.totalTodos} tasks</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${computedValues.progressPercentage}%` }}
            />
          </div>
        </div>
      )}

      {/* Footer */}
      <div className="flex items-center justify-between text-sm">
        {/* Due Date */}
        <div className={`flex items-center space-x-1 ${computedValues.isOverdue ? 'text-red-600' : 'text-gray-600'}`}>
          <LuCalendar className="w-4 h-4" />
          <span>{computedValues.formattedDueDate}</span>
        </div>

        {/* Assigned Users */}
        <div className="flex items-center space-x-2">
          {task.attachments && task.attachments.length > 0 && (
            <div className="flex items-center space-x-1 text-gray-500">
              <LuPaperclip className="w-4 h-4" />
              <span className="text-xs">{task.attachments.length}</span>
            </div>
          )}
          {assignedUsersDisplay}
        </div>
      </div>
    </div>
  );
});

// Set display name for debugging
MemoizedTaskCard.displayName = 'MemoizedTaskCard';

export default MemoizedTaskCard;
