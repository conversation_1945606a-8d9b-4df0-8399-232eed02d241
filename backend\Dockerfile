# Use an official Node.js LTS runtime as a parent image
FROM node:22-alpine

# The official node image includes a non-root 'node' user. We will use it.
# Set the working directory in the node user's home directory.
WORKDIR /home/<USER>/app

# Copy package files and install dependencies
# This layer is cached and only re-run when package.json or package-lock.json changes
COPY package*.json ./
RUN npm ci --cache /home/<USER>/.npm

# Copy the rest of the application code
COPY . .

# Expose the port the app runs on
EXPOSE 3000

# The container will run as the 'node' user by default.
CMD ["npm", "start"]



