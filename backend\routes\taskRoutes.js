const express = require("express");
const { enhancedAdminOnly } = require("../middleware/enhancedAuthMiddleware");
const {
  getDashboardData,
  getUserDashboardData,
  getTasks,
  getTaskById,
  createTask,
  updateTask,
  deleteTask,
  updateTaskStatus,
  updateTaskChecklist,
  bulkDeleteTasks,
} = require("../controllers/taskController");
const { createSecureUpload, validateUploadedFile } = require("../middleware/secureUploadMiddleware");
const multer = require('multer');
const path = require('path');

const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, path.join(__dirname, '../uploads/')); // Ensure this directory exists
  },
  filename: function (req, file, cb) {
    cb(null, Date.now() + '-' + file.originalname);
  }
});

const upload = multer({ storage: storage });

const router = express.Router();

// Note: 'enhancedProtect' middleware is already applied at the app level in server.js for all task routes.

// Task Management Routes
router.get("/dashboard-data", enhancedAdminOnly, getDashboardData); // Admin dashboard data
router.get("/user-dashboard-data", getUserDashboardData); // User dashboard data
router.get("/", getTasks); // Get all tasks (Controller handles role-based filtering)

// IMPORTANT: Specific routes like /bulk must come BEFORE /:id routes
router.delete("/bulk", enhancedAdminOnly, bulkDeleteTasks); // Bulk delete tasks (Admin only)

// Routes with path parameters
router.get("/:id", getTaskById); // Get task by ID
router.post("/", enhancedAdminOnly, upload.array('attachments'), createTask);
router.put("/:id", enhancedAdminOnly, upload.array('attachments'), updateTask);
router.delete("/:id", enhancedAdminOnly, deleteTask);
router.put("/:id/status", updateTaskStatus);
router.put("/:id/checklist", updateTaskChecklist);
router.delete("/:id", enhancedAdminOnly, deleteTask); // Delete a task (Admin only)

module.exports = router;
