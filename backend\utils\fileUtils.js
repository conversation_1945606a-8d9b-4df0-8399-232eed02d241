const fs = require('fs');
const path = require('path');

/**
 * Delete a file from the uploads directory
 * @param {string} filePath - Path to the file, can be full URL or just filename
 * @returns {Promise<boolean>} - True if file was deleted, false if not
 */
const deleteFile = async (filePath) => {
  try {
    if (!filePath) {
      console.log('No file path provided for deletion');
      return false;
    }

    // If it's a full URL or path with /uploads/, extract just the filename
    let filename = filePath;
    
    // Extract filename from URL or path
    const uploadMatch = filePath.match(/\/uploads\/([^?#]+)/);
    if (uploadMatch && uploadMatch[1]) {
      filename = decodeURIComponent(uploadMatch[1]); // Handle URL encoded characters
    } else if (path.basename(filePath) !== filePath) {
      // If it's a path but not just a filename, get the basename
      filename = path.basename(filePath);
    }
    
    // Construct the full path to the file
    const fullPath = path.join(__dirname, '..', 'uploads', filename);
    console.log(`Attempting to delete file: ${fullPath}`);
    
    // Check if file exists
    if (!fs.existsSync(fullPath)) {
      console.log(`File not found: ${fullPath}`);
      return false;
    }
    
    // Delete the file
    fs.unlinkSync(fullPath);
    console.log(`Successfully deleted file: ${fullPath}`);
    return true;
  } catch (error) {
    console.error(`Error deleting file: ${error.message}`);
    return false;
  }
};

/**
 * Extract filename from a URL or path
 * @param {string} urlOrPath - URL or path containing filename
 * @returns {string|null} - Extracted filename or null if not found
 */
const extractFilenameFromPath = (urlOrPath) => {
  if (!urlOrPath) return null;
  
  // Try to extract filename from /uploads/ path
  const uploadMatch = urlOrPath.match(/\/uploads\/([^?#]+)/);
  if (uploadMatch && uploadMatch[1]) {
    return uploadMatch[1];
  }
  
  // If it's just a filename, return it
  if (path.basename(urlOrPath) === urlOrPath) {
    return urlOrPath;
  }
  
  // Otherwise return the basename
  return path.basename(urlOrPath);
};

module.exports = {
  deleteFile,
  extractFilenameFromPath
};
