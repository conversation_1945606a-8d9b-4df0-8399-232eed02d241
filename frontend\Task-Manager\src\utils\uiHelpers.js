/**
 * UI Helper functions for consistent styling and behavior across components
 */
import moment from 'moment';

/**
 * Get the appropriate CSS class for task status badges
 * 
 * @param {string} status - Task status value
 * @returns {string} CSS class string for the badge
 */
export const getStatusBadgeColor = (status) => {
  switch (status) {
    case "Completed":
      return "bg-green-100 text-green-500 border border-green-200";
    case "Pending":
      return "bg-purple-100 text-purple-500 border border-purple-200";
    case "In Progress":
      return "bg-blue-100 text-blue-500 border border-blue-200";
    default:
      return "bg-gray-100 text-gray-500 border border-gray-200";
  }
};

/**
 * Get the appropriate CSS class for task priority badges
 * 
 * @param {string} priority - Task priority value
 * @returns {string} CSS class string for the badge
 */
export const getPriorityBadgeColor = (priority) => {
  switch (priority) {
    case "high":
      return "bg-red-100 text-red-500 border border-red-200";
    case "medium":
      return "bg-orange-100 text-orange-500 border border-orange-200";
    case "low":
      return "bg-green-100 text-green-500 border border-green-200";
    default:
      return "bg-gray-100 text-gray-500 border border-gray-200";
  }
};

/**
 * Format a date string with a consistent date format
 * 
 * @param {string|Date} dateString - Date to format
 * @param {string} format - Optional format string (default: "MMM Do, YYYY")
 * @returns {string} Formatted date string
 */
export const formatDate = (dateString, format = "MMM Do, YYYY") => {
  if (!dateString) return "N/A";
  
  try {
    return moment(dateString).format(format);
  } catch (error) {
    console.error("Error formatting date:", error);
    return dateString.toString();
  }
};

/**
 * Truncate a string if it exceeds maxLength
 * 
 * @param {string} str - String to truncate
 * @param {number} maxLength - Maximum length before truncation
 * @returns {string} Truncated string with ellipsis if needed
 */
export const truncateString = (str, maxLength = 50) => {
  if (!str || str.length <= maxLength) return str;
  return `${str.substring(0, maxLength)}...`;
};

/**
 * Generate user initials from name for avatar placeholders
 * 
 * @param {string} name - User's full name
 * @returns {string} Initials (up to 2 characters)
 */
export const getInitials = (name) => {
  if (!name) return "?";
  
  const parts = name.trim().split(" ");
  if (parts.length === 1) return parts[0].charAt(0).toUpperCase();
  
  return (parts[0].charAt(0) + parts[parts.length - 1].charAt(0)).toUpperCase();
}; 