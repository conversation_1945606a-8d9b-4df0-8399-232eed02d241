import React, { useState, useContext, useEffect } from "react";
import { Link, useNavigate } from "react-router-dom";
import AuthLayout from "../../components/layout/AuthLayout";
import Input from "../../components/Inputs/Input";
import { UserContext } from "../../contexts/userContext";
import axiosInstance from "../../utils/axiosInstance";
import { API_PATHS } from "../../utils/apiPaths";
import { LuRefreshCw } from "react-icons/lu";
import { validateEmail, validatePassword } from "../../utils/helper";
import logger from "../../utils/logger";

function Login() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(false);
  const {updateUser} = useContext(UserContext);

  const navigate = useNavigate();
  
  // Effect to clear error message after 3 seconds
  useEffect(() => {
    if (error) {
      const timer = setTimeout(() => {
        setError("");
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [error]);

  // handle Login Form Submit
  const handleLogin = async (e) => {
    e.preventDefault();

    // Validate email
    if (!validateEmail(email)) {
      setError("Please enter a valid email address.");
      return;
    }

    // Validate password with enhanced validation
    const passwordValidation = validatePassword(password);
    if (!passwordValidation.isValid) {
      setError(passwordValidation.message);
      return;
    }

    setError("");
    setLoading(true);

    // Login API Call
    try {
      const response = await axiosInstance.post(API_PATHS.AUTH.LOGIN, {
        email,
        password,
      });

      if (response.status === 200 && response.data) {
        const { role } = response.data;
        updateUser({
          ...response.data,
          profileImageUrl: response.data.profileImageUrl,
        });
        window.location.href = role === 'admin' ? '/admin/dashboard' : '/user/dashboard';
      } else {
        const errorMessage = response.data?.message || 'Authentication failed';
        setError(errorMessage);
      }
    } catch (err) {
      const errorMessage =
        err.response?.data?.message ||
        (err.code === 'ERR_NETWORK' 
          ? 'Network error. Please check if the server is running.' 
          : 'Something went wrong. Please try again.');
      setError(errorMessage);
      logger.error('Login error:', err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <AuthLayout>
      <div className="w-full max-w-sm mx-auto">
        <div className="text-center mb-8">
          <h3 className="text-2xl font-semibold text-gray-900">Welcome Back</h3>
          <p className="text-sm text-slate-600 mt-1">
            Please enter your details to log in
          </p>
        </div>

        <form onSubmit={handleLogin} className="space-y-6">
          <Input
            label="Email Address"
            type="email"
            name="email"
            placeholder="<EMAIL>"
            value={email}
            onChange={({ target }) => setEmail(target.value)}
          />

          <Input
            label="Password"
            type="password"
            name="password"
            placeholder="Minimum 8 characters"
            value={password}
            onChange={({ target }) => setPassword(target.value)}
          />

          {/* Reserve space for error message to prevent layout shift */}
          <div className="h-4 flex items-center justify-center">
            {error && <p className="text-red-500 text-xs text-center">{error}</p>}
          </div>

          <button
            type="submit"
            className="auth-btn flex items-center justify-center"
            disabled={loading}
          >
            {loading ? (
              <>
                <LuRefreshCw className="animate-spin mr-2" />
                Processing...
              </>
            ) : (
              "Log In"
            )}
          </button>
        </form>

        <p className="text-sm text-slate-700 mt-6 text-center">
          Don't have an account?{" "}
          <Link
            className="font-medium text-primary hover:underline"
            to="/signup"
          >
            Sign Up
          </Link>
        </p>
      </div>
    </AuthLayout>
  );
}

export default Login;
