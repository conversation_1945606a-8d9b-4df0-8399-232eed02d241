import React from 'react';
import { useSocket } from '../../contexts/SocketContext';
import { useNotifications } from '../../contexts/NotificationContext';

const SocketDebug = () => {
  const { 
    socket, 
    connected, 
    connectedUsers, 
    unreadChatCount, 
    isUserOnline 
  } = useSocket();
  
  const { 
    notifications, 
    unreadCount: notificationUnreadCount 
  } = useNotifications();

  return (
    <div className="fixed bottom-4 right-4 bg-white border border-gray-300 rounded-lg p-4 shadow-lg max-w-sm z-50">
      <h3 className="font-bold text-sm mb-2">Socket Debug</h3>
      
      <div className="text-xs space-y-1">
        <div>
          <strong>Socket Connected:</strong> 
          <span className={connected ? 'text-green-600' : 'text-red-600'}>
            {connected ? ' ✅ Yes' : ' ❌ No'}
          </span>
        </div>
        
        <div>
          <strong>Socket ID:</strong> {socket?.id || 'None'}
        </div>
        
        <div>
          <strong>Online Users:</strong> {connectedUsers.length}
          {connectedUsers.length > 0 && (
            <div className="ml-2 text-gray-600">
              {connectedUsers.slice(0, 3).join(', ')}
              {connectedUsers.length > 3 && '...'}
            </div>
          )}
        </div>
        
        <div>
          <strong>Chat Unread:</strong> {unreadChatCount}
        </div>
        
        <div>
          <strong>Notification Unread:</strong> {notificationUnreadCount}
        </div>
        
        <div>
          <strong>Total Notifications:</strong> {notifications.length}
        </div>
        
        <div>
          <strong>Test User Online:</strong>
          <span className={isUserOnline('68495a4be3fd9de62d8a3ac') ? 'text-green-600' : 'text-red-600'}>
            {isUserOnline('68495a4be3fd9de62d8a3ac') ? ' ✅ Yes' : ' ❌ No'}
          </span>
        </div>

        <div>
          <strong>Recent Notifications:</strong>
          {notifications.slice(0, 2).map(notif => (
            <div key={notif._id} className="ml-2 text-xs text-gray-600 truncate">
              {notif.type}: {notif.message.substring(0, 30)}...
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default SocketDebug;
