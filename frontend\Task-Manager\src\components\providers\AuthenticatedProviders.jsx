import React from 'react';
import { SocketProvider } from '../../contexts/SocketContext';
import { useUser } from '../../contexts/userContext';

/**
 * Wrapper component that provides Socket context
 * only for authenticated routes
 */
const AuthenticatedProviders = React.memo(({ children }) => {
  const { user, loading } = useUser();

  // Don't render socket provider until user is loaded and authenticated
  if (loading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin rounded-full h-16 w-16 border-t-4 border-blue-500"></div>
      </div>
    );
  }

  // Ensure we have a valid user and token before initializing the socket
  if (!user || !user._id) {
    return children; // Render children without socket context
  }

  // Additional check to ensure we have a valid token
  const token = localStorage.getItem('token');
  if (!token) {
    return children; // Render children without socket context
  }

  // User is authenticated, wrap with SocketProvider
  return (
    <SocketProvider>
      {children}
    </SocketProvider>
  );
});

AuthenticatedProviders.displayName = 'AuthenticatedProviders';

export default AuthenticatedProviders;
