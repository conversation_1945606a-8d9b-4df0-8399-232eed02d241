import React from "react";
import { BASE_URL } from "../../utils/apiPaths";
import { LuTrash2, LuMessageCircle } from "react-icons/lu";
import ProxyImage from "../common/ProxyImage"; // Import ProxyImage
import { useNavigate } from "react-router-dom";

const UserCard = ({ userInfo, onDelete }) => {
  const navigate = useNavigate();

  // Helper function to check if user has a valid profile image
  const hasValidProfileImage = (user) => {
    return user?.profileImageUrl &&
           user.profileImageUrl !== null &&
           user.profileImageUrl !== 'null' &&
           user.profileImageUrl.trim() !== '';
  };

  // Get user initials
  const getUserInitials = (user) => {
    return user?.name?.charAt(0)?.toUpperCase() || 'U';
  };

  // Get properly formatted image URL
  const getImageUrl = (user) => {
    if (!hasValidProfileImage(user)) return null;

    const imageUrl = user.profileImageUrl;

    // If it's already a full URL, return as is
    if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
      return imageUrl;
    }

    // If it's a relative URL, prepend the base URL
    if (imageUrl.startsWith('/')) {
      return `${BASE_URL}${imageUrl}`;
    }

    // Otherwise, assume it needs /uploads/ prefix
    return `${BASE_URL}/uploads/${imageUrl}`;
  };



  const handleMessageClick = () => {





    // Navigate to chat with this user using React Router for a seamless SPA experience
    navigate(`/chat?userId=${userInfo?._id}`);
  };
  
  return (
    <div className="user-card p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow">
      <div className="space-y-4">
        {/* User Info Row */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="relative flex-shrink-0">
              {hasValidProfileImage(userInfo) ? (
                <div className="relative">
                  <img
                    src={getImageUrl(userInfo)}
                    alt={`${userInfo?.name} avatar`}
                    className="w-10 h-10 rounded-full border-2 border-white object-cover"
                    onError={(e) => {
                      // Hide the image and show fallback
                      e.target.parentElement.style.display = 'none';
                      e.target.parentElement.nextSibling.style.display = 'flex';
                    }}
                  />
                </div>
              ) : null}

              {/* Fallback avatar - always present but hidden when image loads */}
              <div
                className="w-10 h-10 rounded-full bg-blue-500 text-white flex items-center justify-center font-medium text-lg border-2 border-white"
                style={{ display: hasValidProfileImage(userInfo) ? 'none' : 'flex' }}
              >
                {getUserInitials(userInfo)}
              </div>
            </div>
            <div className="min-w-0">
              <p className="text-sm font-medium">{userInfo?.name}</p>
              <p className="text-xs text-gray-500">{userInfo?.email}</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={handleMessageClick}
              className="text-blue-500 hover:text-blue-700 transition-colors p-1"
              title="Message user"
            >
              <LuMessageCircle size={18} />
            </button>
            
            {onDelete && (
              <button
                onClick={() => onDelete(userInfo)}
                className="text-red-500 hover:text-red-700 transition-colors p-1"
                title="Delete user"
              >
                <LuTrash2 size={18} />
              </button>
            )}
          </div>
        </div>

        {/* Stats Row */}
        <div className="grid grid-cols-3 gap-2 pt-2 border-t border-gray-100">
          <StatCard
            label="Pending"
            count={userInfo?.pendingTasks || 0}
            status="Pending"
          />
          <StatCard
            label="In Progress"
            count={userInfo?.inProgressTasks || 0}
            status="In Progress"
          />
          <StatCard
            label="Completed"
            count={userInfo?.completedTasks || 0}
            status="Completed"
          />
        </div>
      </div>
    </div>
  );
};

const StatCard = ({ label, count, status }) => {
  const getStatusTagColor = () => {
    switch (status) {
      case "In Progress":
        return "text-cyan-500 bg-cyan-50";
      case "Completed":
        return "text-indigo-500 bg-indigo-50";
      default: // Pending
        return "text-violet-500 bg-violet-50";
    }
  };

  return (
    <div
      className={`text-center text-[10px] font-medium ${getStatusTagColor()} px-2 py-1 rounded whitespace-nowrap`}
    >
      <div className="text-[12px] font-semibold">{count}</div>
      <div className="text-[10px] truncate">{label}</div>
    </div>
  );
};

export default UserCard;
