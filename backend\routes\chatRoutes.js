const express = require('express');
const router = express.Router();
const { enhancedProtect } = require('../middleware/enhancedAuthMiddleware');
const Conversation = require('../models/Conversation');
const Message = require('../models/Message');
const User = require('../models/User');

// Get all conversations for current user
router.get('/conversations', enhancedProtect, async (req, res) => {
  try {
    console.log(`Fetching conversations for user: ${req.user._id}`);
    
    // Find all conversations where the current user is a participant
    const conversations = await Conversation.find({
      participants: req.user._id,
      isActive: true
    })
      .populate({
        path: 'participants',
        select: 'name profileImageUrl status lastActive email'
      })
      .populate({
        path: 'lastMessage',
        select: 'text sender createdAt readBy'
      })
      .populate({
        path: 'createdBy',
        select: 'name'
      })
      .sort({ updatedAt: -1 });

    console.log(`Found ${conversations.length} conversations for user ${req.user._id}`);
    
    // Enhanced debugging: Check participant data quality
    const participantCheckResult = conversations.map(conv => {
      const participantDetails = conv.participants.map(p => ({
        id: p?._id,
        name: p?.name,
        email: p?.email,
        hasName: !!p?.name,
        hasEmail: !!p?.email,
        hasValidData: !!(p?.name || p?.email)
      }));

      return {
        id: conv._id,
        type: conv.type,
        participantCount: conv.participants.length,
        hasAllParticipantsData: conv.participants.every(p => p && p._id && (p.name || p.email)),
        participantDetails
      };
    });
    console.log('Enhanced participant population check:', JSON.stringify(participantCheckResult, null, 2));

    // For each conversation, count unread messages for the current user
    const conversationsWithUnreadCount = await Promise.all(
      conversations.map(async (conversation) => {
        const unreadCount = await Message.countDocuments({
          conversationId: conversation._id,
          'readBy.user': { $ne: req.user._id },
          sender: { $ne: req.user._id }
        });

        // Convert to POJO and add unread count
        const conversationObj = conversation.toObject();
        conversationObj.unreadCount = unreadCount;
        
        return conversationObj;
      })
    );

    res.json({ success: true, conversations: conversationsWithUnreadCount });
  } catch (error) {
    console.error('Error in /conversations route:', error);
    res.status(500).json({ success: false, message: 'Server error', error: error.message });
  }
});

// Get or create individual conversation between current user and another user
router.post('/conversations/individual/:userId', enhancedProtect, async (req, res) => {
  try {
    const otherUserId = req.params.userId;
    const currentUserId = req.user._id;

    if (currentUserId.equals(otherUserId)) {
      return res.status(400).json({ success: false, message: 'Cannot create a conversation with yourself.' });
    }

    // Find or create conversation
    const conversation = await Conversation.findOrCreateIndividualConversation(
      currentUserId,
      otherUserId
    );

    // Enhanced population to ensure we have complete user data
    await conversation.populate([
        {
          path: 'participants',
          select: 'name profileImageUrl email status lastActive role',
          options: {
            // Ensure we get all participant data even if some fields are missing
            lean: false
          }
        },
        {
          path: 'lastMessage',
          populate: {
            path: 'sender',
            select: 'name profileImageUrl email'
          }
        }
    ]);

    // Validate participant data
    const invalidParticipants = conversation.participants.filter(p => !p || (!p.name && !p.email));
    if (invalidParticipants.length > 0) {
      console.warn('Found participants with missing name/email data:', {
        conversationId: conversation._id,
        invalidParticipants: invalidParticipants.map(p => ({ id: p?._id, name: p?.name, email: p?.email }))
      });
    }

    res.json({ success: true, conversation });
  } catch (error) {
    console.error('Error in individual conversation:', error);
    res.status(500).json({ success: false, message: 'Server error', error: error.message });
  }
});

// Create group conversation
router.post('/conversations/group', enhancedProtect, async (req, res) => {
  try {
    const { name, participantIds } = req.body;
    
    if (!name) {
      return res.status(400).json({ success: false, message: 'Group name is required' });
    }
    
    if (!participantIds || !Array.isArray(participantIds) || participantIds.length < 2) {
      return res.status(400).json({ 
        success: false, 
        message: 'At least 2 other participants are required for a group' 
      });
    }
    
    // Make sure current user is included in participants
    const allParticipants = [req.user._id, ...participantIds];
    
    // Create new group conversation
    const groupConversation = new Conversation({
      type: 'group',
      name,
      participants: allParticipants,
      createdBy: req.user._id
    });
    
    await groupConversation.save();
    
    // Populate user details
    await groupConversation.populate({
      path: 'participants',
      select: 'name profileImageUrl status lastActive'
    });
    
    res.status(201).json({ success: true, conversation: groupConversation });
  } catch (error) {
    console.error(error);
    res.status(500).json({ success: false, message: 'Server error', error: error.message });
  }
});

// Get messages for a specific conversation
router.get('/conversations/:conversationId/messages', enhancedProtect, async (req, res) => {
  try {
    const { conversationId } = req.params;
    const { limit = 30, before } = req.query;
    
    // Check if conversation exists and user is a participant
    const conversation = await Conversation.findOne({
      _id: conversationId,
      participants: req.user._id
    });
    
    if (!conversation) {
      return res.status(404).json({ 
        success: false, 
        message: 'Conversation not found or you are not a participant' 
      });
    }
    
    // Build query for messages
    const query = { conversationId };
    
    // If before parameter is provided, get messages before that timestamp
    if (before) {
      query.createdAt = { $lt: new Date(before) };
    }
    
    // Get messages
    const messages = await Message.find(query)
      .sort({ createdAt: -1 })
      .limit(parseInt(limit))
      .populate({
        path: 'sender',
        select: 'name profileImageUrl'
      });
    
    // Mark messages as read
    const messagesToMark = messages.filter(
      msg => !msg.isReadBy(req.user._id) && msg.sender.toString() !== req.user._id.toString()
    );
    
    await Promise.all(messagesToMark.map(msg => msg.markAsRead(req.user._id)));
    
    res.json({ success: true, messages: messages.reverse() });
  } catch (error) {
    console.error(error);
    res.status(500).json({ success: false, message: 'Server error', error: error.message });
  }
});

// Send a new message (for non-socket fallback)
router.post('/conversations/:conversationId/messages', enhancedProtect, async (req, res) => {
  try {
    const { conversationId } = req.params;
    const { text, attachments = [], taskReference = null } = req.body;
    
    // Check if conversation exists and user is a participant
    const conversation = await Conversation.findOne({
      _id: conversationId,
      participants: req.user._id
    });
    
    if (!conversation) {
      return res.status(404).json({ 
        success: false, 
        message: 'Conversation not found or you are not a participant' 
      });
    }
    
    if (!text && (!attachments || attachments.length === 0)) {
      return res.status(400).json({ 
        success: false, 
        message: 'Message must contain text or attachments' 
      });
    }
    
    // Create new message
    const newMessage = new Message({
      conversationId,
      sender: req.user._id,
      text,
      attachments,
      taskReference,
      readBy: [{ user: req.user._id }]
    });
    
    await newMessage.save();
    
    // Update conversation's last message
    conversation.lastMessage = newMessage._id;
    await conversation.save();
    
    // Populate sender info
    await newMessage.populate({
      path: 'sender',
      select: 'name profileImageUrl'
    });
    
    res.status(201).json({ success: true, message: newMessage });
  } catch (error) {
    console.error(error);
    res.status(500).json({ success: false, message: 'Server error', error: error.message });
  }
});

// Clear/Delete conversation messages for current user
router.delete('/conversations/:conversationId/clear', enhancedProtect, async (req, res) => {
  try {
    const { conversationId } = req.params;
    const currentUserId = req.user._id;

    // Check if conversation exists and user is a participant
    const conversation = await Conversation.findOne({
      _id: conversationId,
      participants: currentUserId
    });

    if (!conversation) {
      return res.status(404).json({
        success: false,
        message: 'Conversation not found or you are not a participant'
      });
    }

    // Delete all messages in this conversation
    // Note: This deletes messages for all participants, not just the current user
    // If you want user-specific deletion, you'd need to add a "deletedBy" field to messages
    const deleteResult = await Message.deleteMany({ conversationId });

    // Clear the lastMessage reference in the conversation
    conversation.lastMessage = null;
    await conversation.save();

    console.log(`Cleared ${deleteResult.deletedCount} messages from conversation ${conversationId} for user ${currentUserId}`);

    res.json({
      success: true,
      message: `Conversation cleared successfully. ${deleteResult.deletedCount} messages deleted.`,
      deletedCount: deleteResult.deletedCount
    });
  } catch (error) {
    console.error('Error clearing conversation:', error);
    res.status(500).json({ success: false, message: 'Server error', error: error.message });
  }
});

module.exports = router;