import React, { useState, useContext, useEffect, useMemo, useCallback } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import SideMenu from "./SideMenu";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>ser, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "react-icons/lu";
import { UserContext } from "../../contexts/userContext";
import ProfileSettingsModal from "../modals/ProfileSettingsModal";
import ProxyImage from "../common/ProxyImage";
import { SIDE_MENU_DATA, SIDE_MENU_USER_DATA } from "../../utils/data";
import { useSocket } from "../../contexts/SocketContext";
import { useNotifications } from "../../contexts/NotificationContext";
import NotificationDropdown from "../notifications/NotificationDropdown";


const DashboardLayout = ({ children }) => {
  const location = useLocation();
  const navigate = useNavigate();

  // User & role helpers
  const { user, clearUser } = useContext(UserContext);
  
  const isAdmin = user?.role === 'admin';

  // Compute active side-menu label from current pathname so the highlight state is always in sync
  const menuData = useMemo(() => (isAdmin ? SIDE_MENU_DATA : SIDE_MENU_USER_DATA), [isAdmin]);
  const activeMenu = useMemo(() => {
    const match = menuData.find((item) => location.pathname.startsWith(item.path));
    return match ? match.label : "";
  }, [location.pathname, menuData]);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [profileDropdownOpen, setProfileDropdownOpen] = useState(false);
  const [profileSettingsModalOpen, setProfileSettingsModalOpen] = useState(false);
  const [notificationDropdownOpen, setNotificationDropdownOpen] = useState(false);
  
  const { unreadChatCount } = useSocket();
  const { unreadCount: notificationUnreadCount, notifications, updateTrigger } = useNotifications();
  

  const dropdownRef = React.useRef(null);
  const isOnChatPage = location.pathname === '/chat';

  const totalNotificationsCount = useMemo(() => {
    return notificationUnreadCount + (isOnChatPage ? 0 : unreadChatCount);
  }, [notificationUnreadCount, unreadChatCount, isOnChatPage]);

  React.useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setProfileDropdownOpen(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  

  

  const getPageTitle = useCallback(() => {
    switch (activeMenu) {
      case "dashboard": return "Dashboard";
      case "tasks": return "Tasks";
      case "users": return "User Management";
      case "settings": return "Settings";
      case "chat": return "Chat";
      default: return "Dashboard";
    }
  }, [activeMenu]);
  
  // Handle logout - memoized to prevent unnecessary re-creation
  const handelLogout = useCallback(() => {
    localStorage.clear();
    clearUser();
    navigate("/login");
  }, [clearUser, navigate]);
  
  // User profile image for dropdown - using useMemo to prevent hooks order issues
  const userProfileImageDropdown = useMemo(() => {
    if (user?.profileImageUrl) {
      return (
        <div className="flex-shrink-0 h-10 w-10 rounded-full bg-primary overflow-hidden">
          <ProxyImage
            src={user.profileImageUrl}
            alt={user?.name || "User"}
            className="w-full h-full object-cover"
            fallbackText={user?.name || "U"}
            containerClassName="w-full h-full"
          />
        </div>
      );
    } else {
      return (
        <div className="flex-shrink-0 h-10 w-10 rounded-full bg-primary flex items-center justify-center overflow-hidden text-white">
          <div className="w-full h-full flex items-center justify-center">
            {user?.name ? user.name.charAt(0).toUpperCase() : "U"}
          </div>
        </div>
      );
    }
  }, [user?.profileImageUrl, user?.id, user?.name]);

  useEffect(() => {
    const currentPath = location.pathname;
    const menuData = isAdmin ? SIDE_MENU_DATA : SIDE_MENU_USER_DATA;
    
    const activeItem = menuData.find(item => {
      // Special handling for logout path, as it's not a direct route
      if (item.path === "logout") {
        return false; // Logout is not an active menu item
      }
      return currentPath.startsWith(item.path);
    });
  }, [location.pathname, isAdmin]);

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Desktop Sidebar */}
      <div className="hidden md:block">
        <SideMenu isAdmin={isAdmin} activeMenu={activeMenu} />
      </div>

      {/* Mobile Sidebar - shown when menu is open */}
      {mobileMenuOpen && (
        <div className="fixed inset-0 z-40 md:hidden">
          {/* Backdrop */}
          <div 
            className="fixed inset-0 bg-gray-600 bg-opacity-75" 
            onClick={() => setMobileMenuOpen(false)}
          ></div>
          
          {/* Sidebar */}
          <div className="relative flex-1 flex flex-col max-w-xs w-full bg-white">
            <div className="absolute top-0 right-0 -mr-12 pt-2">
              <button
                className="ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white"
                onClick={() => setMobileMenuOpen(false)}
              >
                <span className="sr-only">Close sidebar</span>
                <svg className="h-6 w-6 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <SideMenu isAdmin={isAdmin} activeMenu={activeMenu} />
          </div>
        </div>
      )}

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Top Navigation */}
        <header className="relative bg-white shadow-sm z-30">
          <div className="px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between h-16">
              <div className="flex items-center">
                {/* Mobile menu button */}
                <button
                  type="button"
                  className="md:hidden inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none"
                  onClick={() => setMobileMenuOpen(true)}
                >
                  <span className="sr-only">Open sidebar</span>
                  <LuMenu className="block h-6 w-6" aria-hidden="true" />
                </button>
                
                {/* Page title */}
                <div className="flex-shrink-0 flex items-center ml-4 md:ml-0">
                  <h1 className="text-xl font-semibold text-gray-900">{getPageTitle()}</h1>
                </div>

                {/* Search bar - new addition */}
                <div className="hidden md:block ml-8">
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <LuSearch className="h-4 w-4 text-gray-400" />
                    </div>
                    <input
                      type="text"
                      placeholder="Search..."
                      className="block w-full pl-10 pr-3 py-2 border border-gray-200 rounded-lg text-sm placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                </div>
              </div>
              
              {/* User profile and notifications */}
              <div className="flex items-center space-x-4">
                {/* Notifications */}
                <div className="relative">
                  <button
                    onClick={() => setNotificationDropdownOpen(!notificationDropdownOpen)}
                    className="p-2 rounded-full text-gray-400 hover:text-gray-500 hover:bg-gray-100 transition-colors"
                  >
                    <span className="sr-only">View notifications</span>
                    <LuBell className="h-6 w-6" aria-hidden="true" />
                    {totalNotificationsCount > 0 && (
                      <span className="absolute top-0 right-0 h-4 w-4 rounded-full bg-red-500 text-xs text-white font-bold flex items-center justify-center transform translate-x-1 -translate-y-1">
                        {totalNotificationsCount > 9 ? '9+' : totalNotificationsCount}
                      </span>
                    )}
                  </button>

                  {/* Notification Dropdown */}
                  <NotificationDropdown
                    isOpen={notificationDropdownOpen}
                    onClose={() => setNotificationDropdownOpen(false)}
                  />
                </div>
                
                {/* Profile dropdown */}
                <div className="relative" ref={dropdownRef}>
                  <div 
                    className="flex items-center cursor-pointer"
                    onClick={() => setProfileDropdownOpen(!profileDropdownOpen)}
                  >
                    <div className="relative">
                    {user?.profileImageUrl ? (
                        <div className="h-9 w-9 rounded-full overflow-hidden border border-gray-200 shadow-sm">
                        <ProxyImage 
                          src={user.profileImageUrl}
                          alt="Profile"
                          className="w-full h-full object-cover"
                          fallbackText={user?.name || "U"}
                          containerClassName="w-full h-full"
                        />
                      </div>
                    ) : (
                        <div className="h-9 w-9 rounded-full bg-gradient-to-r from-blue-500 to-indigo-600 flex items-center justify-center overflow-hidden border border-gray-200 shadow-sm text-white text-xs font-bold">
                        <div className="w-full h-full flex items-center justify-center">
                          {user?.name ? user.name.charAt(0).toUpperCase() : "U"}
                        </div>
                      </div>
                    )}
                      
                      {/* Online status indicator - new addition */}
                      <div className="absolute bottom-0 right-0 h-3 w-3 rounded-full bg-green-500 border-2 border-white"></div>
                    </div>
                    
                    <div className="ml-2 hidden sm:block">
                      <p className="text-sm font-medium text-gray-700">{user?.name || "User"}</p>
                      <p className="text-xs text-gray-500">{isAdmin ? "Administrator" : "User"}</p>
                    </div>
                  </div>
                  
                  {/* Dropdown Menu */}
                  {profileDropdownOpen && (
                    <div className="absolute right-0 mt-2 w-64 bg-white rounded-lg shadow-xl py-1 z-50 border border-gray-100">
                      {/* User Info */}
                      <div className="px-4 py-3 border-b border-gray-100">
                        <div className="flex items-center">
                          {userProfileImageDropdown}
                          <div className="ml-3">
                            <div className="flex items-center">
                            <p className="text-sm font-medium text-gray-900">{user?.name || "User"}</p>
                              <span className="ml-2 px-2 py-0.5 text-xs rounded-full bg-green-100 text-green-800">Online</span>
                            </div>
                            <p className="text-xs text-gray-500 truncate">{user?.email || ""}</p>
                          </div>
                        </div>
                      </div>
                      
                      {/* Menu Items */}
                      <button 
                        onClick={() => {
                          setProfileSettingsModalOpen(true);
                          setProfileDropdownOpen(false);
                        }}
                        className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors flex items-center"
                      >
                        <svg className="mr-2 h-4 w-4 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                        My Profile
                      </button>
                      
                      <button 
                        onClick={() => {
                          setProfileSettingsModalOpen(true);
                          setProfileDropdownOpen(false);
                        }}
                        className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors flex items-center"
                      >
                        <svg className="mr-2 h-4 w-4 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                        </svg>
                        Settings
                      </button>
                      
                      <div className="border-t border-gray-100 my-1"></div>
                      
                      <button 
                        onClick={handelLogout}
                        className="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors flex items-center">
                        <svg className="mr-2 h-4 w-4 text-red-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                        </svg>
                        Logout
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </header>

        {/* Page Content */}
        <main className="flex-1 overflow-auto bg-gray-50 p-4 sm:p-6 lg:p-8">
        {/* Profile Settings Modal */}
        <ProfileSettingsModal 
          isOpen={profileSettingsModalOpen} 
          onClose={() => setProfileSettingsModalOpen(false)} 
        />
          
          {children}
        </main>
      </div>
    </div>
  );
};

export default DashboardLayout;
