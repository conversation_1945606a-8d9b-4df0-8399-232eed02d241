import React from 'react';

const FAQItem = ({ question, answer, isOpen, onToggle, id }) => {
  return (
    <div className="bg-white rounded-lg shadow-sm overflow-hidden">
      <button
        className="w-full flex justify-between items-center p-5 text-left focus:outline-none"
        onClick={onToggle}
        aria-expanded={isOpen}
        aria-controls={`faq-answer-${id}`}
      >
        <span className="text-slate-800 text-[15px]">{question}</span>
        <span className={`ml-4 text-xl transition-colors ${isOpen ? 'text-emerald-500' : 'text-slate-400'}`}>
          {isOpen ? '−' : '+'}
        </span>
      </button>
      {isOpen && (
        <div 
          id={`faq-answer-${id}`}
          className="px-5 pb-5"
        >
          <p className="text-slate-600 text-sm leading-relaxed">{answer}</p>
        </div>
      )}
    </div>
  );
};

const FAQ = ({ faqData, openFAQ, handleToggle }) => {
  // Split the FAQ data into two columns
  const midPoint = Math.ceil(faqData.length / 2);
  const leftColumnItems = faqData.slice(0, midPoint);
  const rightColumnItems = faqData.slice(midPoint);

  return (
    <section className="py-20 bg-emerald-50/30">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <h2 className="text-2xl sm:text-3xl font-bold text-center mb-12">
          Frequently asked<br />Questions
        </h2>
        
        <div className="max-w-6xl mx-auto grid md:grid-cols-2 gap-4 md:gap-6">
          {/* Left Column */}
          <div className="space-y-4">
            {leftColumnItems.map((item) => (
              <FAQItem
                key={item.id}
                id={`left-${item.id}`}
                question={item.question}
                answer={item.answer}
                isOpen={openFAQ === `left-${item.id}`}
                onToggle={() => handleToggle(`left-${item.id}`)}
              />
            ))}
          </div>

          {/* Right Column */}
          <div className="space-y-4">
            {rightColumnItems.map((item) => (
              <FAQItem
                key={item.id}
                id={`right-${item.id}`}
                question={item.question}
                answer={item.answer}
                isOpen={openFAQ === `right-${item.id}`}
                onToggle={() => handleToggle(`right-${item.id}`)}
              />
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default FAQ;
