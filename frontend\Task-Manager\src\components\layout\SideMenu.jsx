import React, { useContext, useEffect, useState, useCallback } from "react";
import { SIDE_MENU_DATA, SIDE_MENU_USER_DATA } from "../../utils/data";
import { UserContext } from "../../contexts/userContext";
import { useNavigate, Link, useLocation } from "react-router-dom";
import { LuUser } from "react-icons/lu";
import ProxyImage from "../common/ProxyImage";
import appLogo from "../../assets/logos/Logo (2).png";
import { useSocket } from "../../contexts/SocketContext";

const SideMenu = ({ activeMenu }) => {
  const { user, clearUser } = useContext(UserContext);
  const { unreadChatCount } = useSocket();
  const [sideMenuData, setSideMenuData] = useState([]);

  const navigate = useNavigate();
  const location = useLocation();

  // Check if user is on chat page
  const isOnChatPage = location.pathname === '/chat';

  // Memoize navigation handler to prevent unnecessary re-renders
  const handleClick = useCallback((route) => {
    if (route === "logout") {
      handelLogout();
      return;
    }
    navigate(route);
  }, [navigate]);

  // Memoize logout handler to prevent unnecessary re-renders
  const handelLogout = useCallback(() => {
    localStorage.clear();
    clearUser();
    navigate("/login");
  }, [clearUser, navigate]);

  useEffect(() => {
    if (user) {
      setSideMenuData(
        user?.role === "admin" ? SIDE_MENU_DATA : SIDE_MENU_USER_DATA
      );
    }
    return () => {};
  }, [user]);

  // Memoize the user profile section to prevent unnecessary re-renders
  const ProfileSection = React.useMemo(() => {
    return (
      <div className="flex flex-col items-center justify-center pt-5">
        <div className="relative">
          {user?.profileImageUrl ? (
            <div className="w-20 h-20 rounded-full overflow-hidden border-2 border-gray-200">
              <ProxyImage
                src={user.profileImageUrl}
                alt="Profile"
                className="w-full h-full object-cover"
                fallbackText={user?.name || "U"}
                containerClassName="w-full h-full"
              />
            </div>
          ) : (
            <div className="w-20 h-20 bg-primary rounded-full flex items-center justify-center text-white text-2xl font-bold">
              {user?.name ? user.name.charAt(0).toUpperCase() : "U"}
            </div>
          )}
        </div>
        
        {user?.role === "admin" && (
          <div className="text-[10px] font-medium text-white bg-primary px-3 py-0.5 rounded mt-1">
            Admin
          </div>
        )}

        <h5 className="text-gray-950 font-medium leading-6 mt-3">
          {user?.name || ""}
        </h5>
        <p className="text-[12px] text-gray-500">{user?.email || ""}</p>
      </div>
    );
  }, [user?.profileImageUrl, user?.id, user?.name, user?.email, user?.role]);

  return (
    <div className="w-64 h-screen bg-white border-r border-gray-200/50 z-20">
      {/* Logo */}
      <div className="px-6 py-4">
        <Link to="/" className="block">
          <img 
            src={appLogo} 
            alt="Taskify Logo" 
            className="h-16 w-auto" 
          />
        </Link>
      </div>
      {ProfileSection}

      {sideMenuData.map((item, index) => (
        <button
          key={`menu_${index}`}
          className={`w-full flex items-center gap-4 text-[15px] ${
            activeMenu === item.label
              ? "text-primary bg-linear-to-r from-blue-50/40 to-blue-100/50 border-r-3"
              : ""
          } py-3 px-6 mb-3 cursor-pointer relative`}
          onClick={() => handleClick(item.path)}
        >
          {item.icon && <item.icon className="text-xl" />}
          {item.label}
          
          {/* Notification Indicator for Chat - only show if not on chat page */}
          {item.label === "Chat" && unreadChatCount > 0 && !isOnChatPage && (
            <span className="absolute right-6 top-1/2 -translate-y-1/2 h-5 w-5 bg-red-500 text-white text-xs font-bold rounded-full flex items-center justify-center">
              {unreadChatCount > 9 ? '9+' : unreadChatCount}
            </span>
          )}
        </button>
      ))}
    </div>
  );
};

// Memoize the SideMenu component to prevent unnecessary re-renders
export default React.memo(SideMenu, (prevProps, nextProps) => {
  // Re-render only if activeMenu or user data changes
  return (
    prevProps.activeMenu === nextProps.activeMenu &&
    prevProps.user?.profileImageUrl === nextProps.user?.profileImageUrl &&
    prevProps.user?.name === nextProps.user?.name &&
    prevProps.user?.email === nextProps.user?.email &&
    prevProps.user?.role === nextProps.user?.role
  );
});
