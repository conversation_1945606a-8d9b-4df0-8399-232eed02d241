/**
 * Utility functions for handling errors consistently across the application
 */

/**
 * Generic server error handler for controller functions
 *
 * @param {object} res - Express response object
 * @param {Error} error - Error object
 * @param {number} statusCode - Optional custom status code (defaults to 500)
 */
const handleServerError = (res, error, statusCode = 500) => {
  // Log error details for debugging (only in development)
  const isDev = process.env.NODE_ENV === 'development';

  if (isDev) {
    console.error(`Server Error: ${error.message}`);
    console.error(error.stack);
  }

  res.status(statusCode).json({
    success: false,
    message: "Server error",
    error: isDev ? error.message : undefined,
    stack: isDev ? error.stack : undefined
  });
};

/**
 * Handle validation errors (e.g. Mongoose validation errors)
 * 
 * @param {object} res - Express response object
 * @param {Error} error - Error object with validation details
 */
const handleValidationError = (res, error) => {
  // Extract validation error messages
  const errors = {};
  if (error.errors) {
    Object.keys(error.errors).forEach(key => {
      errors[key] = error.errors[key].message;
    });
  }
  
  res.status(400).json({
    success: false,
    message: "Validation error",
    errors
  });
};

module.exports = {
  handleServerError,
  handleValidationError
}; 