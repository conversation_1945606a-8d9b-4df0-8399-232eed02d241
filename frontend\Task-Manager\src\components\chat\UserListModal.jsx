import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { LuX } from 'react-icons/lu';
import { BASE_URL } from '../../utils/apiPaths';

const UserListModal = ({ isOpen, onClose, onSelectUser }) => {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    if (isOpen) {
      const fetchUsers = async () => {
        setLoading(true);
        setError('');
        try {
          const token = localStorage.getItem('token');
          const config = {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          };
          const { data } = await axios.get(`${BASE_URL}/api/users`, config);
          setUsers(data);
        } catch (err) {
          setError('Failed to fetch users. Please try again.');
          console.error(err);
        }
        setLoading(false);
      };

      fetchUsers();
    }
  }, [isOpen]);

  if (!isOpen) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md p-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">Start a new chat</h2>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-800">
            <LuX className="w-6 h-6" />
          </button>
        </div>
        <div className="user-list-container overflow-y-auto max-h-96">
          {loading && <p>Loading users...</p>}
          {error && <p className="text-red-500">{error}</p>}
          {users.map((user) => (
            <div
              key={user._id}
              className="flex items-center p-3 hover:bg-gray-100 rounded-lg cursor-pointer"
              onClick={() => {
                onSelectUser(user);
                onClose();
              }}
            >
              <img
                src={user.profilePic || `https://api.dicebear.com/6.x/initials/svg?seed=${user.name}`}
                alt={user.name}
                className="w-10 h-10 rounded-full mr-4"
              />
              <div>
                <p className="font-semibold">{user.name}</p>
                <p className="text-sm text-gray-500">{user.email}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default UserListModal; 