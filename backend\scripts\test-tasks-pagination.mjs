// Automated script to test tasks pagination (ESM)
import axios from 'axios';
import { CookieJar } from 'tough-cookie';
import { wrapper } from 'axios-cookiejar-support';
import { BASE_URL, ADMIN_INVITE_TOKEN } from './test-helpers.mjs';
const TEST_USER = {
  email: `paginationtestuser${Date.now()}@example.com`,
  password: 'TestUser123', // letters and digits only, matches backend policy
  name: `Pagination Test User ${Date.now()}`
};

async function main() {
  const jar = new CookieJar();
  const client = wrapper(axios.create({ baseURL: BASE_URL, jar, withCredentials: true }));

  // 0. Fetch CSRF token for registration
  let res = await client.get('/auth/csrf-token');
  let csrfToken = res.data.csrfToken;
  if (!csrfToken) throw new Error('Failed to fetch CSRF token for registration');

  // Register user
  try {
    res = await client.post('/auth/register', { ...TEST_USER, adminInviteToken: ADMIN_INVITE_TOKEN }, { headers: { 'x-csrf-token': csrfToken } });
    console.log('Registered new test user.');
  } catch (e) {
    if (e.response) {
      console.error('Register failed:', e.response.status, e.response.data);
    } else {
      console.error('Register failed:', e.message);
    }
    throw e;
  }

  // Always login after registration to ensure token is set
  res = await client.get('/auth/csrf-token');
  csrfToken = res.data.csrfToken;
  let userId = null;
  try {
    res = await client.post('/auth/login', { email: TEST_USER.email, password: TEST_USER.password }, { headers: { 'x-csrf-token': csrfToken } });
    console.log('Logged in as test user.');
    // Print cookies after login
    const cookies = await jar.getCookies(BASE_URL);
    console.log('Cookies after login:', cookies.map(c => `${c.key}=${c.value}`).join('; '));
    // Extract accessToken from cookies immediately after login
    const cookiesAfterLogin = await jar.getCookies(BASE_URL);
    const accessTokenCookie = cookiesAfterLogin.find(c => c.key === 'accessToken');
    const accessToken = accessTokenCookie ? accessTokenCookie.value : null;
    if (!accessToken) throw new Error('accessToken cookie missing after login');
    // Get userId from login response or fallback to JWT decode
    if (res.data && res.data.user && res.data.user._id) {
      userId = res.data.user._id;
    } else {
      // fallback: decode userId from JWT (accessToken)
      function decodeJWT(token) {
        try {
          const payload = token.split('.')[1];
          return JSON.parse(Buffer.from(payload, 'base64').toString('utf8'));
        } catch (e) { return {}; }
      }
      const decoded = decodeJWT(accessToken);
      userId = decoded.id || decoded._id;
    }
    if (!userId) throw new Error('Failed to get userId for filtering');
  } catch (e2) {
    if (e2.response) {
      console.error('Login failed:', e2.response.status, e2.response.data);
    } else {
      console.error('Login failed:', e2.message);
    }
    throw e2;
  }

  // Extract accessToken from cookies after login
  const cookiesAfterLogin = await jar.getCookies(BASE_URL);
  const accessTokenCookie = cookiesAfterLogin.find(c => c.key === 'accessToken');
  const accessToken = accessTokenCookie ? accessTokenCookie.value : null;
  if (!accessToken) throw new Error('accessToken cookie missing after login');

  // Ensure test isolation: fetch and delete all existing tasks for this user
  res = await client.get(`/tasks?createdBy=${userId}&limit=100`, { headers: { 'Authorization': `Bearer ${accessToken}` } });
  if (res.data.tasks.length > 0) {
    console.log('Cleaning up old tasks for this user:', res.data.tasks.map(t => t.title));
    for (const task of res.data.tasks) {
      try {
        await client.delete(`/tasks/${task._id}`, { headers: { 'Authorization': `Bearer ${accessToken}` } });
      } catch (e) {
        console.warn('Failed to delete task', task._id, e.message);
      }
    }
  }

  // After deletion, fetch and log all remaining tasks for this user
  res = await client.get(`/tasks?createdBy=${userId}&limit=100`, { headers: { 'Authorization': `Bearer ${accessToken}` } });
  if (res.data.tasks.length > 0) {
    console.log('Remaining tasks for this user after deletion:', res.data.tasks.map(t => `${t.title} (${t._id})`));
  } else {
    console.log('No remaining tasks for this user after deletion.');
  }

  // Create 5 tasks for this user
  for (let i = 1; i <= 5; i++) {
    // Fetch new CSRF token for each task creation
    res = await client.get('/auth/csrf-token');
    csrfToken = res.data.csrfToken;
    try {
      await client.post('/tasks', {
        title: `Paginated Task ${i}`,
        description: `Test task ${i}`,
        status: 'todo',
        assignedTo: [],
      }, { headers: { 'x-csrf-token': csrfToken, 'Authorization': `Bearer ${accessToken}` } });
    } catch (e) {
      if (e.response) {
        console.error(`Create task ${i} failed:`, e.response.status, e.response.data);
      } else {
        console.error(`Create task ${i} failed:`, e.message);
      }
      throw e;
    }
  }
  console.log('Created 5 test tasks.');

  // Fetch page 1, limit 2 (only tasks created by this user)
  try {
    res = await client.get(`/tasks?page=1&limit=2&createdBy=${userId}`,
      { headers: { 'Authorization': `Bearer ${accessToken}` } });
  } catch (e) {
    if (e.response) {
      console.error('Get tasks page 1 failed:', e.response.status, e.response.data);
    } else {
      console.error('Get tasks page 1 failed:', e.message);
    }
    throw e;
  }
  if (res.data.tasks.length !== 2) throw new Error('Page 1 did not return 2 tasks');
  if (res.data.page !== 1 || res.data.limit !== 2) throw new Error('Incorrect page/limit metadata');
  if (res.data.total < 5) throw new Error('Total tasks count is less than expected');
  console.log('Page 1 OK:', res.data.tasks.map(t => t.title));

  // Fetch page 2, limit 2
  res = await client.get(`/tasks?page=2&limit=2&createdBy=${userId}`, {
    headers: { 'Authorization': `Bearer ${accessToken}` }
  });
  if (res.data.tasks.length !== 2) throw new Error('Page 2 did not return 2 tasks');
  if (res.data.page !== 2) throw new Error('Page 2 metadata incorrect');
  console.log('Page 2 OK:', res.data.tasks.map(t => t.title));

  // Fetch page 3, limit 2 (should have 1 task)
  res = await client.get(`/tasks?page=3&limit=2&createdBy=${userId}`, {
    headers: { 'Authorization': `Bearer ${accessToken}` }
  });
  if (res.data.tasks.length !== 1) {
    console.error('Page 3 task count:', res.data.tasks.length, 'Tasks:', res.data.tasks.map(t => t.title));
    throw new Error('Page 3 did not return 1 task');
  }
  if (res.data.page !== 3) throw new Error('Page 3 metadata incorrect');
  console.log('Page 3 OK:', res.data.tasks.map(t => t.title));

  // Fetch page 4, limit 2 (should be empty)
  res = await client.get(`/tasks?page=4&limit=2&createdBy=${userId}`, {
    headers: { 'Authorization': `Bearer ${accessToken}` }
  });
  if (res.data.tasks.length !== 0) throw new Error('Page 4 did not return 0 tasks');
  if (res.data.page !== 4) throw new Error('Page 4 metadata incorrect');
  console.log('Page 4 OK (empty as expected)');

  console.log('All pagination tests passed!');
}

main().catch(e => {
  if (e.response) {
    console.error('Test failed with response:', {
      status: e.response.status,
      data: e.response.data
    });
  } else {
    console.error('Test failed:', e.message);
  }
  process.exit(1);
});
