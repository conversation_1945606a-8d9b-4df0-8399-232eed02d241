const mongoose = require('mongoose');

const UserSchema = new mongoose.Schema(
  {
    name: { type: String, required: true },
    email: { type: String, required: true, unique: true, index: true },
    password: { type: String, required: true },
    profileImageUrl: { type: String, default: null },
    role: { type: String, enum: ["admin", "member"], default: "member" }, // Role-based access
    status: { type: String, enum: ["online", "offline", "away"], default: "offline" }, // Online status for chat
    lastActive: { type: Date, default: Date.now }, // Last active timestamp
    socketId: { type: String, default: null }, // Current socket connection id
    refreshTokens: [{ type: String }], // Store multiple valid refresh tokens for rotation and multi-device support
  },
  { timestamps: true }
);

// Use a toJSON transform to remove sensitive data before sending to client
UserSchema.methods.toJSON = function() {
  const user = this;
  const userObject = user.toObject();

  delete userObject.password;
  delete userObject.refreshTokens;

  return userObject;
};

module.exports = mongoose.model('User', UserSchema);
