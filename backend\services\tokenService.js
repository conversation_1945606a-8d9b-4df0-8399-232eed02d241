/**
 * Token Service - Handles JWT generation and validation
 */

const jwt = require('jsonwebtoken');
const crypto = require('crypto');

// Generate secure random string for token
const generateRandomString = (length = 32) => {
  return crypto.randomBytes(length).toString('hex');
};

// Generate JWT tokens
const generateTokens = async (userId) => {
  try {
    // Generate access token (short-lived)
    // Add a unique jti to ensure token uniqueness
    const accessToken = jwt.sign(
      { id: userId, jti: generateRandomString(12) },
      process.env.JWT_SECRET,
      { expiresIn: '15m' }
    );

    const refreshToken = jwt.sign(
      { id: userId, jti: generateRandomString(12) },
      process.env.REFRESH_TOKEN_SECRET,
      { expiresIn: '7d' }
    );

    return { accessToken, refreshToken };
  } catch (error) {
    
    throw error;
  }
};

// Verify JWT token
const verifyToken = (token, secret = process.env.JWT_SECRET) => {
  try {
    return jwt.verify(token, secret);
  } catch (error) {
    throw error;
  }
};



module.exports = {
  generateRandomString,
  generateTokens,
  verifyToken
};
