import React, { useEffect, useState, useContext, useCallback } from "react";
import useUserAuth from "../../hooks/useUserAuth";
import { UserContext } from "../../contexts/userContext";
import DashboardLayout from "../../components/layout/DashboardLayout";
import { useNavigate } from "react-router-dom";
import axiosInstance from "../../utils/axiosInstance";
import { API_PATHS } from "../../utils/apiPaths";
import ConfirmationModal from "../../components/common/ConfirmationModal";
import ErrorBoundary from "../../components/common/ErrorBoundary";
import TaskDetailModal from "../../components/modals/TaskDetailModal";

import { LuTrash2, LuArrowRight } from "react-icons/lu";
import { HiExclamation, HiChart<PERSON>ie, HiClock, HiLightningBolt, HiCheckCircle } from "react-icons/hi";
import moment from "moment";
import { addThousandsSeparator } from "../../utils/helper";
import { mapTaskFromApi, getTaskStatistics } from "../../utils/taskUtils";
import InfoCard from "../../components/cards/InfoCard";
import TaskListTable from "../../components/TaskListTable";
import CustomPieChart from '../../components/charts/CustomPieCharts';
import CustomBarChart from '../../components/charts/CustomBarChart';
import toast from 'react-hot-toast';

const COLORS = ["#8b5cf6", "#0051FF", "#7ED600"];

const initialDashboardState = {
  charts: {
    taskDistribution: { All: 0, Pending: 0, InProgress: 0, Completed: 0 },
    taskPriorityLevels: { Low: 0, Medium: 0, High: 0 },
  },
  recentTasks: [],
};

// Custom fallback UI for TaskListTable errors
const TaskListErrorFallback = (error, resetErrorBoundary) => (
  <div className="p-6 bg-red-50 border border-red-200 rounded-lg">
    <div className="flex items-center mb-4">
      <HiExclamation className="text-red-500 mr-2 h-6 w-6" />
      <h3 className="text-lg font-medium text-red-800">Task List Error</h3>
    </div>
    <p className="text-sm text-red-600 mb-4">
      There was a problem loading the task list. This might be due to a React hooks issue.
    </p>
    <button
      onClick={resetErrorBoundary}
      className="px-4 py-2 bg-red-100 text-red-800 rounded-md hover:bg-red-200 transition-colors"
    >
      Try Again
    </button>
  </div>
);

const Dashboard = () => {
  useUserAuth();
  const { user } = useContext(UserContext);
  const navigate = useNavigate();
  const [selectedTask, setSelectedTask] = useState(null);
  const [isTaskDetailModalOpen, setIsTaskDetailModalOpen] = useState(false);

  const [dashboardData, setDashboardData] = useState(initialDashboardState);
  const [pieChartData, setPieChartData] = useState([]);
  const [barChartData, setBarChartData] = useState([]);
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [selectedTasksForBulkDelete, setSelectedTasksForBulkDelete] = useState([]);
  const [isBulkDeleteModalOpen, setIsBulkDeleteModalOpen] = useState(false);



  // Memoized event handlers
  const handleSelectionChange = useCallback((selectedIds) => {
    setSelectedTasksForBulkDelete(selectedIds);
  }, []);

  const handleTaskDeleted = useCallback((deletedTaskId) => {
    setDashboardData(prevData => ({
      ...prevData,
      recentTasks: prevData.recentTasks.filter(task => task._id !== deletedTaskId)
    }));
    // Removed setRefreshTrigger for instant UI update
  }, []);

  const handleBulkDelete = useCallback(async () => {
    if (selectedTasksForBulkDelete.length === 0) return;
    setIsBulkDeleteModalOpen(false);
    try {
      await axiosInstance.delete(API_PATHS.TASKS.BULK_DELETE, { 
        data: { taskIds: selectedTasksForBulkDelete } 
      });
      toast.success(`${selectedTasksForBulkDelete.length} task(s) deleted successfully.`);
      setDashboardData(prevData => ({
        ...prevData,
        recentTasks: prevData.recentTasks.filter(task => !selectedTasksForBulkDelete.includes(task._id))
      }));
      setSelectedTasksForBulkDelete([]);
      setRefreshTrigger(prev => prev + 1);
    } catch (error) {
      toast.error("Failed to delete selected tasks: " + (error.response?.data?.message || error.message));
    }
  }, [selectedTasksForBulkDelete]);

  const handleTaskClick = useCallback(async (task) => {
    try {
      const response = await axiosInstance.get(API_PATHS.TASKS.GET_BY_ID(task._id));
      const fullTask = response.data?.task || response.data;
      
      if (!fullTask) {
        throw new Error('Failed to fetch task details');
      }

      // Normalize the task data using the same utility as ManageTasks
      const normalizedTask = mapTaskFromApi(fullTask);

      setSelectedTask(normalizedTask);
      setIsTaskDetailModalOpen(true);
    } catch (error) {
      console.error('Failed to fetch task details:', error);
      toast.error('Failed to load task details');
    }
}, []);


  // Memoized data preparation and fetching functions
  const prepareChartData = useCallback((data) => {
    let taskDistribution = data?.charts?.taskDistribution || data?.taskDistribution || initialDashboardState.charts.taskDistribution;
    let taskPriorityLevels = data?.charts?.taskPriorityLevels || data?.taskPriorityLevels || initialDashboardState.charts.taskPriorityLevels;

    const pieData = [
      { status: "Pending", count: taskDistribution.Pending || 0 },
      { status: "In Progress", count: taskDistribution.InProgress || 0 },
      { status: "Completed", count: taskDistribution.Completed || 0 },
    ];
    setPieChartData(pieData);

    // Normalize priority level keys to be lowercase for consistent access
    const normalizedPriorityLevels = {};
    if (taskPriorityLevels) {
      for (const key in taskPriorityLevels) {
        normalizedPriorityLevels[key.toLowerCase()] = taskPriorityLevels[key];
      }
    }

    const priorityOrder = ["High", "Medium", "Low"];
    const barData = priorityOrder.map((level) => ({
      priority: level,
      count: normalizedPriorityLevels[level.toLowerCase()] || 0,
    }));
    setBarChartData(barData);
  }, []);

  const getDashboardData = useCallback(async () => {
    try {
      const tasksResponse = await axiosInstance.get(API_PATHS.TASKS.GET_ALL);
      const tasks = tasksResponse.data?.tasks || [];
      
      const normalisedTasks = tasks.map(mapTaskFromApi);
      const stats = getTaskStatistics(normalisedTasks);

      const counts = { All: stats.total, Pending: stats.pending, InProgress: stats.inProgress, Completed: stats.completed };

      const priorityCounts = { Low: 0, Medium: 0, High: 0 };
      normalisedTasks.forEach(t => {
        if (t.priority === 'low') priorityCounts.Low++;
        else if (t.priority === 'medium') priorityCounts.Medium++;
        else if (t.priority === 'high') priorityCounts.High++;
      });

      const manualData = {
        charts: { taskDistribution: counts, taskPriorityLevels: priorityCounts },
        recentTasks: normalisedTasks.slice(0, 5)
      };
      setDashboardData(manualData);
      prepareChartData(manualData);
    } catch (error) {
      console.error("Failed to fetch dashboard data:", error);
      toast.error("Failed to load dashboard data.");
      setDashboardData(initialDashboardState);
      prepareChartData(initialDashboardState);
    }
  }, [prepareChartData]);

  const onSeeMore = useCallback(() => navigate("/admin/tasks"), [navigate]);

  useEffect(() => {
    getDashboardData();
  }, [getDashboardData, refreshTrigger]);

  // Handle errors in the TaskListTable component
  const handleTaskListError = useCallback(() => {
    toast.error("There was an error with the task list. We're working on fixing it.");
  }, []);

  // Calculate completion percentage
  const completionPercentage = dashboardData?.charts?.taskDistribution?.All > 0
    ? Math.round((dashboardData?.charts?.taskDistribution?.Completed / dashboardData?.charts?.taskDistribution?.All) * 100)
    : 0;

  // Determine greeting based on time of day
  const currentHour = moment().hour();
  let greeting;
  if (currentHour >= 5 && currentHour < 12) {
    greeting = "Good Morning";
  } else if (currentHour >= 12 && currentHour < 18) {
    greeting = "Good Afternoon";
  } else {
    greeting = "Good Evening";
  }



  return (
    <>
      <DashboardLayout activeMenu="Dashboard">
        {/* Welcome Header */}
        <div className="relative bg-gradient-to-br from-indigo-600 to-purple-700 text-white p-8 rounded-2xl mb-8 overflow-hidden">
          {/* Decorative SVGs */}
          <div className="absolute top-0 left-0 -translate-x-1/4 -translate-y-1/4 opacity-20">
            <svg width="400" height="400" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
              <path fill="#A78BFA" d="M43.5,-63.9C56.5,-55.1,67.2,-43.3,73.8,-29.4C80.4,-15.5,82.9,-0.1,79.3,14.5C75.7,29.1,66,42.9,53.8,52.9C41.6,62.9,27,69.1,12.2,72.2C-2.5,75.3,-17.4,75.3,-30.5,70.3C-43.6,65.3,-54.9,55.3,-63.9,43.5C-72.9,31.7,-79.6,18.1,-79.9,4.1C-80.2,-9.8,-74.1,-24.1,-65.2,-36.2C-56.3,-48.3,-44.6,-58.2,-32.1,-65.6C-19.6,-73,-6.3,-77.9,7.3,-79.6C20.9,-81.3,41.8,-80.1,43.5,-63.9Z" transform="translate(100 100)" />
            </svg>
          </div>
          <div className="absolute bottom-0 right-0 translate-x-1/4 translate-y-1/4 opacity-10">
            <svg width="300" height="300" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
              <path fill="#C4B5FD" d="M51.8,-49.9C63.8,-35.5,67.9,-17.8,66.5,-1.3C65.1,15.2,58.3,30.3,47.1,43.4C35.9,56.5,20.3,67.5,2.4,66.1C-15.5,64.7,-31,50.9,-43.3,36.3C-55.6,21.7,-64.7,6.3,-64.1,-9C-63.5,-24.3,-53.2,-39.5,-40.4,-53.2C-27.6,-66.9,-12.3,-79.1,5.1,-81.1C22.5,-83.1,45.1,-75.1,51.8,-49.9Z" transform="translate(100 100)" />
            </svg>
          </div>

          <div className="relative z-10 flex justify-between items-center">
            <div>
              <h2 className="text-3xl font-extrabold tracking-tight">{greeting}! {user?.fullName?.split(' ')[0] || user?.name}</h2>
              <p className="text-indigo-200 mt-1">{moment().format('dddd, Do MMMM YYYY')}</p>
              <div className="mt-5 bg-white/20 backdrop-blur-sm px-4 py-2 rounded-full text-sm inline-flex items-center shadow-inner-lg">
                <HiChartPie className="mr-2 text-indigo-100" />
                <span>Task Completion: <strong>{completionPercentage}%</strong></span>
              </div>
            </div>
            <div className="text-right">
              <div className="text-5xl font-bold">{dashboardData?.charts?.taskDistribution?.All || 0}</div>
              <div className="text-indigo-200">Today's Tasks</div>
            </div>
          </div>
        </div>

        {/* Info Cards */}
        <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-4 gap-4 lg:gap-6 mb-6">
          <InfoCard 
            label="Total Tasks" 
            value={addThousandsSeparator(dashboardData?.charts?.taskDistribution?.All || 0)} 
            color="from-blue-500 to-indigo-600" 
            icon={<HiChartPie className="h-8 w-8" />}
          />
          <InfoCard 
            label="Pending Tasks" 
            value={addThousandsSeparator(dashboardData?.charts?.taskDistribution?.Pending || 0)} 
            color="from-purple-500 to-pink-600" 
            icon={<HiClock className="h-8 w-8" />}
          />
          <InfoCard 
            label="In Progress" 
            value={addThousandsSeparator(dashboardData?.charts?.taskDistribution?.InProgress || 0)} 
            color="from-yellow-500 to-orange-600" 
            icon={<HiLightningBolt className="h-8 w-8" />}
          />
          <InfoCard 
            label="Completed Tasks" 
            value={addThousandsSeparator(dashboardData?.charts?.taskDistribution?.Completed || 0)} 
            color="from-green-500 to-teal-600" 
            icon={<HiCheckCircle className="h-8 w-8" />}
          />
        </div>

        {/* Charts */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-10">
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-5">
            <div className="flex items-center justify-between mb-4">
              <h5 className="font-medium text-gray-700">Task Distribution</h5>
              <div className="bg-blue-50 text-blue-700 text-xs font-medium px-2.5 py-0.5 rounded-full">Status</div>
            </div>
            <div className="h-64">
              <CustomPieChart data={pieChartData} label="Task Status" colors={COLORS} />
            </div>
            <div className="flex justify-center mt-4 space-x-6">
              {pieChartData.map((item, index) => (
                <div key={item.status} className="flex items-center">
                  <div 
                    className="w-3 h-3 rounded-full mr-2" 
                    style={{ backgroundColor: COLORS[index % COLORS.length] }}
                  ></div>
                  <span className="text-xs text-gray-600">{item.status}</span>
                </div>
              ))}
            </div>
          </div>
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-5">
            <div className="flex items-center justify-between mb-4">
              <h5 className="font-medium text-gray-700">Task Priority Levels</h5>
              <div className="bg-purple-50 text-purple-700 text-xs font-medium px-2.5 py-0.5 rounded-full">Priority</div>
            </div>
            <div className="h-64">
              <CustomBarChart data={barChartData} />
            </div>
          </div>
        </div>

        {/* Recent Tasks Section */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-100 flex items-center justify-between">
            <h5 className="text-lg font-semibold text-gray-800">Recent Tasks</h5>
            <div className="flex items-center">
              {selectedTasksForBulkDelete.length > 0 && (
                <button
                  onClick={() => setIsBulkDeleteModalOpen(true)}
                  className="px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors flex items-center"
                >
                  <LuTrash2 className="mr-2 h-4 w-4" /> Delete Selected ({selectedTasksForBulkDelete.length})
                </button>
              )}
              {selectedTasksForBulkDelete.length === 0 && (
                <button 
                  className="flex items-center text-sm text-blue-600 font-medium hover:text-blue-800 transition-colors" 
                  onClick={onSeeMore}
                >
                  See All <LuArrowRight className="ml-1" />
                </button>
              )}
            </div>
          </div>
          
          {/* TaskListTable wrapped with ErrorBoundary */}
          <ErrorBoundary 
            fallback={TaskListErrorFallback}
            onError={handleTaskListError}
            errorTitle="Task List Error"
            errorMessage="There was a problem displaying the task list."
          >
            <TaskListTable 
              tableData={dashboardData?.recentTasks?.map(task => {
                // --- Normalize todos for table view (same as modal) ---
                let normalizedTodos = [];
                if (Array.isArray(task.subTasks) && task.subTasks.length > 0) {
                  normalizedTodos = task.subTasks.map(todo => ({
                    text: todo.text || todo.title || todo.name,
                    completed: todo.completed || todo.isCompleted || todo.status === 'completed' || false
                  }));
                } else if (Array.isArray(task.todoChecklist) && task.todoChecklist.length > 0) {
                  normalizedTodos = task.todoChecklist.map(todo => ({
                    text: todo.text || todo.title || todo.name,
                    completed: todo.completed || todo.isCompleted || todo.status === 'completed' || false
                  }));
                } else if (Array.isArray(task.todoCheckList) && task.todoCheckList.length > 0) {
                  normalizedTodos = task.todoCheckList.map(todo => ({
                    text: todo.text || todo.title || todo.name,
                    completed: todo.completed || todo.isCompleted || todo.status === 'completed' || false
                  }));
                }

                // --- Ensure attachments are an array ---
                const attachments = Array.isArray(task.attachments) ? task.attachments : [];

                return {
                  ...task,
                  assignedTo: Array.isArray(task.team) ? task.team : Array.isArray(task.assignedTo) ? task.assignedTo : [],
                  team: Array.isArray(task.team) ? task.team : Array.isArray(task.assignedTo) ? task.assignedTo : [],
                  subTasks: normalizedTodos,
                  attachments: attachments
                };
              })}
              onTaskDeleted={handleTaskDeleted}
              onSelectionChange={handleSelectionChange}
              onTaskClick={handleTaskClick}
            />
          </ErrorBoundary>
        </div>
      </DashboardLayout>

      <TaskDetailModal
        isOpen={isTaskDetailModalOpen}
        onClose={() => {
          setSelectedTask(null);
          setIsTaskDetailModalOpen(false);
        }}
        task={selectedTask}
        onEdit={() => {
          if (selectedTask?._id) {
            navigate(`/admin/edit-task/${selectedTask._id}`);
            setIsTaskDetailModalOpen(false);
          }
        }}
      />
      {/* Debug output */}
      {isTaskDetailModalOpen && (
        <div style={{ display: 'none' }}>
          {console.log('Debug - Selected Task:', selectedTask)}
          {console.log('Debug - Organization Members:', user?.organization?.members)}
        </div>
      )}

      <ConfirmationModal
        isOpen={isBulkDeleteModalOpen}
        onClose={() => setIsBulkDeleteModalOpen(false)}
        onConfirm={handleBulkDelete}
        title="Confirm Bulk Deletion"
        message={`Are you sure you want to delete ${selectedTasksForBulkDelete.length} selected task(s)? This action cannot be undone.`}
      />
    </>
  );
};

export default Dashboard;
