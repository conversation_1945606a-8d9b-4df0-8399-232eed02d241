import React, { createContext, useState, useEffect, useContext, useCallback } from "react";
import axiosInstance from "../utils/axiosInstance";
import { API_PATHS } from "../utils/apiPaths";
import logger from "../utils/logger";
import { isTokenExpired, debugToken } from "../utils/tokenValidator";

// Create context
export const UserContext = createContext();

// Storage keys
const STORAGE_KEYS = {
  USER: 'user',
  TOKEN: 'token'
};

/**
 * User Provider component that handles authentication state
 * 
 * @param {object} props - Component props
 * @param {React.ReactNode} props.children - Child components
 * @returns {JSX.Element} Provider component
 */
const UserProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  /**
   * Load user from local storage or fetch from API
   */
  const loadUser = useCallback(async () => {
    try {
      // First check if we have a valid token
      const accessToken = localStorage.getItem(STORAGE_KEYS.TOKEN);

      // If no token, user is not authenticated
      if (!accessToken) {
        if (import.meta.env.DEV) {

        }
        setUser(null);
        setLoading(false);
        return;
      }

      // Validate token format and expiration
      if (import.meta.env.DEV) {
        const tokenInfo = debugToken(accessToken);

      }

      if (isTokenExpired(accessToken)) {
        // If localStorage token is expired, still attempt to fetch user profile.
        // The backend will validate the httpOnly cookie and return 401 if truly expired.
        // The axios interceptor will then handle redirection to login.
        if (import.meta.env.DEV) {
          // console.warn("🔑 LocalStorage token expired, attempting to validate with backend...");
        }
      }

      // Always fetch fresh user data to ensure token is valid
      // This prevents issues with expired tokens and stale user data
      if (import.meta.env.DEV) {

      }

      // Fetch user profile from API to validate token
      const response = await axiosInstance.get(API_PATHS.AUTH.GET_PROFILE);
      const userData = response.data;

      if (import.meta.env.DEV) {

      }

      // Validate API response - check for both _id and id
      if (!userData || (!userData._id && !userData.id)) {
        console.error('❌ Invalid user data received from API:', userData);
        throw new Error('Invalid user data received from API');
      }

      // Ensure user has required fields
      const completeUserData = {
        ...userData,
        _id: userData._id || userData.id,
        id: userData._id || userData.id,
        name: userData.name || 'Unknown User',
        email: userData.email || '',
        role: userData.role || 'user',
        profileImageUrl: userData.profileImageUrl || null
      };

      // Update state and storage
      setUser(completeUserData);
      localStorage.setItem(STORAGE_KEYS.USER, JSON.stringify(completeUserData));

      setError(null);
    } catch (error) {
      if (import.meta.env.DEV) {
        console.error("❌ Authentication error:", error);
      }

      // Handle specific authentication errors
      if (error.response?.status === 401) {
        if (import.meta.env.DEV) {

        }
        setError("Your session has expired. Please log in again.");
      } else if (error.response?.status === 403) {
        setError("Access denied. Please check your permissions.");
      } else {
        setError("Failed to authenticate. Please log in again.");
      }

      clearUser(); // Clear invalid authentication data
    } finally {
      setLoading(false);
    }
  }, []);

  // Load user on initial mount only
  useEffect(() => {
    loadUser();
  }, []); // Empty dependency array - run only once on mount

  /**
   * Update user data in state and storage
   * 
   * @param {object} userData - User data to update
   */
  const updateUser = useCallback((userData) => {
    if (!userData) {
      clearUser();
      return;
    }

    try {
      // Normalize user data to ensure consistent structure
      const normalizedUserData = {
        ...userData,
        _id: userData._id || userData.id,
        id: userData._id || userData.id,
        name: userData.name || 'Unknown User',
        email: userData.email || '',
        role: userData.role || 'user',
        profileImageUrl: userData.profileImageUrl && userData.profileImageUrl.trim() !== '' ? userData.profileImageUrl : null
      };

      // Removed console.log to reduce noise

      // Update user state
      setUser(normalizedUserData);

      // Save token if it exists in the userData
      if (userData?.token) {
          localStorage.setItem(STORAGE_KEYS.TOKEN, userData.token);
      }

      // Save the entire user object to localStorage for persistence
      localStorage.setItem(STORAGE_KEYS.USER, JSON.stringify(normalizedUserData));

      setError(null);
      setLoading(false);
    } catch (error) {
      logger.error("Error updating user data:", error);
      setError("Failed to update user data");
    }
  }, []);

  /**
   * Clear all user data from state and storage
   */
  const clearUser = useCallback(() => {
    setUser(null);
    localStorage.removeItem(STORAGE_KEYS.TOKEN);
    localStorage.removeItem(STORAGE_KEYS.USER);
    setError(null);
  }, []);

  /**
   * Refresh the user data from the API
   */
  const refreshUser = useCallback(async () => {
    try {
      setLoading(true);
      // Clear localStorage cache to force fresh data
      localStorage.removeItem(STORAGE_KEYS.USER);
      const response = await axiosInstance.get(API_PATHS.AUTH.GET_PROFILE);
      updateUser(response.data);
    } catch (error) {
      logger.error("Error refreshing user data:", error);
      setError("Failed to refresh user data");
    } finally {
      setLoading(false);
    }
  }, [updateUser]);

  /**
   * Force refresh user data and clear all caches
   */
  const forceRefreshUser = useCallback(async () => {
    try {
      setLoading(true);
      // Clear all cached data
      localStorage.removeItem(STORAGE_KEYS.USER);

      // Force fresh API call
      const response = await axiosInstance.get(`${API_PATHS.AUTH.GET_PROFILE}?_=${Date.now()}`);
      const userData = response.data;

      // Removed console.log to reduce noise

      // Ensure user has required fields
      const completeUserData = {
        ...userData,
        _id: userData._id || userData.id,
        id: userData._id || userData.id,
        name: userData.name || 'Unknown User',
        email: userData.email || '',
        role: userData.role || 'user',
        profileImageUrl: userData.profileImageUrl && userData.profileImageUrl.trim() !== '' ? userData.profileImageUrl : null
      };

      // Removed console.log to reduce noise

      // Update state and storage
      setUser(completeUserData);
      localStorage.setItem(STORAGE_KEYS.USER, JSON.stringify(completeUserData));
      setError(null);
    } catch (error) {
      logger.error("Error force refreshing user data:", error);
      setError("Failed to refresh user data");
    } finally {
      setLoading(false);
    }
  }, []);

  // Context value with memoized functions
  const contextValue = {
    user,
    loading,
    error,
    updateUser,
    clearUser,
    refreshUser,
    forceRefreshUser
  };

  return (
    <UserContext.Provider value={contextValue}>
      {children}
    </UserContext.Provider>
  );
};

/**
 * Custom hook for accessing user context
 * @returns {object} User context value
 */
export const useUser = () => {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
};

export default UserProvider;
