import React, { useState, useEffect, useRef } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, LuPlus, LuRefreshCw, LuSettings, LuTrash2 } from "react-icons/lu";
import { useSocket } from '../../contexts/SocketContext';
import { useUser } from '../../contexts/userContext';
import UserAvatar from '../common/UserAvatar';
import CreateGroupDialog from './CreateGroupDialog';
import NotificationSettings from './NotificationSettings';
import { toast } from 'react-hot-toast';
import chatService from '../../services/chatService';
import {
  getConversationDisplayName,
  getValidOtherParticipants
} from '../../utils/userNameUtils';

const ConversationList = ({ conversations = [], selectedConversation, onSelectConversation, loading, onNewChat, onRefresh, onConversationCleared }) => {
  const { user } = useUser();
  const { connected, isUserOnline, reconnect, getConversationUnreadCount } = useSocket();
  const [searchTerm, setSearchTerm] = useState('');
  const [showCreateGroup, setShowCreateGroup] = useState(false);
  const [showNotificationSettings, setShowNotificationSettings] = useState(false);
  const [reconnecting, setReconnecting] = useState(false);

  // State for dropdown menus and confirmation dialogs
  const [openDropdown, setOpenDropdown] = useState(null);
  const [showClearConfirm, setShowClearConfirm] = useState(null);
  const [clearingConversation, setClearingConversation] = useState(null);

  // State for managing temporarily promoted conversations
  const [temporarilyPromoted, setTemporarilyPromoted] = useState(new Set());
  const [originalPositions, setOriginalPositions] = useState(new Map());
  const previousConversations = useRef(conversations);

  // Effect to detect when conversations are updated (message sent)
  useEffect(() => {
    const prev = previousConversations.current;
    const current = conversations;

    // Check if any temporarily promoted conversation has been updated
    temporarilyPromoted.forEach(conversationId => {
      const prevConv = prev.find(c => c._id === conversationId);
      const currentConv = current.find(c => c._id === conversationId);

      if (prevConv && currentConv) {
        const prevLastMessageTime = new Date(prevConv.lastMessage?.createdAt || 0);
        const currentLastMessageTime = new Date(currentConv.lastMessage?.createdAt || 0);

        // If the last message time has changed, a message was sent
        if (currentLastMessageTime > prevLastMessageTime) {
          // Remove from temporarily promoted (it will stay at top naturally)
          setTemporarilyPromoted(prev => {
            const newSet = new Set(prev);
            newSet.delete(conversationId);
            return newSet;
          });

          // Remove from original positions tracking
          setOriginalPositions(prev => {
            const newMap = new Map(prev);
            newMap.delete(conversationId);
            return newMap;
          });
        }
      }
    });

    // Update the ref for next comparison
    previousConversations.current = conversations;
  }, [conversations, temporarilyPromoted]);
  
  // Filter and sort conversations
  const filteredConversations = conversations
    .filter(conversation => {
      if (!conversation) return false;

      // For group chats, search by group name
      if (conversation.type === 'group' && conversation.name) {
        return conversation.name.toLowerCase().includes(searchTerm.toLowerCase());
      }

      // For individual chats, search by other participant's name
      if (conversation.type === 'individual' && conversation.participants) {
        const currentUserId = user?._id || user?.id;
        const otherParticipant = conversation.participants.find(p => p && p._id !== currentUserId);
        if (otherParticipant && otherParticipant.name) {
          return otherParticipant.name.toLowerCase().includes(searchTerm.toLowerCase());
        }
      }

      return false;
    })
    .sort((a, b) => {
      // First, check if either conversation is temporarily promoted
      const aPromoted = temporarilyPromoted.has(a._id);
      const bPromoted = temporarilyPromoted.has(b._id);

      if (aPromoted && !bPromoted) return -1; // a comes first
      if (!aPromoted && bPromoted) return 1;  // b comes first

      // If both or neither are promoted, sort by last message time (most recent first)
      const aTime = new Date(a.lastMessage?.createdAt || a.updatedAt || 0);
      const bTime = new Date(b.lastMessage?.createdAt || b.updatedAt || 0);

      return bTime - aTime;
    });
  
  // Format the timestamp for last message
  const formatTimestamp = (timestamp) => {
    if (!timestamp) return '';
    
    const date = new Date(timestamp);
    const now = new Date();
    
    // If the message is from today, show time only
    if (date.toDateString() === now.toDateString()) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    }
    
    // If the message is from this week, show day name
    const diff = Math.floor((now - date) / (1000 * 60 * 60 * 24));
    if (diff < 7) {
      return date.toLocaleDateString([], { weekday: 'short' });
    }
    
    // Otherwise show date
    return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
  };
  
  // Enhanced conversation details using utility functions
  const getConversationDetails = (conversation) => {
    if (!conversation) return { name: 'Unknown', image: null };

    if (conversation.type === 'group') {
      return {
        name: getConversationDisplayName(conversation, user),
        image: null,
        isGroup: true,
        participants: conversation.participants || []
      };
    }

    // For individual conversations, use utility functions
    const otherParticipants = getValidOtherParticipants(conversation, user);

    if (otherParticipants.length === 0) {
      return { name: 'Unknown User', image: null, isGroup: false, participants: [] };
    }

    const otherParticipant = otherParticipants[0];

    const conversationName = getConversationDisplayName(conversation, user);

    return {
      name: conversationName,
      image: otherParticipant.profileImageUrl || otherParticipant.profileImage,
      isOnline: isUserOnline(otherParticipant._id),
      isGroup: false,
      participants: [otherParticipant]
    };
  };
  
  // Handle conversation selection with temporary promotion
  const handleConversationSelect = (conversation) => {
    // Store original position if not already stored
    if (!originalPositions.has(conversation._id)) {
      const originalIndex = conversations.findIndex(c => c._id === conversation._id);
      setOriginalPositions(prev => new Map(prev).set(conversation._id, originalIndex));
    }

    // Temporarily promote this conversation to the top
    setTemporarilyPromoted(prev => new Set(prev).add(conversation._id));

    // Call the original selection handler
    onSelectConversation(conversation);

    // Set a timeout to remove temporary promotion if no message is sent
    // This will be cleared if a message is actually sent
    setTimeout(() => {
      setTemporarilyPromoted(prev => {
        const newSet = new Set(prev);
        newSet.delete(conversation._id);
        return newSet;
      });
    }, 30000); // 30 seconds timeout
  };

  // Handle reconnection attempt
  const handleReconnect = async () => {
    setReconnecting(true);

    try {
      if (reconnect && typeof reconnect === 'function') {
        const success = await reconnect();
        if (success) {
          toast.success("Reconnected to chat server!");
        } else {
          toast.error("Couldn't reconnect. Please refresh the page.");
        }
      } else {
        toast.error("Reconnect function not available");
      }
    } catch (err) {
      // Error during reconnection - handled silently
    } finally {
      setReconnecting(false);
    }
  };

  // Handle clearing conversation
  const handleClearConversation = async (conversationId) => {
    try {
      setClearingConversation(conversationId);
      const response = await chatService.clearConversation(conversationId);

      if (response.success) {
        toast.success('Conversation cleared successfully');

        // Notify parent component about the cleared conversation
        if (onConversationCleared) {
          onConversationCleared(conversationId);
        }

        // If the cleared conversation is currently selected, deselect it
        if (selectedConversation?._id === conversationId) {
          onSelectConversation(null);
        }

        // Refresh conversations to update the UI
        if (onRefresh) {
          onRefresh();
        }
      } else {
        toast.error(response.error || 'Failed to clear conversation');
      }
    } catch (error) {
      toast.error('Failed to clear conversation');
    } finally {
      setClearingConversation(null);
      setShowClearConfirm(null);
      setOpenDropdown(null);
    }
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (openDropdown && !event.target.closest('.conversation-dropdown')) {

        setOpenDropdown(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [openDropdown]);

  // Debug: Log when openDropdown state changes
  useEffect(() => {

  }, [openDropdown]);
  
  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="flex justify-between items-center px-6 py-4 border-b">
        <h2 className="text-xl font-semibold">Messages</h2>
        <div className="flex items-center space-x-2">
          {onRefresh && (
            <button
              onClick={onRefresh}
              className="w-8 h-8 flex items-center justify-center bg-gray-100 text-gray-600 rounded-full hover:bg-gray-200 transition-colors"
              title="Refresh conversations"
            >
              <LuRefreshCw className="w-4 h-4" />
            </button>
          )}
          <button
            onClick={() => setShowNotificationSettings(true)}
            className="w-8 h-8 flex items-center justify-center bg-gray-100 text-gray-600 rounded-full hover:bg-gray-200 transition-colors"
            title="Notification settings"
          >
            <LuSettings className="w-4 h-4" />
          </button>
          <button
            onClick={onNewChat}
            className="w-8 h-8 flex items-center justify-center bg-blue-500 text-white rounded-full hover:bg-blue-600 transition-colors"
          >
            <LuPlus className="w-5 h-5" />
          </button>
        </div>
      </div>
      
      {/* Connection status */}
      {!connected && (
        <div className="bg-yellow-50 border-b border-yellow-200 px-4 py-2 flex items-center justify-between">
          <div className="text-yellow-700 text-sm flex items-center">
            <span className="w-2 h-2 bg-yellow-500 rounded-full mr-2 animate-pulse"></span>
            Disconnected from chat server
          </div>
          <button
            onClick={handleReconnect}
            disabled={reconnecting}
            className="px-2 py-1 bg-yellow-100 hover:bg-yellow-200 text-yellow-800 text-xs rounded flex items-center transition-colors"
          >
            {reconnecting ? (
              <>
                <LuRefreshCw className="w-3 h-3 mr-1 animate-spin" />
                Reconnecting...
              </>
            ) : (
              <>
                <LuRefreshCw className="w-3 h-3 mr-1" />
                Reconnect
              </>
            )}
          </button>
        </div>
      )}
      
      {/* Search */}
      <div className="px-6 py-3 border-b">
          <div className="relative">
            <input
              type="text"
            placeholder="Search conversations..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full py-2 pl-10 pr-4 bg-gray-100 rounded-full focus:outline-none focus:ring-2 focus:ring-blue-400"
          />
          <LuSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
        </div>
      </div>

      {/* Conversations List */}
      <div className="flex-1 overflow-y-auto overflow-x-visible">
        {loading ? (
          <div className="flex justify-center items-center h-32">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          </div>
        ) : filteredConversations.length === 0 ? (
          <div className="text-center text-gray-500 py-8">
            {searchTerm ? (
              <p>No conversations match your search</p>
            ) : (
              <p>No conversations yet</p>
            )}
          </div>
        ) : (
          <div className="divide-y">
            {filteredConversations.map((conversation, index) => {
              if (!conversation || !conversation._id) {
                return null;
              }

              const details = getConversationDetails(conversation);
              const lastMessage = conversation.lastMessage;
              const isSelected = selectedConversation?._id === conversation._id;
              const unreadCount = conversation.unreadCount || 0;



              return (
                <div
                  key={`conversation-${conversation._id}-${index}`}
                  className={`group flex items-start p-4 hover:bg-gray-50 cursor-pointer transition-colors relative ${
                    isSelected ? 'bg-blue-50' : ''
                  }`}
                >
                  {/* Main conversation content */}
                  <div
                    onClick={() => handleConversationSelect(conversation)}
                    className="flex items-start flex-1"
                  >
                    {details.isGroup ? (
                      <div className="relative flex items-center justify-center w-12 h-12 bg-gradient-to-br from-blue-400 to-indigo-600 rounded-full text-white">
                        <div className="font-bold text-sm">
                          {details.name.substring(0, 2).toUpperCase()}
                        </div>
                        {unreadCount > 0 && (
                          <div className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 rounded-full flex items-center justify-center text-white text-xs font-bold">
                            {unreadCount > 9 ? '9+' : unreadCount}
                          </div>
                        )}
                      </div>
                    ) : (
                      <div className="relative">
                        <UserAvatar
                          user={details.participants[0]}
                          showStatus
                          isOnline={details.isOnline}
                          size={48}
                        />
                        {unreadCount > 0 && (
                          <div className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 rounded-full flex items-center justify-center text-white text-xs font-bold">
                            {unreadCount > 9 ? '9+' : unreadCount}
                          </div>
                        )}
                      </div>
                    )}

                    <div className="ml-3 flex-1 min-w-0">
                      <div className="flex justify-between items-baseline">
                        <h3 className="font-medium text-gray-900 truncate">
                          {details.name}
                        </h3>
                        {lastMessage && (
                          <span className="text-xs text-gray-500">
                            {formatTimestamp(lastMessage.createdAt)}
                          </span>
                        )}
                      </div>

                      <p className="text-sm text-gray-500 truncate mt-1">
                        {lastMessage ? (
                          <>
                            {lastMessage.sender?._id === user?._id && (
                              <span className="text-gray-400 mr-1">You: </span>
                            )}
                            {lastMessage.text || 'Attachment'}
                          </>
                        ) : (
                          <span className="text-gray-400 italic">No messages yet</span>
                        )}
                      </p>
                    </div>
                  </div>

                  {/* 3-dot menu */}
                  <div className="conversation-dropdown relative flex-shrink-0">
                    <button
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();

                        setOpenDropdown(openDropdown === conversation._id ? null : conversation._id);
                      }}
                      className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-200 rounded-full transition-all duration-200 opacity-100"
                      title="More options"
                    >
                      <span className="text-lg leading-none select-none font-bold">⋮</span>
                    </button>

                    {/* Dropdown menu */}
                    {openDropdown === conversation._id && (
                      <div
                        className="absolute right-0 top-full mt-1 w-48 bg-white rounded-lg shadow-xl border border-gray-200 py-1 z-[100]"
                        style={{
                          position: 'absolute',
                          right: '0',
                          top: '100%',
                          marginTop: '4px',
                          zIndex: 1000
                        }}
                      >
                        <button
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();

                            setShowClearConfirm(conversation._id);
                            setOpenDropdown(null);
                          }}
                          disabled={clearingConversation === conversation._id}
                          className="w-full px-4 py-2 text-left text-sm text-red-600 hover:bg-red-50 flex items-center disabled:opacity-50 transition-colors"
                        >
                          <LuTrash2 className="w-4 h-4 mr-2" />
                          {clearingConversation === conversation._id ? 'Clearing...' : 'Clear Chat'}
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>

      <CreateGroupDialog
        open={showCreateGroup}
        onClose={() => setShowCreateGroup(false)}
      />

      <NotificationSettings
        isOpen={showNotificationSettings}
        onClose={() => setShowNotificationSettings(false)}
      />

      {/* Clear Chat Confirmation Dialog */}
      {showClearConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Clear Chat
            </h3>
            <p className="text-gray-600 mb-6">
              Are you sure you want to delete this conversation? This action will remove all messages and cannot be undone.
            </p>
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowClearConfirm(null)}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
                disabled={clearingConversation === showClearConfirm}
              >
                Cancel
              </button>
              <button
                onClick={() => handleClearConversation(showClearConfirm)}
                disabled={clearingConversation === showClearConfirm}
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50"
              >
                {clearingConversation === showClearConfirm ? 'Clearing...' : 'Clear Chat'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};



export default ConversationList; 