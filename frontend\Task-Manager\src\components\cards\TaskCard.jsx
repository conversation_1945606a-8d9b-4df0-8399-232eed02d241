import React from "react";
import moment from "moment";
import AvatarGroup from "../../components/AvatarGroup";
import { Lu<PERSON>aperclip } from "react-icons/lu";
import Progress from "../Progress";
import PropTypes from "prop-types";
import {
  mapTask<PERSON><PERSON><PERSON><PERSON>,
  calculateTaskProgress,
  TASK_STATUSES,
  TASK_PRIORITIES,
  getStatusBadgeColor,
  getPriorityBadgeColor
} from "../../utils/taskUtils";

/**
 * Task card component for displaying task information
 * 
 * @param {Object} props - Component props
 * @param {Object|null} props.task - Task object (if using object-based approach)
 * @param {string} props.title - Task title
 * @param {string} props.description - Task description
 * @param {string} props.priority - Task priority
 * @param {string} props.status - Task status
 * @param {number} props.progress - Task progress percentage
 * @param {string} props.createdAt - Task creation date
 * @param {string} props.startDate - Task start date
 * @param {string} props.dueDate - Task due date
 * @param {Array} props.assignedTo - Array of users assigned to the task
 * @param {number} props.completedTodoCount - Number of completed todo items
 * @param {Array} props.todoCheckList - Array of todo items
 * @param {Array} props.attachments - Array of attachments
 * @param {Function} props.onClick - Click handler for the card
 * @returns {JSX.Element} Rendered component
 */
const TaskCard = ({ task: taskProp, isSelected, onClick }) => {
  // The task prop is assumed to be normalized by the parent component.
  // We provide default values to prevent crashes if the task object is malformed.
  const task = taskProp || {};
  const {
    title = "Untitled Task",
    description = "",
    priority = TASK_PRIORITIES.MEDIUM,
    status = TASK_STATUSES.PENDING,
    createdAt = new Date().toISOString(),
    startDate,
    dueDate,
    assignedTo = [],
    todoCheckList = [],
    completedTodoCount = 0,
    attachments = [],
  } = task;

  // Debug task data in development
  if (import.meta.env.MODE === 'development') {
    console.debug('[TaskCard] Received task prop:', {
      id: task._id,
      title,
      status,
      priority,
      todos: todoCheckList?.length,
      completed: completedTodoCount
    });
  }

  const getPriorityDisplay = (p) => {
    const map = {
      [TASK_PRIORITIES.LOW]: 'Low',
      [TASK_PRIORITIES.MEDIUM]: 'Medium',
      [TASK_PRIORITIES.HIGH]: 'High'
    };
    return map[p] || 'Medium';
  };

  const getStatusSideColor = () => {
    const colorMap = {
      [TASK_STATUSES.IN_PROGRESS]: "bg-blue-500",
      [TASK_STATUSES.COMPLETED]: "bg-green-500",
      [TASK_STATUSES.PENDING]: "bg-purple-500"
    };
    return colorMap[status] || "bg-gray-400";
  };

  const formatDate = (date) => {
    return date ? moment(date).format("Do MMM YYYY") : 'N/A';
  };

  return (
    <div
      className={`p-4 rounded-md shadow-sm border hover:shadow-lg transition-shadow duration-200 cursor-pointer flex flex-col h-full relative overflow-hidden ${
        isSelected
          ? 'bg-blue-50 border-blue-400 ring-2 ring-blue-300'
          : 'bg-white border-gray-200'
      }`}
      onClick={onClick}
    >
      {/* Accent Border */}
      <div className={`absolute left-0 top-0 bottom-0 w-1.5 ${getStatusSideColor()}`}></div>

      <div className="pl-4 flex flex-col flex-grow">
        {/* Top section: Badges, Title, Description */}
        <div className="flex-grow">
          <div className="flex items-center space-x-2 mb-2">
            <span className={`text-xs px-2 py-0.5 rounded-md font-semibold ${getStatusBadgeColor(status)}`}>
              {status}
            </span>
            <span className={`text-xs px-2 py-0.5 rounded-md font-semibold ${getPriorityBadgeColor(priority)}`}>
              {getPriorityDisplay(priority)} Priority
            </span>
          </div>
          <h3 className="text-md font-semibold text-gray-800 mb-1 line-clamp-2">{title}</h3>
          <p className="text-xs text-gray-600 mb-3 line-clamp-2">{description || "No description available"}</p>
        </div>

        {/* Middle section: Progress & Todos */}
        <div className="my-3">
          <div className="flex justify-between text-xs text-gray-600 mb-1">
            <span>Task Done:</span>
            <span>
              {todoCheckList.length > 0 
                ? `${completedTodoCount} / ${todoCheckList.length}` 
                : "0 / 0"}
            </span>
          </div>
          <Progress 
            progress={calculateTaskProgress(todoCheckList)} 
            status={status} 
          />
          {/* Show preview of todos */}
          {todoCheckList.length > 0 && (
            <ul className="mt-2 text-xs text-gray-700 max-h-16 overflow-y-auto">
              {todoCheckList.slice(0, 3).map((todo, idx) => (
                <li key={idx}>
                  <span className={todo.completed ? "line-through text-gray-400" : ""}>
                    {todo.title}
                  </span>
                </li>
              ))}
              {todoCheckList.length > 3 && (
                <li className="text-gray-400">+{todoCheckList.length - 3} more</li>
              )}
            </ul>
          )}
        </div>

        {/* Bottom Section: Dates, Assignees, Attachments */}
        <div className="mt-auto pt-3 border-t border-gray-100">
          <div className="flex justify-between items-end">
            <div className="flex flex-col space-y-2">
              <div>
                <p className="text-xs text-gray-500 mb-0.5">Start Date</p>
                <p className="text-xs font-medium text-gray-700">{formatDate(startDate || createdAt)}</p>
              </div>
              <div>
                <p className="text-xs text-gray-500 mb-0.5">Due Date</p>
                <p className="text-xs font-medium text-gray-700">{formatDate(dueDate)}</p>
              </div>
            </div>
            <div className="flex flex-col space-y-2 items-end">
              {assignedTo && assignedTo.length > 0 && (
                <AvatarGroup avatars={assignedTo} size="sm" />
              )}
              {attachments && attachments.length > 0 && (
                <div className="flex items-center gap-1 text-gray-600 bg-gray-100 px-1.5 py-0.5 rounded-md w-fit">
                  <LuPaperclip size={14} className="text-gray-500" />
                  <span className="text-xs font-semibold">{attachments.length}</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

TaskCard.propTypes = {
  task: PropTypes.shape({
    _id: PropTypes.string.isRequired,
    title: PropTypes.string,
    description: PropTypes.string,
    priority: PropTypes.string,
    status: PropTypes.string,
    createdAt: PropTypes.string,
    startDate: PropTypes.string,
    dueDate: PropTypes.string,
    assignedTo: PropTypes.array,
    completedTodoCount: PropTypes.number,
    todoCheckList: PropTypes.array,
    attachments: PropTypes.array,
  }).isRequired,
  onClick: PropTypes.func.isRequired,
  isSelected: PropTypes.bool,
};

export default TaskCard;
