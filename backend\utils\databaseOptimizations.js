/**
 * Database Optimization Utilities
 * Handles database indexing and query optimization
 */

const mongoose = require('mongoose');

/**
 * Create database indexes for better query performance
 */
const createDatabaseIndexes = async () => {
  try {


    // Task model indexes
    const Task = mongoose.model('Task');
    
    // Compound indexes for common query patterns
    await Task.collection.createIndex({ status: 1, assignedTo: 1 });
    await Task.collection.createIndex({ assignedTo: 1, status: 1, dueDate: 1 });
    await Task.collection.createIndex({ createdBy: 1, createdAt: -1 });
    await Task.collection.createIndex({ dueDate: 1, status: 1 });
    await Task.collection.createIndex({ priority: 1, status: 1 });
    await Task.collection.createIndex({ createdAt: -1 }); // For recent tasks
    
    // Text search index for task titles and descriptions
    await Task.collection.createIndex({ 
      title: 'text', 
      description: 'text' 
    }, {
      weights: { title: 10, description: 5 },
      name: 'task_text_search'
    });

    // User model indexes
    const User = mongoose.model('User');
    await User.collection.createIndex({ email: 1 }, { unique: true });
    await User.collection.createIndex({ role: 1 });

    // Notification model indexes
    const Notification = mongoose.model('Notification');
    await Notification.collection.createIndex({ recipient: 1, createdAt: -1 });
    await Notification.collection.createIndex({ recipient: 1, isRead: 1 });
    await Notification.collection.createIndex({ type: 1, recipient: 1 });


  } catch (error) {
    console.error('Error creating database indexes:', error);
  }
};

/**
 * Optimized aggregation pipeline for task statistics
 * Replaces multiple separate queries with a single aggregation
 */
const getOptimizedTaskStatistics = async (userId = null) => {
  const Task = mongoose.model('Task');
  
  const matchStage = userId ? { assignedTo: userId } : {};
  
  const pipeline = [
    { $match: matchStage },
    {
      $facet: {
        // Status distribution
        statusStats: [
          {
            $group: {
              _id: '$status',
              count: { $sum: 1 }
            }
          }
        ],
        
        // Priority distribution
        priorityStats: [
          {
            $group: {
              _id: '$priority',
              count: { $sum: 1 }
            }
          }
        ],
        
        // Overdue tasks count
        overdueStats: [
          {
            $match: {
              status: { $ne: 'Completed' },
              dueDate: { $lt: new Date() }
            }
          },
          { $count: 'count' }
        ],
        
        // Total count
        totalStats: [
          { $count: 'count' }
        ],
        
        // Recent tasks (last 10)
        recentTasks: [
          { $sort: { createdAt: -1 } },
          { $limit: 10 },
          {
            $lookup: {
              from: 'users',
              localField: 'assignedTo',
              foreignField: '_id',
              as: 'assignedUsers',
              pipeline: [
                { $project: { name: 1, email: 1, profileImageUrl: 1 } }
              ]
            }
          },
          {
            $project: {
              title: 1,
              status: 1,
              priority: 1,
              dueDate: 1,
              createdAt: 1,
              assignedTo: '$assignedUsers'
            }
          }
        ]
      }
    }
  ];
  
  const [result] = await Task.aggregate(pipeline);
  
  // Transform results to expected format
  const statusStats = result.statusStats.reduce((acc, item) => {
    acc[item._id] = item.count;
    return acc;
  }, { Pending: 0, 'In Progress': 0, Completed: 0 });
  
  const priorityStats = result.priorityStats.reduce((acc, item) => {
    acc[item._id] = item.count;
    return acc;
  }, { Low: 0, Medium: 0, High: 0 });
  
  return {
    totalTasks: result.totalStats[0]?.count || 0,
    pendingTasks: statusStats.Pending,
    inProgressTasks: statusStats['In Progress'],
    completedTasks: statusStats.Completed,
    overdueTasks: result.overdueStats[0]?.count || 0,
    priorityDistribution: priorityStats,
    recentTasks: result.recentTasks
  };
};

/**
 * Optimized user task counts using aggregation
 * Replaces N+1 queries with a single aggregation
 */
const getOptimizedUserTaskCounts = async (userIds) => {
  const Task = mongoose.model('Task');
  
  const pipeline = [
    {
      $match: {
        assignedTo: { $in: userIds }
      }
    },
    {
      $unwind: '$assignedTo'
    },
    {
      $match: {
        assignedTo: { $in: userIds }
      }
    },
    {
      $group: {
        _id: {
          userId: '$assignedTo',
          status: '$status'
        },
        count: { $sum: 1 }
      }
    },
    {
      $group: {
        _id: '$_id.userId',
        statusCounts: {
          $push: {
            status: '$_id.status',
            count: '$count'
          }
        },
        totalTasks: { $sum: '$count' }
      }
    }
  ];
  
  const results = await Task.aggregate(pipeline);
  
  // Transform to expected format
  const userTaskCounts = {};
  userIds.forEach(userId => {
    userTaskCounts[userId.toString()] = {
      pendingTasks: 0,
      inProgressTasks: 0,
      completedTasks: 0,
      totalTasks: 0
    };
  });
  
  results.forEach(result => {
    const userId = result._id.toString();
    userTaskCounts[userId].totalTasks = result.totalTasks;
    
    result.statusCounts.forEach(statusCount => {
      switch (statusCount.status) {
        case 'Pending':
          userTaskCounts[userId].pendingTasks = statusCount.count;
          break;
        case 'In Progress':
          userTaskCounts[userId].inProgressTasks = statusCount.count;
          break;
        case 'Completed':
          userTaskCounts[userId].completedTasks = statusCount.count;
          break;
      }
    });
  });
  
  return userTaskCounts;
};

/**
 * Optimized task search with text indexing
 */
const searchTasks = async (searchTerm, userId = null, options = {}) => {
  const Task = mongoose.model('Task');
  
  const {
    limit = 20,
    skip = 0,
    status = null,
    priority = null
  } = options;
  
  const matchStage = {
    $text: { $search: searchTerm }
  };
  
  if (userId) {
    matchStage.assignedTo = userId;
  }
  
  if (status) {
    matchStage.status = status;
  }
  
  if (priority) {
    matchStage.priority = priority;
  }
  
  const pipeline = [
    { $match: matchStage },
    {
      $addFields: {
        score: { $meta: 'textScore' }
      }
    },
    { $sort: { score: { $meta: 'textScore' }, createdAt: -1 } },
    { $skip: skip },
    { $limit: limit },
    {
      $lookup: {
        from: 'users',
        localField: 'assignedTo',
        foreignField: '_id',
        as: 'assignedUsers',
        pipeline: [
          { $project: { name: 1, email: 1, profileImageUrl: 1 } }
        ]
      }
    },
    {
      $addFields: {
        assignedTo: '$assignedUsers'
      }
    },
    {
      $project: {
        assignedUsers: 0,
        score: 0
      }
    }
  ];
  
  return await Task.aggregate(pipeline);
};

/**
 * Clean up old data to improve performance
 */
const cleanupOldData = async () => {
  try {
    const Notification = mongoose.model('Notification');
    
    // Delete notifications older than 30 days
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    const result = await Notification.deleteMany({
      createdAt: { $lt: thirtyDaysAgo },
      isRead: true
    });
    
    // Only log if there were items to clean up
    if (result.deletedCount > 0) {
      console.log(`Cleaned up ${result.deletedCount} old notifications`);
    }
    
    return result.deletedCount;
  } catch (error) {
    console.error('Error cleaning up old data:', error);
    return 0;
  }
};

/**
 * Get database performance statistics
 */
const getDatabaseStats = async () => {
  try {
    const db = mongoose.connection.db;
    const stats = await db.stats();
    
    return {
      collections: stats.collections,
      dataSize: Math.round(stats.dataSize / 1024 / 1024 * 100) / 100, // MB
      indexSize: Math.round(stats.indexSize / 1024 / 1024 * 100) / 100, // MB
      totalSize: Math.round((stats.dataSize + stats.indexSize) / 1024 / 1024 * 100) / 100, // MB
      objects: stats.objects
    };
  } catch (error) {
    console.error('Error getting database stats:', error);
    return null;
  }
};

module.exports = {
  createDatabaseIndexes,
  getOptimizedTaskStatistics,
  getOptimizedUserTaskCounts,
  searchTasks,
  cleanupOldData,
  getDatabaseStats
};
