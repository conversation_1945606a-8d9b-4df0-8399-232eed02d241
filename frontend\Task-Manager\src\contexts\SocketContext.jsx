import React, { createContext, useContext, useEffect, useState, useCallback, useRef } from 'react';
import { io } from 'socket.io-client';
import { BASE_URL } from '../utils/apiPaths';
import { useUser } from './userContext';

// Create context
const SocketContext = createContext();

// Define a custom event for socket state synchronization
const SOCKET_STATE_EVENT = 'socket_state_update';

export const useSocket = () => {
  const context = useContext(SocketContext);

  // Return default values if context is not available (user not authenticated)
  if (!context) {
    return {
      socket: null,
      connected: false,
      isConnected: false,
      connectedUsers: [],
      typingUsers: {},
      unreadChatCount: 0,
      isUserOnline: () => false,
      sendMessage: () => false,
      onNewMessage: () => () => {},
      sendTyping: () => {},
      sendTypingStatus: () => {},
      onUserTyping: () => () => {},
      markMessageRead: () => {},
      onMessageRead: () => () => {},
      joinConversation: () => false,
      reconnect: () => {},
      markMessageAsDelivered: () => {},
      incrementUnreadChatCount: () => {},
      resetUnreadChatCount: () => {},
      resetConversationUnreadCount: () => {},
      getConversationUnreadCount: () => 0,
      joinRoom: () => false,
      leaveRoom: () => false,
      onTaskAssigned: () => () => {},
      onTaskUpdated: () => () => {},
      onTaskDeleted: () => () => {},
      onNotification: () => () => {},
      emitEvent: () => false,
      onEvent: () => () => {}
    };
  }

  return context;
};

export const SocketProvider = ({ children }) => {
  const { user } = useUser();
  const [socket, setSocket] = useState(null);
  const [isConnected, setIsConnected] = useState(false);
  const [connectedUsers, setConnectedUsers] = useState([]);
  const [typingUsers, setTypingUsers] = useState({});
  const [unreadChatCount, setUnreadChatCount] = useState(0);
  const typingTimeoutRef = useRef({});
  const socketInitializedRef = useRef(false);
  const currentUserIdRef = useRef(null);
  const reconnectTimerRef = useRef(null);
  const eventListenersInitializedRef = useRef(false);
  


  // Function to broadcast socket state changes across tabs
  const broadcastSocketState = (state) => {
    try {
      localStorage.setItem('socket_state', JSON.stringify({
        timestamp: Date.now(),
        state
      }));
      
      // Dispatch a custom event for other tabs
      window.dispatchEvent(new CustomEvent(SOCKET_STATE_EVENT, { 
        detail: state 
      }));
    } catch (err) {
      console.error('Error broadcasting socket state:', err);
    }
  };

  // Function to setup global socket event listeners
  const setupSocketEventListeners = useCallback((socketInstance) => {
    if (!socketInstance) return;

    // Remove any existing listeners first to prevent duplicates
    socketInstance.off('chat:unreadCount');
    socketInstance.off('notificationRead');
    socketInstance.off('messageRead');
    socketInstance.off('newNotification');

    // Listen for unread chat count updates
    socketInstance.on('chat:unreadCount', (count) => {
      setUnreadChatCount(count || 0);

      // Broadcast the updated count to other tabs
      broadcastSocketState({ type: 'unread_count', count });
    });

    // Handle notification read events from socket
    socketInstance.on('notificationRead', (data) => {
      // This will be handled in NotificationContext, but broadcast to other tabs
      broadcastSocketState({ type: 'notification_read', data });
    });

    // Handle message read events from socket
    socketInstance.on('messageRead', (data) => {
      broadcastSocketState({ type: 'message_read', data });
    });

    // Set up notification listener immediately when socket is ready
    socketInstance.on('newNotification', (data) => {
      // Add a small delay and check to prevent rapid duplicate dispatches
      const notificationId = data?._id;
      if (notificationId) {
        // Use a simple debounce mechanism
        const eventKey = `notification_${notificationId}`;
        if (window.lastNotificationDispatch && window.lastNotificationDispatch[eventKey]) {
          const timeSinceLastDispatch = Date.now() - window.lastNotificationDispatch[eventKey];
          if (timeSinceLastDispatch < 1000) { // Prevent duplicates within 1 second
            return;
          }
        }

        // Track this dispatch
        if (!window.lastNotificationDispatch) {
          window.lastNotificationDispatch = {};
        }
        window.lastNotificationDispatch[eventKey] = Date.now();

        // Clean up old entries (older than 5 minutes)
        const fiveMinutesAgo = Date.now() - (5 * 60 * 1000);
        Object.keys(window.lastNotificationDispatch).forEach(key => {
          if (window.lastNotificationDispatch[key] < fiveMinutesAgo) {
            delete window.lastNotificationDispatch[key];
          }
        });
      }

      // Dispatch a custom event that NotificationContext can listen to
      window.dispatchEvent(new CustomEvent('socketNewNotification', { detail: data }));
    });

    // --- Task Event Listeners ---
    // These listeners dispatch window events, allowing any component to react
    // to real-time task updates without being directly coupled to the socket context.

    socketInstance.on('taskAssigned', (task) => {
      window.dispatchEvent(new CustomEvent('socketTaskAssigned', { detail: task }));
    });

    socketInstance.on('taskUpdated', (task) => {
      window.dispatchEvent(new CustomEvent('socketTaskUpdated', { detail: task }));
    });

    socketInstance.on('taskDeleted', (data) => {
      window.dispatchEvent(new CustomEvent('socketTaskDeleted', { detail: data }));
    });


    eventListenersInitializedRef.current = true;
  }, []);

  // Listen for socket state updates from other tabs
  useEffect(() => {
    const handleSocketStateUpdate = (event) => {
      const { detail } = event;
      
      if (!detail || !detail.type) return;
      

      
      switch (detail.type) {
        case 'unread_count':
          setUnreadChatCount(detail.count);
          break;
        case 'notification_read':
          // This is handled in NotificationContext
          break;
        case 'message_read':
          // No action needed here - components will handle it through their hooks
          break;
        case 'connection_change':
          if (detail.connected !== isConnected) {
            setIsConnected(detail.connected);
          }
          break;
        default:
          break;
      }
    };
    
    window.addEventListener(SOCKET_STATE_EVENT, handleSocketStateUpdate);
    
    return () => {
      window.removeEventListener(SOCKET_STATE_EVENT, handleSocketStateUpdate);
    };
  }, [isConnected]);

  // Initialize socket connection when user is authenticated
  useEffect(() => {
    if (!user || !user._id) {
      socketInitializedRef.current = false;
      currentUserIdRef.current = null;
      return;
    }

    // Clear any existing reconnect timer
    if (reconnectTimerRef.current) {
      clearTimeout(reconnectTimerRef.current);
      reconnectTimerRef.current = null;
    }

    // Prevent multiple socket connections for the same user
    if (socketInitializedRef.current && currentUserIdRef.current === user._id && socket && socket.connected) {
      return;
    }

    // Clean up existing socket if user changed
    if (socket && currentUserIdRef.current !== user._id) {
      socket.disconnect();
      socketInitializedRef.current = false;
      eventListenersInitializedRef.current = false;
    }
    
    const token = localStorage.getItem('token');
    const socketUrl = import.meta.env.VITE_SOCKET_URL || BASE_URL;

    if (!socketUrl) {
      console.error('CRITICAL: VITE_SOCKET_URL or VITE_API_BASE_URL is not defined in your environment. Socket connection aborted.');
      return;
    }

    const newSocket = io(socketUrl, {
      withCredentials: true,
      auth: {
        token: token
      },
      transports: ['websocket', 'polling'], // Try websocket first, then fall back to polling
      path: '/socket.io/',
      timeout: 20000,
      reconnection: true,
      reconnectionDelay: 1000,
      reconnectionAttempts: Infinity, // Keep trying to reconnect
      reconnectionDelayMax: 5000
    });

    // Set socket immediately so other contexts can access it
    setSocket(newSocket);

    newSocket.on('connect', () => {
      setIsConnected(true);
      socketInitializedRef.current = true;
      currentUserIdRef.current = user._id;
      
      // Broadcast connection state to other tabs
      broadcastSocketState({ 
        type: 'connection_change', 
        connected: true 
      });

      // Reset reconnect timer if it exists
      if (reconnectTimerRef.current) {
        clearTimeout(reconnectTimerRef.current);
        reconnectTimerRef.current = null;
      }

      // Identify user to the server
      newSocket.emit('user:identify', { userId: user._id, name: user.name });
      
      // Setup listeners for various socket events
      setupSocketEventListeners(newSocket);

      // If the user is an admin, join the admin-room to receive all task updates
      if (user?.role === 'admin') {
        newSocket.emit('join', { room: 'admin-room' });
      }
    });
    
    newSocket.on('disconnect', () => {
      setIsConnected(false);
      broadcastSocketState({
        type: 'connection_change',
        connected: false
      });

      // Try to reconnect after a delay
      if (!reconnectTimerRef.current) {
        reconnectTimerRef.current = setTimeout(() => {
          reconnect();
          reconnectTimerRef.current = null;
        }, 3000);
      }
    });
    
    newSocket.on('connect_error', (error) => {
      console.error('Socket connection error:', error.message);
      setIsConnected(false);
      broadcastSocketState({ 
        type: 'connection_change', 
        connected: false 
      });
      
      // Try to reconnect after a delay
      if (!reconnectTimerRef.current) {
        reconnectTimerRef.current = setTimeout(() => {
          reconnect();
          reconnectTimerRef.current = null;
        }, 5000);
      }
    });
    
    newSocket.on('onlineUsers', (users) => {
      setConnectedUsers(users);
    });
    
    // Cleanup function
    return () => {
      if (newSocket) {
        newSocket.disconnect();
        setIsConnected(false);
        setSocket(null);
        
        if (reconnectTimerRef.current) {
          clearTimeout(reconnectTimerRef.current);
          reconnectTimerRef.current = null;
        }
        
        broadcastSocketState({ 
          type: 'connection_change', 
          connected: false 
        });
      }
    };
  }, [user?._id, setupSocketEventListeners]); 
  
  // Chat message handling
  const sendMessage = useCallback((data) => {
    if (socket && isConnected) {
      socket.emit('sendMessage', data);
      return true;
    } else {
      return false;
    }
  }, [socket, isConnected]);
  
  // Listen for new messages
  const onNewMessage = useCallback((callback) => {
    if (socket) {
      socket.on('newMessage', callback);
      return () => socket.off('newMessage', callback);
    }
    return () => {};
  }, [socket]);
  
  // Handle typing indicators
  const sendTyping = useCallback((data) => {
    if (socket && isConnected && user) {
      socket.emit('chat:typing', {
        ...data,
        userId: user._id,
        username: user.name
      });
    }
  }, [socket, isConnected, user]);

  // Send typing status for conversations
  const sendTypingStatus = useCallback((conversationId, isTyping) => {
    if (socket && isConnected && user && conversationId) {
      socket.emit('typing', {
        conversationId,
        isTyping
      });
    }
  }, [socket, isConnected, user]);
  
  const onUserTyping = useCallback((callback) => {
    if (socket) {
      socket.on('chat:typing', (data) => {
        if (!user || data.userId === user._id) return; // Don't track own typing
        
        setTypingUsers(prev => ({
          ...prev,
          [data.chatId]: {
            userId: data.userId,
            username: data.username,
            timestamp: Date.now()
          }
        }));
        
        // Clear typing indicator after 3 seconds of inactivity
        if (typingTimeoutRef.current[data.chatId]) {
          clearTimeout(typingTimeoutRef.current[data.chatId]);
        }
        
        typingTimeoutRef.current[data.chatId] = setTimeout(() => {
          setTypingUsers(prev => {
            const updated = { ...prev };
            delete updated[data.chatId];
            return updated;
          });
        }, 3000);
        
        callback(data);
      });
      
      return () => socket.off('chat:typing');
    }
    return () => {};
  }, [socket, user]);
  
  // Task notification handlers that listen to the window events
  const onTaskAssigned = useCallback((callback) => {
    const handler = (event) => callback(event.detail);
    window.addEventListener('socketTaskAssigned', handler);
    return () => window.removeEventListener('socketTaskAssigned', handler);
  }, []);

  const onTaskUpdated = useCallback((callback) => {
    const handler = (event) => callback(event.detail);
    window.addEventListener('socketTaskUpdated', handler);
    return () => window.removeEventListener('socketTaskUpdated', handler);
  }, []);

  const onTaskDeleted = useCallback((callback) => {
    const handler = (event) => callback(event.detail);
    window.addEventListener('socketTaskDeleted', handler);
    return () => window.removeEventListener('socketTaskDeleted', handler);
  }, []);
  
  // Notification handlers - using custom event system to prevent duplicates
  const onNotification = useCallback((callback) => {
    if (typeof callback === 'function') {
      const handleCustomNotification = (event) => {
        callback(event.detail);
      };

      window.addEventListener('socketNewNotification', handleCustomNotification);

      return () => {
        window.removeEventListener('socketNewNotification', handleCustomNotification);
      };
    }
    return () => {};
  }, []);
  
  // Subscribe to a room (channel or user-specific)
  const joinRoom = useCallback((roomId) => {
    if (socket && isConnected) {

      socket.emit('room:join', { roomId });
      return true;
    }
    console.warn('Cannot join room, socket not connected');
    return false;
  }, [socket, isConnected]);
  
  // Leave a room
  const leaveRoom = useCallback((roomId) => {
    if (socket && isConnected) {

      socket.emit('room:leave', { roomId });
      return true;
    }
    return false;
  }, [socket, isConnected]);
  
  // Message read receipts
  const markMessageRead = useCallback((messageId, chatId) => {
    if (socket && isConnected && user) {
      socket.emit('chat:messageRead', { 
        messageId,
        chatId,
        userId: user._id
      });
      
      // Also emit the messageRead event for our own UI to update
      broadcastSocketState({
        type: 'message_read',
        data: { messageId, userId: user._id, chatId }
      });
    }
  }, [socket, isConnected, user]);
  
  const onMessageRead = useCallback((callback) => {
    if (socket) {
      socket.on('chat:messageRead', callback);
      return () => socket.off('chat:messageRead', callback);
    }
    return () => {};
  }, [socket]);
  
  // Emit a generic custom event
  const emitEvent = useCallback((eventName, data) => {
    if (socket && isConnected) {
      socket.emit(eventName, data);
      return true;
    }
    return false;
  }, [socket, isConnected]);
  
  // Listen for a generic custom event
  const onEvent = useCallback((eventName, callback) => {
    if (socket) {
      socket.on(eventName, callback);
      return () => socket.off(eventName, callback);
    }
    return () => {};
  }, [socket]);
  
  // Check if a specific user is online
  const isUserOnline = useCallback((userId) => {
    return connectedUsers.includes(userId);
  }, [connectedUsers]);

  // Chat-specific methods
  const joinConversation = useCallback((conversationId) => {
    if (socket && isConnected) {

      socket.emit('joinConversation', { conversationId });
      return true;
    }
    console.warn('Cannot join conversation, socket not connected');
    return false;
  }, [socket, isConnected]);

  const reconnect = useCallback(() => {
    if (socket) {

      if (!socket.connected) {
        socket.connect();
      }
    }
  }, [socket]);

  const markMessageAsDelivered = useCallback((messageId, conversationId) => {
    if (socket && isConnected && user) {
      socket.emit('messageDelivered', {
        messageId,
        conversationId,
        userId: user._id
      });
      
      // Also emit for our own UI to update
      broadcastSocketState({
        type: 'message_delivered',
        data: { messageId, userId: user._id, conversationId }
      });
    }
  }, [socket, isConnected, user]);

  const incrementUnreadChatCount = useCallback(() => {
    setUnreadChatCount(prev => {
      const newCount = prev + 1;
      broadcastSocketState({ 
        type: 'unread_count', 
        count: newCount 
      });
      return newCount;
    });
  }, []);

  const resetUnreadChatCount = useCallback(() => {
    setUnreadChatCount(0);
    broadcastSocketState({ 
      type: 'unread_count', 
      count: 0 
    });
  }, []);

  const resetConversationUnreadCount = useCallback((conversationId) => {
    if (socket && isConnected) {

      socket.emit('markConversationAsRead', { conversationId });
    }
  }, [socket, isConnected]);

  const getConversationUnreadCount = useCallback((conversationId, conversations = []) => {
    // Find the conversation and return its unread count
    const conversation = conversations.find(conv => conv._id === conversationId);
    return conversation?.unreadCount || 0;
  }, []);

  // Context value
  const value = {
    socket,
    connected: isConnected, // Add alias for backward compatibility
    isConnected,
    connectedUsers,
    typingUsers,
    unreadChatCount,
    isUserOnline,

    // Chat methods
    sendMessage,
    onNewMessage,
    sendTyping,
    sendTypingStatus,
    onUserTyping,
    markMessageRead,
    onMessageRead,

    // Chat-specific methods
    joinConversation,
    reconnect,
    markMessageAsDelivered,
    incrementUnreadChatCount,
    resetUnreadChatCount,
    resetConversationUnreadCount,
    getConversationUnreadCount,

    // Room methods
    joinRoom,
    leaveRoom,

    // Task notifications
    onTaskAssigned,
    onTaskUpdated,
    onTaskDeleted,

    // Notifications
    onNotification,

    // Generic methods
    emitEvent,
    onEvent
  };

  return (
    <SocketContext.Provider value={value}>
      {children}
    </SocketContext.Provider>
  );
};
