import { useEffect, useRef, useCallback, useState } from 'react';
import { useSocket } from '../contexts/SocketContext';
import { useUser } from '../contexts/userContext';
import { useNotifications } from '../contexts/NotificationContext';

// Custom event name for messages marked as read
const MESSAGE_READ_EVENT = 'message_marked_as_read';

/**
 * Hook to automatically mark messages as read when they come into view
 * Uses Intersection Observer API to detect when messages are visible
 */
export const useMessageReadTracking = (messages, conversation) => {
  const { markMessageAsRead, socket } = useSocket();
  const { user } = useUser();
  const { notifications, markAsRead: markNotificationAsRead, markMultipleAsRead } = useNotifications();
  const observerRef = useRef(null);
  const markedAsReadRef = useRef(new Set());
  const [lastMarkedMessage, setLastMarkedMessage] = useState(null);
  
  // Listen for external read status changes
  useEffect(() => {
    const handleExternalReadMarkEvent = (event) => {
      const { messageId, senderId, chatId } = event.detail || {};
      
      if (messageId && chatId === conversation?._id) {
        // Update our local tracking of read messages
        markedAsReadRef.current.add(messageId);
        setLastMarkedMessage({
          messageId,
          senderId,
          timestamp: Date.now()
        });
      }
    };
    
    // Listen for messages marked as read from other components
    window.addEventListener(MESSAGE_READ_EVENT, handleExternalReadMarkEvent);
    
    return () => {
      window.removeEventListener(MESSAGE_READ_EVENT, handleExternalReadMarkEvent);
    };
  }, [conversation]);
  
  // Listen for socket read status updates
  useEffect(() => {
    if (!socket || !conversation) return;
    
    const handleSocketReadEvent = (data) => {
      const { messageId, userId, chatId } = data || {};
      
      if (messageId && chatId === conversation._id) {
        // Update our local tracking of read messages
        markedAsReadRef.current.add(messageId);
        setLastMarkedMessage({
          messageId,
          senderId: userId,
          timestamp: Date.now()
        });
      }
    };
    
    socket.on('chat:messageRead', handleSocketReadEvent);
    
    return () => {
      socket.off('chat:messageRead', handleSocketReadEvent);
    };
  }, [socket, conversation]);

  // Create intersection observer
  const createObserver = useCallback(() => {
    if (!markMessageAsRead || !conversation || !user) return;

    // Clean up existing observer
    if (observerRef.current) {
      observerRef.current.disconnect();
    }

    observerRef.current = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const messageId = entry.target.dataset.messageId;
            const senderId = entry.target.dataset.senderId;
            
            // Only mark messages from others as read
            if (messageId && senderId && senderId !== user._id && !markedAsReadRef.current.has(messageId)) {
              markMessageAsRead(messageId, conversation._id);
              markedAsReadRef.current.add(messageId);
              
              // Dispatch custom event to notify other components
              window.dispatchEvent(new CustomEvent(MESSAGE_READ_EVENT, {
                detail: {
                  messageId,
                  senderId,
                  chatId: conversation._id,
                  timestamp: Date.now()
                }
              }));

              // Also mark related notifications as read
              const relatedNotifications = notifications.filter(
                n => n.type === 'chat_message' &&
                     n.conversationId === conversation._id &&
                     !n.isRead &&
                     n.relatedEntity?.entityId === messageId
              );

              if (relatedNotifications.length > 0) {
                const notificationIds = relatedNotifications.map(n => n._id);
                markMultipleAsRead(notificationIds);
              }
              
              // Update last marked message state to trigger re-renders
              setLastMarkedMessage({
                messageId,
                senderId,
                timestamp: Date.now()
              });
            }
          }
        });
      },
      {
        threshold: 0.5, // Message is considered "read" when 50% visible
        rootMargin: '0px 0px -50px 0px' // Only trigger when message is well within viewport
      }
    );
  }, [markMessageAsRead, conversation, user, notifications, markMultipleAsRead]);

  // Observe message elements
  const observeMessage = useCallback((element) => {
    if (observerRef.current && element) {
      observerRef.current.observe(element);
    }
  }, []);

  // Unobserve message elements
  const unobserveMessage = useCallback((element) => {
    if (observerRef.current && element) {
      observerRef.current.unobserve(element);
    }
  }, []);

  // Initialize observer when dependencies change
  useEffect(() => {
    createObserver();
    
    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [createObserver]);

  // Clear marked messages when conversation changes
  useEffect(() => {
    markedAsReadRef.current.clear();
    setLastMarkedMessage(null);
  }, [conversation?._id]);
  
  // Check if a message is read
  const isMessageRead = useCallback((messageId) => {
    return markedAsReadRef.current.has(messageId);
  }, []);

  return {
    observeMessage,
    unobserveMessage,
    isMessageRead,
    lastMarkedMessage
  };
};
