/* Custom scrollbar hide for consistency */
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

.scrollbar-hide {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

/* Chat UI Styles */
.chat-container {
  height: calc(100vh - 80px);
  display: flex;
}

/* Smooth transitions */
.chat-item {
  transition: all 0.2s ease-in-out;
}

.chat-item:hover {
  background-color: rgba(59, 130, 246, 0.05);
}

/* Message bubble styles */
.message-bubble {
  max-width: 80%;
  word-wrap: break-word;
}

.incoming-message {
  background-color: #ffffff;
  border-radius: 0.75rem;
  padding: 0.75rem;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.outgoing-message {
  background-color: #3b82f6;
  color: white;
  border-radius: 0.75rem;
  padding: 0.75rem;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* Online status indicator */
.status-indicator {
  position: absolute;
  bottom: 0;
  right: 0;
  height: 0.75rem;
  width: 0.75rem;
  border-radius: 50%;
  border: 2px solid white;
}

.status-online {
  background-color: #10b981;
}

.status-offline {
  background-color: #9ca3af;
}

/* Input field focus style */
.message-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

/* Message status indicators */
.message-status-icon {
  transition: all 0.2s ease-in-out;
}

.message-status-sending {
  opacity: 0.6;
  animation: pulse 1.5s ease-in-out infinite;
}

.message-status-sent {
  color: rgba(255, 255, 255, 0.7);
}

.message-status-delivered {
  color: rgba(255, 255, 255, 0.8);
}

.message-status-read {
  color: #4FC3F7;
}

.message-status-failed {
  color: #f44336;
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
}