import React from "react";
import appLogo from "../../assets/logos/Logo (2).png"; 
import { Link } from "react-router-dom";



const AuthLayout = ({ children, title = "Sign in to your account" }) => {
  return (
    <div className="flex min-h-screen bg-gray-50">
      <div className="w-full min-h-screen md:w-[60vw] flex flex-col overflow-y-auto"> {/* Changed h-screen to min-h-screen and added overflow-y-auto */}
        {/* Header with logo */}
        <header className="px-8 py-2"> {/* Further reduced padding */}
          <Link to="/" className="flex items-center gap-2">
            <img src={appLogo} alt="Taskify Logo" className="h-16 w-auto" />
          </Link>
        </header>
        
        {/* Main content area */}
        <div className="flex-1 flex items-center justify-center px-6 py-2 lg:px-8"> {/* Further reduced padding */}
          <div className="w-full max-w-md">
            {/* <div className="text-center">
              <h2 className="text-2xl font-bold tracking-tight text-gray-900">{title}</h2>
            </div> */}
            {children}
          </div>
        </div>
        
     
      </div>
      
      {/* Side panel with illustration */}
      <div className="hidden md:flex w-[40vw] h-screen items-center justify-center bg-blue-50 bg-cover bg-no-repeat bg-center overflow-hidden p-4" style={{ backgroundImage: 'url(/authbackground1.jpg)' }}>
        <div className="max-w-lg p-8 text-center">
          {/* Text content removed */}
        </div>
      </div>
    </div>
  );
};

export default AuthLayout;