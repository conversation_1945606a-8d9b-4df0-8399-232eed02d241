/**
 * Cache Manager
 * Optimized caching system for API responses and computed values
 */

class CacheManager {
  constructor() {
    this.cache = new Map();
    this.timestamps = new Map();
    this.defaultTTL = 5 * 60 * 1000; // 5 minutes
    this.maxSize = 100; // Maximum number of cached items
    
    // Clean up expired items every minute
    setInterval(() => this.cleanup(), 60 * 1000);
  }

  /**
   * Generate cache key from parameters
   */
  generateKey(prefix, params = {}) {
    const sortedParams = Object.keys(params)
      .sort()
      .map(key => `${key}:${JSON.stringify(params[key])}`)
      .join('|');
    
    return `${prefix}${sortedParams ? `|${sortedParams}` : ''}`;
  }

  /**
   * Set cache item with TTL
   */
  set(key, value, ttl = this.defaultTTL) {
    // Remove oldest items if cache is full
    if (this.cache.size >= this.maxSize) {
      const oldestKey = this.cache.keys().next().value;
      this.delete(oldestKey);
    }

    this.cache.set(key, value);
    this.timestamps.set(key, Date.now() + ttl);
    
    return value;
  }

  /**
   * Get cache item if not expired
   */
  get(key) {
    const timestamp = this.timestamps.get(key);
    
    if (!timestamp || Date.now() > timestamp) {
      this.delete(key);
      return null;
    }
    
    return this.cache.get(key);
  }

  /**
   * Delete cache item
   */
  delete(key) {
    this.cache.delete(key);
    this.timestamps.delete(key);
  }

  /**
   * Clear all cache
   */
  clear() {
    this.cache.clear();
    this.timestamps.clear();
  }

  /**
   * Clean up expired items
   */
  cleanup() {
    const now = Date.now();
    
    for (const [key, timestamp] of this.timestamps.entries()) {
      if (now > timestamp) {
        this.delete(key);
      }
    }
  }

  /**
   * Get cache statistics
   */
  getStats() {
    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      hitRate: this.hitCount / (this.hitCount + this.missCount) || 0,
      hits: this.hitCount,
      misses: this.missCount
    };
  }

  /**
   * Cached API call wrapper
   */
  async cachedApiCall(key, apiCall, ttl = this.defaultTTL) {
    // Check cache first
    const cached = this.get(key);
    if (cached) {
      this.hitCount = (this.hitCount || 0) + 1;
      return cached;
    }

    // Make API call and cache result
    try {
      const result = await apiCall();
      this.missCount = (this.missCount || 0) + 1;
      return this.set(key, result, ttl);
    } catch (error) {
      this.missCount = (this.missCount || 0) + 1;
      throw error;
    }
  }

  /**
   * Invalidate cache by pattern
   */
  invalidatePattern(pattern) {
    const regex = new RegExp(pattern);
    const keysToDelete = [];
    
    for (const key of this.cache.keys()) {
      if (regex.test(key)) {
        keysToDelete.push(key);
      }
    }
    
    keysToDelete.forEach(key => this.delete(key));
    return keysToDelete.length;
  }
}

// Create singleton instance
const cacheManager = new CacheManager();

// Specific cache utilities for common use cases

/**
 * Cache dashboard data
 */
export const cacheDashboardData = (userRole, data) => {
  const key = cacheManager.generateKey('dashboard', { role: userRole });
  return cacheManager.set(key, data, 3 * 60 * 1000); // 3 minutes TTL
};

export const getCachedDashboardData = (userRole) => {
  const key = cacheManager.generateKey('dashboard', { role: userRole });
  return cacheManager.get(key);
};

/**
 * Cache task list data
 */
export const cacheTaskList = (filters, data) => {
  const key = cacheManager.generateKey('tasks', filters);
  return cacheManager.set(key, data, 2 * 60 * 1000); // 2 minutes TTL
};

export const getCachedTaskList = (filters) => {
  const key = cacheManager.generateKey('tasks', filters);
  return cacheManager.get(key);
};

/**
 * Cache user list data
 */
export const cacheUserList = (data) => {
  const key = cacheManager.generateKey('users');
  return cacheManager.set(key, data, 5 * 60 * 1000); // 5 minutes TTL
};

export const getCachedUserList = () => {
  const key = cacheManager.generateKey('users');
  return cacheManager.get(key);
};

/**
 * Cache individual task data
 */
export const cacheTask = (taskId, data) => {
  const key = cacheManager.generateKey('task', { id: taskId });
  return cacheManager.set(key, data, 10 * 60 * 1000); // 10 minutes TTL
};

export const getCachedTask = (taskId) => {
  const key = cacheManager.generateKey('task', { id: taskId });
  return cacheManager.get(key);
};

/**
 * Invalidate task-related caches
 */
export const invalidateTaskCaches = () => {
  const invalidated = cacheManager.invalidatePattern('^(tasks|task|dashboard)');

  return invalidated;
};

/**
 * Invalidate user-related caches
 */
export const invalidateUserCaches = () => {
  const invalidated = cacheManager.invalidatePattern('^(users|dashboard)');

  return invalidated;
};

/**
 * Cache computed values (memoization)
 */
const computedCache = new Map();

export const memoize = (fn, keyGenerator = (...args) => JSON.stringify(args)) => {
  return (...args) => {
    const key = keyGenerator(...args);
    
    if (computedCache.has(key)) {
      return computedCache.get(key);
    }
    
    const result = fn(...args);
    computedCache.set(key, result);
    
    // Limit computed cache size
    if (computedCache.size > 50) {
      const firstKey = computedCache.keys().next().value;
      computedCache.delete(firstKey);
    }
    
    return result;
  };
};

/**
 * Clear computed cache
 */
export const clearComputedCache = () => {
  computedCache.clear();
};

/**
 * Local storage cache with expiration
 */
export const localStorageCache = {
  set(key, value, ttl = 24 * 60 * 60 * 1000) { // 24 hours default
    const item = {
      value,
      expiry: Date.now() + ttl
    };
    
    try {
      localStorage.setItem(key, JSON.stringify(item));
    } catch (error) {
      console.warn('Failed to save to localStorage:', error);
    }
  },

  get(key) {
    try {
      const itemStr = localStorage.getItem(key);
      if (!itemStr) return null;

      const item = JSON.parse(itemStr);
      
      if (Date.now() > item.expiry) {
        localStorage.removeItem(key);
        return null;
      }
      
      return item.value;
    } catch (error) {
      console.warn('Failed to read from localStorage:', error);
      return null;
    }
  },

  remove(key) {
    try {
      localStorage.removeItem(key);
    } catch (error) {
      console.warn('Failed to remove from localStorage:', error);
    }
  },

  clear() {
    try {
      localStorage.clear();
    } catch (error) {
      console.warn('Failed to clear localStorage:', error);
    }
  }
};

/**
 * Session storage cache (expires when tab closes)
 */
export const sessionStorageCache = {
  set(key, value) {
    try {
      sessionStorage.setItem(key, JSON.stringify(value));
    } catch (error) {
      console.warn('Failed to save to sessionStorage:', error);
    }
  },

  get(key) {
    try {
      const itemStr = sessionStorage.getItem(key);
      return itemStr ? JSON.parse(itemStr) : null;
    } catch (error) {
      console.warn('Failed to read from sessionStorage:', error);
      return null;
    }
  },

  remove(key) {
    try {
      sessionStorage.removeItem(key);
    } catch (error) {
      console.warn('Failed to remove from sessionStorage:', error);
    }
  },

  clear() {
    try {
      sessionStorage.clear();
    } catch (error) {
      console.warn('Failed to clear sessionStorage:', error);
    }
  }
};

// Export cache manager instance and utilities
export {
  cacheManager,
  CacheManager
};

export default cacheManager;
