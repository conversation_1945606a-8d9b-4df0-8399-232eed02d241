const csurf = require('csurf');
const express = require('express');
const cookieParser = require('cookie-parser');
const request = require('supertest');

describe('CSRF Middleware Integration', () => {
  let app;
  beforeEach(() => {
    app = express();
    app.use(cookieParser());
    // Setup CSRF
    const csrfProtection = csurf({
      cookie: {
        httpOnly: false,
        sameSite: 'strict',
        secure: false,
      }
    });
    app.get('/csrf-token', csrfProtection, (req, res) => {
      res.cookie('XSRF-TOKEN', req.csrfToken(), {
        httpOnly: false,
        sameSite: 'lax',
        secure: false,
      });
      res.json({ csrfToken: req.csrfToken() });
    });
    // Simulate state-changing route
    app.post('/test', csrfProtection, (req, res) => {
      res.json({ success: true });
    });
    // Add error handler to return JSON error messages like in production
    app.use((err, req, res, next) => {
      if (err.code === 'EBADCSRFTOKEN') {
        return res.status(403).json({ message: 'Invalid CSRF token' });
      }
      res.status(500).json({ message: err.message });
    });
  });

  it('should reject POST without CSRF token', async () => {
    const res = await request(app).post('/test');
    expect(res.status).toBe(403);
    expect(res.body).toHaveProperty('message');
    expect(res.body.message.toLowerCase()).toContain('csrf');
  });

  it('should allow POST with valid CSRF token (cookie & header)', async () => {
    // First, get CSRF token and cookie
    const csrfRes = await request(app).get('/csrf-token');
    const csrfToken = csrfRes.body.csrfToken;
    const cookies = csrfRes.headers['set-cookie'];
    // Send POST with cookie and header
    const res = await request(app)
      .post('/test')
      .set('Cookie', cookies)
      .set('x-csrf-token', csrfToken);
    expect(res.status).toBe(200);
    expect(res.body).toEqual({ success: true });
  });
});
