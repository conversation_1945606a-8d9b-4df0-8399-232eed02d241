// CSRF token utilities for SPA integration
import axiosInstance from './axiosInstance';

let csrfToken = null;

// Fetch CSRF token from backend and cache it
export async function fetchCsrfToken() {
  const res = await axiosInstance.get('/api/auth/csrf-token');
  csrfToken = res.data?.csrfToken || null;
  return csrfToken;
}

export function getCsrfToken() {
  return csrfToken;
}

// Attach CSRF token to config if needed
export function attachCsrfToken(config) {
  if (!config.headers) config.headers = {};
  // Only attach for non-GET/OPTIONS requests
  if (config.method && !['get','options'].includes(config.method.toLowerCase())) {
    if (csrfToken) {
      config.headers['x-csrf-token'] = csrfToken;
    }
  }
  return config;
}
