/**
 * Input Security Middleware
 * Comprehensive input validation, sanitization, and security measures
 */

// Try to require optional dependencies, fallback to built-in functions if not available
let validator;
let DOMPurify;
try {
  validator = require('validator');
} catch (e) {
  // Fallback validator implementation
  validator = {
    isEmail: (email) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email),
    isURL: (url, options) => {
      try {
        const urlObj = new URL(url);
        if (options?.protocols && !options.protocols.includes(urlObj.protocol.slice(0, -1))) {
          return false;
        }
        return true;
      } catch {
        return false;
      }
    }
  };
}

try {
  DOMPurify = require('isomorphic-dompurify');
} catch (e) {
  // Fallback DOMPurify implementation
  DOMPurify = {
    sanitize: (input, options) => {
      if (typeof input !== 'string') return input;
      return input
        .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
        .replace(/<[^>]*>/g, '')
        .replace(/javascript:/gi, '')
        .replace(/vbscript:/gi, '');
    }
  };
}

const rateLimit = require('express-rate-limit');

/**
 * SQL/NoSQL Injection Protection
 */
const sanitizeInput = (input) => {
  if (typeof input !== 'string') return input;
  
  // Remove potentially dangerous characters and patterns
  return input
    .replace(/[<>]/g, '') // Remove angle brackets
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/vbscript:/gi, '') // Remove vbscript: protocol
    .replace(/on\w+\s*=/gi, '') // Remove event handlers
    .replace(/\$where/gi, '') // Remove MongoDB $where
    .replace(/\$ne/gi, '') // Remove MongoDB $ne
    .replace(/\$gt/gi, '') // Remove MongoDB $gt
    .replace(/\$lt/gi, '') // Remove MongoDB $lt
    .replace(/\$or/gi, '') // Remove MongoDB $or
    .replace(/\$and/gi, '') // Remove MongoDB $and
    .trim();
};

/**
 * XSS Protection
 */
const sanitizeHtml = (input) => {
  if (typeof input !== 'string') return input;
  
  // Use DOMPurify to clean HTML
  return DOMPurify.sanitize(input, {
    ALLOWED_TAGS: [], // No HTML tags allowed
    ALLOWED_ATTR: [],
    KEEP_CONTENT: true
  });
};

/**
 * Deep sanitization for objects
 */
const deepSanitize = (obj) => {
  if (obj === null || obj === undefined) return obj;
  
  if (typeof obj === 'string') {
    return sanitizeHtml(sanitizeInput(obj));
  }
  
  if (Array.isArray(obj)) {
    return obj.map(deepSanitize);
  }
  
  if (typeof obj === 'object') {
    const sanitized = {};
    for (const [key, value] of Object.entries(obj)) {
      // Sanitize both key and value
      const sanitizedKey = sanitizeInput(key);
      sanitized[sanitizedKey] = deepSanitize(value);
    }
    return sanitized;
  }
  
  return obj;
};

/**
 * Input validation middleware
 */
const validateInput = (req, res, next) => {
  try {
    // Sanitize request body
    if (req.body) {
      req.body = deepSanitize(req.body);
    }
    
    // Sanitize query parameters
    if (req.query) {
      req.query = deepSanitize(req.query);
    }
    
    // Sanitize URL parameters
    if (req.params) {
      req.params = deepSanitize(req.params);
    }
    
    next();
  } catch (error) {
    return res.status(400).json({
      success: false,
      message: 'Invalid input data'
    });
  }
};

/**
 * Email validation
 */
const validateEmail = (email) => {
  if (!email || typeof email !== 'string') {
    return { isValid: false, message: 'Email is required' };
  }
  
  if (!validator.isEmail(email)) {
    return { isValid: false, message: 'Invalid email format' };
  }
  
  if (email.length > 254) {
    return { isValid: false, message: 'Email too long' };
  }
  
  // Check for suspicious patterns
  const suspiciousPatterns = [
    /script/i,
    /javascript/i,
    /vbscript/i,
    /<.*>/,
    /\$\w+/
  ];
  
  for (const pattern of suspiciousPatterns) {
    if (pattern.test(email)) {
      return { isValid: false, message: 'Invalid email format' };
    }
  }
  
  return { isValid: true };
};

/**
 * Password validation
 */
const validatePassword = (password) => {
  if (!password || typeof password !== 'string') {
    return { isValid: false, message: 'Password is required' };
  }
  
  const minLength = 8;
  const maxLength = 128;
  
  if (password.length < minLength) {
    return { isValid: false, message: `Password must be at least ${minLength} characters long` };
  }
  
  if (password.length > maxLength) {
    return { isValid: false, message: `Password must be less than ${maxLength} characters long` };
  }
  
  // Check for required character types
  const hasUpperCase = /[A-Z]/.test(password);
  const hasLowerCase = /[a-z]/.test(password);
  const hasNumbers = /\d/.test(password);
  const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);
  
  if (!hasUpperCase) {
    return { isValid: false, message: 'Password must contain at least one uppercase letter' };
  }
  
  if (!hasLowerCase) {
    return { isValid: false, message: 'Password must contain at least one lowercase letter' };
  }
  
  if (!hasNumbers) {
    return { isValid: false, message: 'Password must contain at least one number' };
  }
  
  if (!hasSpecialChar) {
    return { isValid: false, message: 'Password must contain at least one special character' };
  }
  
  // Check for common weak passwords
  const commonPasswords = [
    'password', '123456', '123456789', 'qwerty', 'abc123',
    'password123', 'admin', 'letmein', 'welcome', 'monkey'
  ];
  
  if (commonPasswords.includes(password.toLowerCase())) {
    return { isValid: false, message: 'Password is too common' };
  }
  
  return { isValid: true };
};

/**
 * Text content validation
 */
const validateTextContent = (text, options = {}) => {
  const {
    minLength = 0,
    maxLength = 1000,
    allowHtml = false,
    fieldName = 'Text'
  } = options;
  
  if (text === null || text === undefined) {
    if (minLength > 0) {
      return { isValid: false, message: `${fieldName} is required` };
    }
    return { isValid: true };
  }
  
  if (typeof text !== 'string') {
    return { isValid: false, message: `${fieldName} must be a string` };
  }
  
  if (text.length < minLength) {
    return { isValid: false, message: `${fieldName} must be at least ${minLength} characters long` };
  }
  
  if (text.length > maxLength) {
    return { isValid: false, message: `${fieldName} must be less than ${maxLength} characters long` };
  }
  
  // Check for HTML if not allowed
  if (!allowHtml && /<[^>]*>/.test(text)) {
    return { isValid: false, message: `${fieldName} cannot contain HTML` };
  }
  
  // Check for suspicious patterns
  const suspiciousPatterns = [
    /javascript:/i,
    /vbscript:/i,
    /data:text\/html/i,
    /\$\w+\(/,
    /eval\(/i,
    /exec\(/i
  ];
  
  for (const pattern of suspiciousPatterns) {
    if (pattern.test(text)) {
      return { isValid: false, message: `${fieldName} contains invalid content` };
    }
  }
  
  return { isValid: true };
};

/**
 * URL validation
 */
const validateUrl = (url) => {
  if (!url || typeof url !== 'string') {
    return { isValid: false, message: 'URL is required' };
  }
  
  if (!validator.isURL(url, {
    protocols: ['http', 'https'],
    require_protocol: true,
    require_host: true,
    require_valid_protocol: true
  })) {
    return { isValid: false, message: 'Invalid URL format' };
  }
  
  // Block dangerous protocols and localhost
  const dangerousPatterns = [
    /^javascript:/i,
    /^data:/i,
    /^vbscript:/i,
    /^file:/i,
    /^ftp:/i,
    /localhost/i,
    /127\.0\.0\.1/,
    /0\.0\.0\.0/,
    /192\.168\./,
    /10\./,
    /172\.(1[6-9]|2[0-9]|3[0-1])\./
  ];
  
  for (const pattern of dangerousPatterns) {
    if (pattern.test(url)) {
      return { isValid: false, message: 'URL not allowed' };
    }
  }
  
  return { isValid: true };
};

/**
 * Request size validation middleware
 */
const validateRequestSize = (maxSize = '10mb') => {
  return (req, res, next) => {
    const contentLength = parseInt(req.get('content-length') || '0');
    const maxBytes = parseSize(maxSize);
    
    if (contentLength > maxBytes) {
      return res.status(413).json({
        success: false,
        message: `Request too large. Maximum size is ${maxSize}`
      });
    }
    
    next();
  };
};

/**
 * Parse size string to bytes
 */
const parseSize = (size) => {
  const units = { b: 1, kb: 1024, mb: 1024 * 1024, gb: 1024 * 1024 * 1024 };
  const match = size.toString().toLowerCase().match(/^(\d+(?:\.\d+)?)\s*(b|kb|mb|gb)?$/);
  
  if (!match) throw new Error('Invalid size format');
  
  const value = parseFloat(match[1]);
  const unit = match[2] || 'b';
  
  return Math.floor(value * units[unit]);
};

/**
 * CSRF Protection middleware
 */
const csrfProtection = (req, res, next) => {
  // Skip CSRF for GET, HEAD, OPTIONS
  if (['GET', 'HEAD', 'OPTIONS'].includes(req.method)) {
    return next();
  }
  
  const token = req.headers['x-csrf-token'] || req.body._csrf;
  const sessionToken = req.session?.csrfToken;
  
  if (!token || !sessionToken || token !== sessionToken) {
    return res.status(403).json({
      success: false,
      message: 'Invalid CSRF token'
    });
  }
  
  next();
};

/**
 * Rate limiting for different endpoints
 */
const createRateLimit = (windowMs, max, message) => {
  return rateLimit({
    windowMs,
    max,
    message: {
      success: false,
      message
    },
    standardHeaders: true,
    legacyHeaders: false
  });
};

/**
 * Honeypot field validation
 */
const honeypotValidation = (req, res, next) => {
  // Skip validation if no body or body is not an object
  if (!req.body || typeof req.body !== 'object') {
    return next();
  }

  // Check for honeypot fields that should be empty
  const honeypotFields = ['website', 'url', 'homepage', 'phone_number'];

  for (const field of honeypotFields) {
    if (req.body[field] && typeof req.body[field] === 'string' && req.body[field].trim() !== '') {
      console.log(`🚫 Honeypot field detected: ${field} = ${req.body[field]}`);
      // Likely a bot, silently reject
      return res.status(400).json({
        success: false,
        message: 'Invalid request'
      });
    }
  }

  next();
};

module.exports = {
  sanitizeInput,
  sanitizeHtml,
  deepSanitize,
  validateInput,
  validateEmail,
  validatePassword,
  validateTextContent,
  validateUrl,
  validateRequestSize,
  csrfProtection,
  createRateLimit,
  honeypotValidation
};
