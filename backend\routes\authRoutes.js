const express = require('express');
const { register, login, getUserProfile, updateUserProfile, logout } = require('../controllers/authController');
const { protect, adminOnly } = require('../middleware/authMiddleware');
const { enhancedProtect } = require('../middleware/enhancedAuthMiddleware');
const upload = require('../middleware/uploadMiddleware');
const jwt = require('jsonwebtoken');

const router = express.Router();

// CSRF Token Route
const crypto = require('crypto');
router.get('/csrf-token', (req, res) => {
  const token = crypto.randomBytes(24).toString('hex');
  // Optionally store token in session/cookie for validation later if needed
  res.json({ csrfToken: token });
});

// Auth Routes
router.post('/register', register); // Register User
router.post('/login', login); // Login
router.post('/logout', logout); // Logout
router.get('/profile', enhancedProtect, getUserProfile); // Get User Profile
router.put('/profile', enhancedProtect, updateUserProfile); // Update Profile

// Image upload route - protected to ensure only authenticated users can upload
router.post('/upload-image', protect, upload.single('image'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ message: 'No file uploaded' });
    }
    
    // Get the current user to check for existing profile image
    const User = require('../models/User');
    const user = await User.findById(req.user.id);

    if (!user) {
      // This case should be rare if protect middleware is working, but good for defense
      return res.status(404).json({ message: 'User not found after image upload processing.' });
    }

    const fs = require('fs');
    const path = require('path');
    
    // If user has an existing profile image, delete it directly
    if (user && user.profileImageUrl) {
      try {
        // Extract filename from the profile image URL
        const oldImageMatch = user.profileImageUrl.match(/\/uploads\/([^?#]+)/);
        if (oldImageMatch && oldImageMatch[1]) {
          const oldFilename = decodeURIComponent(oldImageMatch[1]);
          const oldFilePath = path.join(__dirname, '..', 'uploads', oldFilename);

          // Check if file exists and delete it
          if (fs.existsSync(oldFilePath)) {
            fs.unlinkSync(oldFilePath);
          }
        }
      } catch (deleteError) {
        // Continue with upload even if deletion fails
      }
    }
    
    // Construct the URL for the new image
    const newImageUrl = `${req.protocol}://${req.get('host')}/uploads/${req.file.filename}`;
    console.log('Upload: Constructed image URL:', newImageUrl);
    console.log('Upload: File details:', {
      filename: req.file.filename,
      originalname: req.file.originalname,
      size: req.file.size,
      path: req.file.path
    });

    // Update the user's profileImageUrl in the database
    user.profileImageUrl = newImageUrl;
    await user.save();
    console.log('Upload: Updated user profileImageUrl to:', user.profileImageUrl);

    // Return the URL to the new image from the updated user object
    res.status(200).json({ imageUrl: user.profileImageUrl });
  } catch (error) {
    res.status(500).json({ message: 'Error uploading image', error: error.message });
  }
});

// Add a debug route for token inspection without authentication
router.get('/debug-token', (req, res) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader) {
      return res.status(400).json({
        success: false,
        message: 'No Authorization header provided'
      });
    }
    
    // Check token format
    const hasBearer = authHeader.startsWith('Bearer ');
    const token = hasBearer ? authHeader.slice(7) : authHeader;
    
    // Return info about the token without verifying it
    return res.status(200).json({
      success: true,
      message: 'Token inspection',
      hasBearer: hasBearer,
      tokenLength: token.length,
      tokenPreview: token.substring(0, 10) + '...',
      fullHeader: authHeader
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Server error during token inspection'
    });
  }
});

// Add a debug route to verify token
router.get('/verify-token', protect, (req, res) => {
  try {
    // If we get here, the token is valid because protect middleware has verified it
    return res.status(200).json({
      success: true,
      message: 'Token is valid',
      user: {
        _id: req.user._id,
        name: req.user.name,
        email: req.user.email,
        role: req.user.role
      }
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Server error during token verification'
    });
  }
});

// This is a new route for verifying a token
router.post('/verify-token', (req, res) => {
  const { token } = req.body;
  if (!token) {
    return res.status(400).json({ success: false, message: 'Token is required' });
  }

  const tokenValue = token.startsWith('Bearer ') ? token.split(' ')[1] : token;

  try {
    const decoded = jwt.verify(tokenValue, process.env.JWT_SECRET);
    res.json({ success: true, decoded });
  } catch (error) {
    res.status(401).json({ success: false, message: 'Token is not valid', error: error.message });
  }
});

module.exports = router;