import React from 'react';
import { Box, Typography, Paper, useTheme } from '@mui/material';
import { format } from 'date-fns';
import UserAvatar from '../common/UserAvatar';
import MessageStatus from './MessageStatus';

const MessageBubble = ({
  message,
  isOwnMessage,
  showSender = false,
  isFirstInGroup = true,
  isLastInGroup = true,
  conversation = null
}) => {
  const theme = useTheme();

  // Format timestamp
  const formatTime = (timestamp) => {
    if (!timestamp) return '';
    return format(new Date(timestamp), 'h:mm a');
  };
  
  return (
    <Box 
      sx={{ 
        display: 'flex',
        flexDirection: isOwnMessage ? 'row-reverse' : 'row',
        mb: isLastInGroup ? 2 : 0.5,
        mt: isFirstInGroup ? 1 : 0
      }}
    >
      {/* Avatar for sender (only show for first message in group) */}
      {!isOwnMessage && isFirstInGroup && showSender && (
        <Box sx={{ mr: 1, alignSelf: 'flex-end', mb: 1 }}>
          <UserAvatar 
            user={message.sender}
            size={32}
          />
        </Box>
      )}
      
      {/* Message content */}
      <Box 
        sx={{ 
          maxWidth: '70%',
          display: 'flex',
          flexDirection: 'column',
          alignItems: isOwnMessage ? 'flex-end' : 'flex-start'
        }}
      >
        {/* Sender name (only show for first message in group) */}
        {!isOwnMessage && isFirstInGroup && showSender && (
          <Typography 
            variant="caption" 
            color="textSecondary"
            sx={{ ml: 1, mb: 0.5 }}
          >
            {message.sender.name}
          </Typography>
        )}
        
        {/* Message bubble */}
        <Paper
          elevation={0}
          sx={{
            p: 1.5,
            borderRadius: 2,
            bgcolor: isOwnMessage 
              ? theme.palette.primary.main 
              : theme.palette.background.paper,
            color: isOwnMessage ? 'white' : 'inherit',
            position: 'relative'
          }}
        >
          <Typography variant="body2">
            {message.text}
          </Typography>
          
          {/* Timestamp and read status */}
          <Box 
            sx={{ 
              display: 'flex', 
              alignItems: 'center',
              justifyContent: 'flex-end',
              mt: 0.5
            }}
          >
            <Typography
              variant="caption"
              color={isOwnMessage ? 'rgba(255, 255, 255, 0.7)' : 'text.secondary'}
              sx={{ fontSize: '0.7rem', mr: 0.5 }}
            >
              {formatTime(message.createdAt)}
            </Typography>

            {isOwnMessage && (
              <MessageStatus
                message={message}
                conversation={conversation}
                isOwnMessage={isOwnMessage}
              />
            )}
          </Box>
        </Paper>
      </Box>
    </Box>
  );
};

export default MessageBubble; 