const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs');

const router = express.Router();

// Ensure uploads directory exists
const uploadsDir = path.join(__dirname, '..', 'uploads');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

// Multer storage configuration
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, uploadsDir); // Save files to the 'uploads' directory
  },
  filename: function (req, file, cb) {
    // Create a unique filename to avoid overwrites
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

// File filter for multer - allow images and documents
const fileFilter = (req, file, cb) => {
  // Allow common file formats including documents
  const allowedMimeTypes = [
    'image/jpeg',
    'image/jpg',
    'image/png',
    'image/gif',
    'image/webp',
    'image/svg+xml',
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
  ];

  if (allowedMimeTypes.includes(file.mimetype)) {
    console.log(`File filter: Accepting ${file.mimetype} file: ${file.originalname}`);
    cb(null, true);
  } else {
    console.log(`File filter: Rejecting ${file.mimetype} file: ${file.originalname}`);
    cb(new Error(`File type ${file.mimetype} not supported. Please upload images, PDFs, or Word documents only.`), false);
  }
};

const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: { fileSize: 2 * 1024 * 1024 } // 2MB limit
});

// @route   POST /api/uploads/single
// @desc    Upload a single file
// @access  Private (should be protected by auth middleware)
router.post('/single', (req, res) => {
  upload.single('file')(req, res, (err) => {
    if (err) {
      console.error('Upload error:', err);

      if (err instanceof multer.MulterError) {
        if (err.code === 'LIMIT_FILE_SIZE') {
          return res.status(400).json({ message: 'File too large. Maximum size is 2MB.' });
        }
        return res.status(400).json({ message: `Upload error: ${err.message}` });
      }

      // Custom file filter error
      return res.status(400).json({ message: err.message });
    }

    if (!req.file) {
      return res.status(400).json({ message: 'No file uploaded.' });
    }

    // Construct the URL for the uploaded file
    // The file is served statically from /uploads by server.js
    const fileUrl = `/uploads/${req.file.filename}`;

    console.log('File uploaded successfully:', {
      filename: req.file.filename,
      url: fileUrl,
      size: req.file.size,
      mimetype: req.file.mimetype
    });

    res.status(201).json({
      message: 'File uploaded successfully.',
      fileName: req.file.filename,
      filePath: req.file.path, // Absolute path on server
      url: fileUrl, // URL to access the file
      path: fileUrl, // Also include path for compatibility
      mimetype: req.file.mimetype,
      size: req.file.size
    });
  });
});


// @route   POST /api/uploads/signup-image
// @desc    Upload a single image during signup (unprotected)
// @access  Public
router.post('/signup-image', upload.single('image'), (req, res) => {
  if (!req.file) {
    return res.status(400).json({ message: 'No file uploaded for signup.' });
  }

  // Construct the relative URL for the uploaded file (consistent with other endpoints)
  const imageUrl = `/uploads/${req.file.filename}`;

  console.log('Signup image upload successful:', {
    filename: req.file.filename,
    imageUrl: imageUrl,
    mimetype: req.file.mimetype,
    size: req.file.size
  });

  res.status(201).json({
    message: 'Signup image uploaded successfully.',
    imageUrl: imageUrl, // Return the relative URL for consistency
    fileName: req.file.filename,
    mimetype: req.file.mimetype,
    size: req.file.size
  });
});

module.exports = router;
