/**
 * Security Audit and Monitoring System
 * Tracks security events, vulnerabilities, and generates reports
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

class SecurityAudit {
  constructor() {
    this.events = [];
    this.maxEvents = 10000;
    this.logFile = path.join(__dirname, '../logs/security.log');
    this.alertThresholds = {
      failedLogins: 5,
      suspiciousRequests: 10,
      fileUploadFailures: 3
    };
    
    // Ensure log directory exists
    this.ensureLogDirectory();
  }

  /**
   * Ensure log directory exists
   */
  ensureLogDirectory() {
    const logDir = path.dirname(this.logFile);
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true, mode: 0o755 });
    }
  }

  /**
   * Log security event
   */
  logEvent(type, details, severity = 'info', userId = null, ip = null) {
    const event = {
      id: crypto.randomUUID(),
      timestamp: new Date().toISOString(),
      type,
      severity,
      details,
      userId,
      ip,
      userAgent: details.userAgent || null
    };

    // Add to memory
    this.events.unshift(event);
    if (this.events.length > this.maxEvents) {
      this.events.pop();
    }

    // Write to file
    this.writeToFile(event);

    // Check for alerts
    this.checkAlerts(type, ip, userId);

    return event.id;
  }

  /**
   * Write event to log file
   */
  writeToFile(event) {
    try {
      const logEntry = JSON.stringify(event) + '\n';
      fs.appendFileSync(this.logFile, logEntry);
    } catch (error) {
      console.error('Failed to write security log:', error);
    }
  }

  /**
   * Check for security alerts
   */
  checkAlerts(type, ip, userId) {
    const now = Date.now();
    const timeWindow = 15 * 60 * 1000; // 15 minutes

    // Count recent events of same type
    const recentEvents = this.events.filter(event => {
      const eventTime = new Date(event.timestamp).getTime();
      return (now - eventTime) < timeWindow && 
             event.type === type && 
             (event.ip === ip || event.userId === userId);
    });

    const threshold = this.alertThresholds[type];
    if (threshold && recentEvents.length >= threshold) {
      this.triggerAlert(type, recentEvents, ip, userId);
    }
  }

  /**
   * Trigger security alert
   */
  triggerAlert(type, events, ip, userId) {
    const alert = {
      id: crypto.randomUUID(),
      timestamp: new Date().toISOString(),
      type: 'SECURITY_ALERT',
      alertType: type,
      severity: 'high',
      eventCount: events.length,
      ip,
      userId,
      details: `${events.length} ${type} events detected in 15 minutes`
    };

    console.warn('🚨 SECURITY ALERT:', alert);

    // Log the alert
    this.logEvent('SECURITY_ALERT', alert, 'high', userId, ip);

    // In production, send to monitoring service
    if (process.env.NODE_ENV === 'production') {
      this.sendToMonitoring(alert);
    }
  }

  /**
   * Send alert to monitoring service
   */
  sendToMonitoring(alert) {
    // Placeholder for monitoring service integration
    // In production, integrate with services like:
    // - Slack/Discord webhooks
    // - Email alerts
    // - PagerDuty
    // - Datadog/New Relic
    console.log('Alert sent to monitoring service:', alert.id);
  }

  /**
   * Get security events with filtering
   */
  getEvents(filters = {}) {
    let filteredEvents = [...this.events];

    if (filters.type) {
      filteredEvents = filteredEvents.filter(event => event.type === filters.type);
    }

    if (filters.severity) {
      filteredEvents = filteredEvents.filter(event => event.severity === filters.severity);
    }

    if (filters.userId) {
      filteredEvents = filteredEvents.filter(event => event.userId === filters.userId);
    }

    if (filters.ip) {
      filteredEvents = filteredEvents.filter(event => event.ip === filters.ip);
    }

    if (filters.since) {
      const sinceTime = new Date(filters.since).getTime();
      filteredEvents = filteredEvents.filter(event => {
        return new Date(event.timestamp).getTime() >= sinceTime;
      });
    }

    return filteredEvents.slice(0, filters.limit || 100);
  }

  /**
   * Generate security report
   */
  generateReport(timeframe = '24h') {
    const now = Date.now();
    let timeWindow;

    switch (timeframe) {
      case '1h':
        timeWindow = 60 * 60 * 1000;
        break;
      case '24h':
        timeWindow = 24 * 60 * 60 * 1000;
        break;
      case '7d':
        timeWindow = 7 * 24 * 60 * 60 * 1000;
        break;
      default:
        timeWindow = 24 * 60 * 60 * 1000;
    }

    const recentEvents = this.events.filter(event => {
      const eventTime = new Date(event.timestamp).getTime();
      return (now - eventTime) < timeWindow;
    });

    // Group by type
    const eventsByType = {};
    const eventsBySeverity = {};
    const topIPs = {};
    const topUsers = {};

    recentEvents.forEach(event => {
      // By type
      eventsByType[event.type] = (eventsByType[event.type] || 0) + 1;

      // By severity
      eventsBySeverity[event.severity] = (eventsBySeverity[event.severity] || 0) + 1;

      // By IP
      if (event.ip) {
        topIPs[event.ip] = (topIPs[event.ip] || 0) + 1;
      }

      // By user
      if (event.userId) {
        topUsers[event.userId] = (topUsers[event.userId] || 0) + 1;
      }
    });

    return {
      timeframe,
      totalEvents: recentEvents.length,
      eventsByType: Object.entries(eventsByType)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 10),
      eventsBySeverity,
      topIPs: Object.entries(topIPs)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 10),
      topUsers: Object.entries(topUsers)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 10),
      alerts: recentEvents.filter(event => event.type === 'SECURITY_ALERT').length
    };
  }

  /**
   * Security health check
   */
  healthCheck() {
    const now = Date.now();
    const last24h = 24 * 60 * 60 * 1000;

    const recentEvents = this.events.filter(event => {
      const eventTime = new Date(event.timestamp).getTime();
      return (now - eventTime) < last24h;
    });

    const criticalEvents = recentEvents.filter(event => 
      event.severity === 'critical' || event.type === 'SECURITY_ALERT'
    );

    const highSeverityEvents = recentEvents.filter(event => 
      event.severity === 'high'
    );

    let status = 'healthy';
    const issues = [];

    if (criticalEvents.length > 0) {
      status = 'critical';
      issues.push(`${criticalEvents.length} critical security events in last 24h`);
    } else if (highSeverityEvents.length > 10) {
      status = 'warning';
      issues.push(`${highSeverityEvents.length} high severity events in last 24h`);
    }

    return {
      status,
      issues,
      totalEvents: recentEvents.length,
      criticalEvents: criticalEvents.length,
      highSeverityEvents: highSeverityEvents.length
    };
  }

  /**
   * Clean up old events
   */
  cleanup(maxAge = 30 * 24 * 60 * 60 * 1000) { // 30 days
    const cutoff = Date.now() - maxAge;
    
    this.events = this.events.filter(event => {
      const eventTime = new Date(event.timestamp).getTime();
      return eventTime > cutoff;
    });

    console.log(`Security audit cleanup: ${this.events.length} events retained`);
  }
}

// Create singleton instance
const securityAudit = new SecurityAudit();

// Predefined event types
const SECURITY_EVENTS = {
  LOGIN_SUCCESS: 'login_success',
  LOGIN_FAILURE: 'login_failure',
  LOGOUT: 'logout',
  PASSWORD_CHANGE: 'password_change',
  ACCOUNT_LOCKED: 'account_locked',
  SUSPICIOUS_REQUEST: 'suspicious_request',
  FILE_UPLOAD_SUCCESS: 'file_upload_success',
  FILE_UPLOAD_FAILURE: 'file_upload_failure',
  UNAUTHORIZED_ACCESS: 'unauthorized_access',
  ADMIN_ACTION: 'admin_action',
  DATA_EXPORT: 'data_export',
  SECURITY_SCAN: 'security_scan',
  RATE_LIMIT_EXCEEDED: 'rate_limit_exceeded',
  CSRF_VIOLATION: 'csrf_violation',
  XSS_ATTEMPT: 'xss_attempt',
  SQL_INJECTION_ATTEMPT: 'sql_injection_attempt'
};

// Helper functions
const logSecurityEvent = (type, details, severity = 'info', req = null) => {
  const userId = req?.user?._id?.toString() || null;
  const ip = req?.ip || req?.connection?.remoteAddress || null;
  const userAgent = req?.get('User-Agent') || null;

  return securityAudit.logEvent(type, {
    ...details,
    userAgent
  }, severity, userId, ip);
};

// Schedule cleanup every 24 hours
setInterval(() => {
  securityAudit.cleanup();
}, 24 * 60 * 60 * 1000);

module.exports = {
  securityAudit,
  SECURITY_EVENTS,
  logSecurityEvent
};
