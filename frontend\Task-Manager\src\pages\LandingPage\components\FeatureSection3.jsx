import React from 'react';

/**
 * Third feature section component for the landing page
 * @returns {JSX.Element} - Rendered component
 */
const FeatureSection3 = () => {
  return (
    <section className="py-20 md:py-28 px-6 sm:px-8 md:px-16 bg-white">
      <div className="container mx-auto flex flex-col lg:flex-row items-center gap-16 lg:gap-20">
        {/* Feature Content */}
        <div className="lg:w-1/2 lg:order-1 text-center lg:text-left" data-aos="fade-right" data-aos-duration="800" data-aos-delay="150">
          <span className="text-blue-600 font-semibold text-sm tracking-wider uppercase">FEATURES —</span>
          <h2 className="text-4xl sm:text-5xl font-extrabold text-slate-900 mt-3 mb-7 leading-tight">Organize, prioritize, <br className="hidden sm:block" />Achieve goals</h2>
          <p className="text-slate-600 mb-9 text-lg leading-relaxed">Streamline your workflow with powerful, intuitive tools that ensure every member stays organized, aligned, and focused on their tasks.</p>
          <a href="#" className="inline-block px-6 py-3 bg-slate-900 text-white font-medium rounded-lg hover:bg-slate-800 transition-colors duration-300">Get a free consultation</a>
        </div>
        
        {/* Feature UI - Task Card */}
        <div className="lg:w-1/2 lg:order-2" data-aos="fade-left" data-aos-duration="800">
          <div className="bg-gray-50 p-6 rounded-2xl shadow-lg max-w-md mx-auto lg:ml-auto">
            {/* Card Header */}
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-2">
                <span className="px-3 py-1 text-xs font-medium text-gray-700 bg-white rounded-full border border-gray-200">Low Priority</span>
                <span className="px-3 py-1 text-xs font-medium text-white bg-blue-500 rounded-full">Front-End Task</span>
              </div>
              <div className="flex -space-x-2">
                <div className="flex items-center justify-center w-6 h-6 rounded-full bg-gray-200 border-2 border-white text-xs font-medium text-gray-600">+3</div>
                <div className="w-6 h-6 rounded-full bg-pink-300 border-2 border-white"></div>
                <div className="w-6 h-6 rounded-full bg-yellow-300 border-2 border-white"></div>
              </div>
            </div>
            
            {/* Card Content */}
            <h3 className="text-lg font-semibold text-gray-800 mb-1">Ensure Responsive Design</h3>
            <p className="text-sm text-gray-600 mb-4">Test and adjust the UI for various screen sizes and devices.</p>
            
            {/* Progress Bar */}
            <div className="mb-1">
              <div className="w-full bg-blue-100 rounded-full h-2">
                <div className="bg-blue-500 h-2 rounded-full" style={{width: "75%"}}></div>
              </div>
            </div>
            <div className="flex justify-between text-xs text-gray-500 mb-6">
              <span>Progress</span>
              <span>75%</span>
            </div>
            
            {/* Card Footer */}
            <div className="flex items-center justify-between pt-3 border-t border-gray-200">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 rounded-full bg-blue-200 flex items-center justify-center overflow-hidden">
                  <img src="https://i.pravatar.cc/40?u=adamdoe" alt="Adam Doe" className="w-full h-full object-cover" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-800">Adam Doe</p>
                  <p className="text-xs text-gray-500">Lead Designer</p>
                </div>
              </div>
              
              <div className="flex items-center gap-4 text-gray-500">
                <div className="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clipRule="evenodd" />
                  </svg>
                  <span className="text-sm">9</span>
                </div>
                
                <div className="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M8 9a3 3 0 100-6 3 3 0 000 6zM8 11a6 6 0 016 6H2a6 6 0 016-6zM16 7a1 1 0 10-2 0v1h-1a1 1 0 100 2h1v1a1 1 0 102 0v-1h1a1 1 0 100-2h-1V7z" />
                  </svg>
                  <span className="text-sm">7</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FeatureSection3;
