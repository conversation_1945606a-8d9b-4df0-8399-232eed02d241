const express = require('express');
const { enhancedAdminOnly } = require('../middleware/enhancedAuthMiddleware');
const { getUsers, getUserById, deleteUser } = require('../controllers/userController');
const router = express.Router();

// User Management Routes
// Note: enhancedProtect is applied to all routes in this file from server.js

// Admin-only route to get all users
router.get('/', getUsers);

// Admin-only route to delete a user
router.delete('/:id', enhancedAdminOnly, deleteUser);

// Route for any authenticated user to get another user's details by ID
router.get('/:id', getUserById);

module.exports = router;
