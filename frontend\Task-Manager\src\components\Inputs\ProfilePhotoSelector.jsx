import React, { useRef, useState, useEffect, useCallback } from "react";
import { LuUser, LuUpload, LuTrash } from "react-icons/lu";
import ProxyImage from "../common/ProxyImage";

const ProfilePhotoSelector = ({ image, setImage }) => {
  const inputRef = useRef(null);
  const [previewUrl, setPreviewUrl] = useState(null);
  
  // Initialize previewUrl from image prop when component mounts or image prop changes
  useEffect(() => {
    // If image is a string (URL), use it for preview but don't create a new object URL
    if (typeof image === 'string' && image) {
      setPreviewUrl(null); // Clear any existing object URL to avoid memory leaks
    } else if (!image && previewUrl) {
      // If image is null/undefined but we have a previewUrl, revoke and clear it
      URL.revokeObjectURL(previewUrl);
      setPreviewUrl(null);
    }
    // Note: we don't set previewUrl for File objects here because that's handled in handleImageChange
  }, [image]);

  const handleImageChange = useCallback((event) => {
    event.stopPropagation(); // Prevent event bubbling
    const file = event.target.files[0];
    if (file) {
      // Revoke previous object URL to prevent memory leaks
      if (previewUrl && !previewUrl.startsWith('http')) {
        URL.revokeObjectURL(previewUrl);
      }
      
      // Create new preview URL
      const preview = URL.createObjectURL(file);
      setPreviewUrl(preview);
      setImage(file); // Send File object to parent component
    }
  }, [previewUrl, setImage]);

  const handleRemoveImage = useCallback(() => {
    // Revoke object URL to prevent memory leaks
    if (previewUrl && !previewUrl.startsWith('http')) {
      URL.revokeObjectURL(previewUrl);
    }
    setImage(null); // Tell parent component image was removed
    setPreviewUrl(null);
  }, [previewUrl, setImage]);

  const onChooseFile = useCallback((e) => {
    e.stopPropagation(); // Prevent event bubbling
    inputRef.current.click();
  }, []);


  return (
    <div className="flex flex-col items-center mb-2">
      <input
        type="file"
        accept="image/*"
        ref={inputRef}
        onChange={handleImageChange}
        className="hidden"
      />

      {!image ? ( // Conditionally render if no image is selected (image is null/falsy)
        <div 
          className="w-20 h-20 flex items-center justify-center bg-blue-100/50 rounded-full relative cursor-pointer"
          onClick={(e) => onChooseFile(e)}
        >
          <LuUser className="text-4xl text-primary" />
          <div className="absolute -bottom-1 -right-1">
            <button
              type="button"
              className="w-6 h-6 flex items-center justify-center bg-primary text-white rounded-full border border-white shadow-sm"
              onClick={(e) => onChooseFile(e)}
            >
              <LuUpload size={14} />
            </button>
          </div>
        </div>
      ) : ( // Render if an image is selected
        <div className="relative">
          <div className="w-20 h-20 rounded-full overflow-hidden border-2 border-gray-200">
            {typeof image === 'string' ? (
              <ProxyImage
                src={image} // Existing image URL (string)
                alt="Profile"
                className="w-full h-full object-cover"
                fallbackText={"P"} // Fallback for existing string URLs, as per original logic
                containerClassName="w-full h-full"
              />
            ) : previewUrl ? ( // image is a File object, previewUrl is its blob URL
              <img
                src={previewUrl}
                alt="Preview"
                className="w-full h-full object-cover"
                onError={(e) => { console.error('Direct <img> preview error for blob URL. File name: ' + (image?.name || 'unknown'), e); }}
              />
            ) : (
              // Fallback if image is a File but previewUrl isn't ready or other unexpected state
              <div className="w-full h-full flex items-center justify-center bg-gray-100 text-gray-400">
                {image && typeof image === 'object' && image.name ? image.name.charAt(0).toUpperCase() : <LuUser size={30} />}
              </div>
            )}
          </div>
          <div className="absolute -bottom-1 -right-1">
            <button
              type="button"
              className="w-6 h-6 flex items-center justify-center bg-red-500 text-white rounded-full border border-white shadow-sm"
              onClick={(e) => {
                e.stopPropagation();
                handleRemoveImage();
              }}
            >
              <LuTrash size={14} />
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProfilePhotoSelector;