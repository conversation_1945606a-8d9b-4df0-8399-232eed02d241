import React, { useState, useEffect, useCallback } from "react";
import { useParams, useNavigate } from "react-router-dom";
import DashboardLayout from "../../components/layout/DashboardLayout";
import axiosInstance from "../../utils/axiosInstance";
import { API_PATHS } from "../../utils/apiPaths";
import TaskDetailModal from "../../components/modals/TaskDetailModal";
import toast from "react-hot-toast";
import { useUser } from "../../contexts/userContext";
import { LuArrowLeft } from "react-icons/lu";

const ViewTaskDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { user } = useUser();

  const [task, setTask] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fetch task data
  const fetchTaskData = useCallback(async () => {
    if (!id) {
      setError("No task ID provided");
      setLoading(false);
      return;
    }

    try {
      const response = await axiosInstance.get(API_PATHS.TASKS.GET_BY_ID(id));

      // Handle both response formats: { task: {...} } and direct task object
      let taskData;
      if (response.data && response.data.task) {
        taskData = response.data.task;
      } else if (response.data && response.data._id) {
        // Direct task object response
        taskData = response.data;
      } else {
        throw new Error("Invalid response format: missing task data");
      }

      setTask(taskData);
    } catch (error) {
      console.error('Error fetching task:', error);

      // Handle the specific case where the user is unassigned from the task
      if (error.response && error.response.data && error.response.data.unassigned) {
        toast.error("This task has been reassigned.");
        navigate('/user/tasks');
        return; // Stop further execution
      }

      setError('Failed to load task data');
      toast.error('Failed to load task data');
    } finally {
      setLoading(false);
    }
  }, [id, navigate]);

  useEffect(() => {
    fetchTaskData();
  }, [fetchTaskData]);

  const handleEditTask = useCallback((taskData) => {
    if (taskData && taskData._id) {
      navigate(`/user/edit-task/${taskData._id}`, {
        state: { taskToEdit: taskData }
      });
    } else {
      toast.error("Could not edit task: Missing task ID");
    }
  }, [navigate]);

  const handleClose = useCallback(() => {
    navigate('/user/tasks');
  }, [navigate]);

  if (loading) {
    return (
      <DashboardLayout activeMenu="My Tasks">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-16 w-16 border-t-4 border-blue-500"></div>
        </div>
      </DashboardLayout>
    );
  }

  if (error || !task) {
    return (
      <DashboardLayout activeMenu="My Tasks">
        <div className="flex flex-col items-center justify-center h-64">
          <div className="text-red-500 text-xl mb-4">Error Loading Task</div>
          <p className="text-gray-600 mb-4">{error || "Task not found"}</p>
          <button
            onClick={() => navigate('/user/tasks')}
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            <LuArrowLeft />
            Back to Tasks
          </button>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout activeMenu="My Tasks">
      <div className="flex items-center gap-3 mb-6">
        <button
          onClick={() => navigate('/user/tasks')}
          className="p-2 text-gray-500 hover:text-gray-700 rounded-full hover:bg-gray-100"
        >
          <LuArrowLeft className="text-xl" />
        </button>
        <h1 className="text-2xl font-semibold">Task Details</h1>
      </div>

      {/* Use the existing TaskDetailModal component but render it inline */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <TaskDetailModal
          isOpen={true}
          onClose={handleClose}
          task={task}
          onEdit={() => handleEditTask(task)}
          onTaskUpdate={fetchTaskData}
          isEditing={true}
        />
      </div>
    </DashboardLayout>
  );
};

export default ViewTaskDetails;