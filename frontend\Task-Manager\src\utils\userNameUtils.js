/**
 * Utility functions for consistent user name handling across the chat system
 */
import { BASE_URL } from './apiPaths';

/**
 * Get display name for a user with multiple fallbacks
 * @param {Object} user - User object
 * @returns {string} Display name
 */
export const getUserDisplayName = (user) => {
  if (!user) return 'Unknown User';

  // Try multiple name sources in order of preference
  return user.name ||
         user.displayName ||
         user.email?.split('@')[0] ||
         `User ${user._id?.slice(-4) || 'Unknown'}`;
};

/**
 * Get conversation display name based on type and participants
 * @param {Object} conversation - Conversation object
 * @param {Object} currentUser - Current user object
 * @returns {string} Conversation display name
 */
export const getConversationDisplayName = (conversation, currentUser) => {
  if (!conversation) return 'Unknown Conversation';

  // Get current user ID - handle both 'id' and '_id' fields
  const currentUserId = currentUser?._id || currentUser?.id;

  if (conversation.type === 'group') {
    return conversation.name || `Group Chat (${conversation.participants?.length || 0} members)`;
  }

  // For individual conversations, find the other participant using the same validation as getValidOtherParticipants
  const validOtherParticipants = getValidOtherParticipants(conversation, { _id: currentUserId, id: currentUserId });
  const otherParticipant = validOtherParticipants[0];

  if (!otherParticipant) {
    return 'Unknown User';
  }

  return getUserDisplayName(otherParticipant);
};

/**
 * Validate if a participant has sufficient data for display
 * @param {Object} participant - Participant object
 * @param {Object} currentUser - Current user object (to exclude from validation)
 * @returns {boolean} Whether participant is valid
 */
export const isValidParticipant = (participant, currentUser) => {
  return participant && 
         participant._id && 
         participant._id !== currentUser?._id &&
         (participant.name || participant.email);
};

/**
 * Get valid other participants from a conversation
 * @param {Object} conversation - Conversation object
 * @param {Object} currentUser - Current user object
 * @returns {Array} Array of valid other participants
 */
export const getValidOtherParticipants = (conversation, currentUser) => {
  if (!conversation?.participants) return [];
  
  return conversation.participants.filter(p => isValidParticipant(p, currentUser));
};

/**
 * Get initials from user data with fallbacks
 * @param {Object} user - User object
 * @returns {string} User initials
 */
export const getUserInitials = (user) => {
  if (!user) return '?';
  
  const name = getUserDisplayName(user);
  
  if (!name || name === 'Unknown User') {
    return user._id ? user._id.slice(-2).toUpperCase() : '?';
  }
  
  const nameParts = name.split(' ').filter(part => part.length > 0);
  
  if (nameParts.length === 0) return '?';
  if (nameParts.length === 1) {
    return nameParts[0].charAt(0).toUpperCase();
  }
  
  return `${nameParts[0].charAt(0)}${nameParts[nameParts.length - 1].charAt(0)}`.toUpperCase();
};

/**
 * Get user avatar URL with enhanced fallbacks and proper URL handling
 * @param {Object} user - User object
 * @returns {string} Avatar URL
 */
export const getUserAvatarUrl = (user) => {
  if (!user) {
    return null;
  }

  // Try multiple image sources
  const imageUrl = user.profileImageUrl ||
                  user.profileImage ||
                  user.avatar ||
                  user.picture;

  if (imageUrl) {
    // Handle relative URLs by prepending base URL
    if (imageUrl.startsWith('/')) {
      
      const fullUrl = `${BASE_URL}${imageUrl}`;
      return fullUrl;
    }

    // Handle full URLs
    if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
      return imageUrl;
    }

    // Handle other relative paths
    
    const fullUrl = `${BASE_URL}/${imageUrl}`;
    return fullUrl;
  }

  // Generate fallback avatar URL using ui-avatars service
  const displayName = getUserDisplayName(user);
  const fallbackUrl = `https://ui-avatars.com/api/?name=${encodeURIComponent(displayName)}&background=random&color=ffffff&size=128`;
  return fallbackUrl;
};





/**
 * Validate user data quality and log issues
 * @param {Object} user - User object to validate
 * @param {string} context - Context for logging
 * @returns {Object} Validation result
 */
export const validateUserData = (user, context = 'Unknown') => {
  const issues = [];
  const warnings = [];

  if (!user) {
    issues.push('User object is null/undefined');
    return { valid: false, issues, warnings };
  }

  if (!user._id) {
    issues.push('User missing _id field');
  }

  if (!user.name && !user.email) {
    issues.push('User missing both name and email fields');
  }

  if (!user.name) {
    warnings.push('User missing name field');
  }

  if (!user.email) {
    warnings.push('User missing email field');
  }

  if (!user.profileImageUrl && !user.profileImage && !user.avatar && !user.picture) {
    warnings.push('User has no profile image fields');
  }

  const result = {
    valid: issues.length === 0,
    issues,
    warnings,
    user
  };



  return result;
};

export default {
  getUserDisplayName,
  getConversationDisplayName,
  isValidParticipant,
  getValidOtherParticipants,
  getUserInitials,
  getUserAvatarUrl,
  validateUserData
};
