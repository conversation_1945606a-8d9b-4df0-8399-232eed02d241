import React, { useState, useEffect, useMemo, useCallback } from "react";
import Modal from "../modals/Modal";
import Portal from "../common/Portal";
import AvatarGroup from "../AvatarGroup";
import { LuPlus } from "react-icons/lu";

const SelectUsers = ({ selectedUsers = [], onChange = () => {}, disabled = false, allUsers = [] }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  
  // Log initial props
  useEffect(() => {
    console.group('SelectUsers Mount');
    console.log('Initial selectedUsers:', selectedUsers);
    console.log('Initial allUsers:', allUsers);
    console.groupEnd();
  }, []);

  // Normalize and memoize selected users
  const selectedUserObjects = useMemo(() => {
    console.log('Processing selected users:', selectedUsers);
    if (!Array.isArray(selectedUsers)) return [];
    
    return selectedUsers.map(user => {
      if (typeof user === 'string') {
        // If user is an ID, find the full user object
        const fullUser = allUsers.find(u => u._id === user);
        return fullUser || { _id: user, name: 'Unknown User' };
      }
      return user;
    }).filter(Boolean);
  }, [selectedUsers, allUsers]);

  // Keep track of local selections
  const [tempSelectedIds, setTempSelectedIds] = useState(() => {
    // Initialize from selectedUsers prop
    const initialIds = new Set();
    if (Array.isArray(selectedUsers)) {
      selectedUsers.forEach(user => {
        const userId = typeof user === 'string' ? user : user._id;
        if (userId) initialIds.add(userId);
      });
    }
    return initialIds;
  });

  // Update local state when selectedUsers prop changes
  useEffect(() => {
    console.log('Selected users prop changed:', {
      selectedUsers,
      selectedUserObjects,
      currentTempIds: Array.from(tempSelectedIds)
    });

    // Create a new Set of IDs from the current selectedUsers
    const newIds = new Set();
    if (Array.isArray(selectedUsers)) {
      selectedUsers.forEach(user => {
        const userId = typeof user === 'string' ? user : user._id;
        if (userId) newIds.add(userId);
      });
    }

    // Only update if the IDs have actually changed
    const currentIds = Array.from(tempSelectedIds).sort().join(',');
    const newIdsString = Array.from(newIds).sort().join(',');

    if (currentIds !== newIdsString) {
      console.log('Updating tempSelectedIds:', {
        from: Array.from(tempSelectedIds),
        to: Array.from(newIds)
      });
      setTempSelectedIds(newIds);
    }
  }, [selectedUsers]);

  const toggleUserSelection = useCallback((userId) => {
    setTempSelectedIds(prev => {
      const next = new Set(prev);
      if (next.has(userId)) {
        next.delete(userId);
      } else {
        next.add(userId);
      }
      return next;
    });
  }, []);

  const handleConfirmSelection = useCallback((e) => {
    e?.stopPropagation?.();
    // Convert selected IDs back to user objects
    const selectedUsers = Array.from(tempSelectedIds).map(id => {
      const user = allUsers.find(u => u._id === id);
      return user || { _id: id, name: 'Unknown User' };
    });
    
    console.log('Confirming selection:', {
      selectedIds: Array.from(tempSelectedIds),
      selectedUsers: selectedUsers
    });
    onChange(selectedUsers);
    setIsModalOpen(false);
  }, [tempSelectedIds, allUsers, onChange]);

  return (
    <div className="relative w-full">
      <div 
        className={`form-input flex items-center justify-between h-12 ${disabled ? 'bg-gray-100' : 'cursor-pointer hover:border-blue-500'}`}
        onClick={() => !disabled && setIsModalOpen(true)}
      >
        <div className="flex items-center justify-between w-full">
          <div className="flex-1 min-w-0">
            {selectedUserObjects.length === 0 ? (
              <span className="text-gray-500">Select users</span>
            ) : (
              <div className="flex items-center gap-2">
                <AvatarGroup avatars={selectedUserObjects} maxVisible={4} size="sm" />
                <span className="text-sm text-gray-600">
                  {selectedUserObjects.length} user{selectedUserObjects.length !== 1 ? 's' : ''} selected
                </span>
              </div>
            )}
          </div>
          <LuPlus className={`text-gray-400 ml-2 ${disabled ? 'hidden' : ''}`} />
        </div>
      </div>

      {!disabled && (
        <Portal>
          <Modal
            isOpen={isModalOpen}
            onClose={() => setIsModalOpen(false)}
            title="Assign Users"
          >
            <div className="max-h-80 overflow-y-auto p-1">
              {allUsers.length > 0 ? (
                allUsers.map((user) => (
                  <div 
                    key={user._id}
                    className="flex items-center p-3 hover:bg-gray-100 rounded-lg cursor-pointer transition-colors"
                    onClick={() => toggleUserSelection(user._id)}
                  >
                    <input 
                      type="checkbox"
                      checked={tempSelectedIds.has(user._id)}
                      readOnly // The click is handled by the div
                      className="h-5 w-5 rounded text-blue-600 focus:ring-blue-500 border-gray-300 mr-4 pointer-events-none"
                    />
                    <AvatarGroup avatars={[user]} />
                    <span className="ml-3 text-gray-700">{user.fullName || user.name}</span>
                  </div>
                ))
              ) : (
                <div className="text-center text-gray-500 p-8">
                  No team members available to assign.
                </div>
              )}
            </div>
            
            <div className="flex justify-end mt-6 gap-3">
              <button 
                type="button"
                onClick={() => setIsModalOpen(false)}
                className="px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </button>
              <button 
                type="button"
                onClick={handleConfirmSelection}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium hover:bg-blue-700"
              >
                Confirm
              </button>
            </div>
          </Modal>
        </Portal>
      )}
    </div>
  );
};

export default SelectUsers;
