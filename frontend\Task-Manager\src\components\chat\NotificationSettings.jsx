import React, { useState, useEffect } from 'react';
import { MdNotifications, MdNotificationsOff, MdVolumeUp, MdVolumeOff } from 'react-icons/md';
import { useSocket } from '../../contexts/SocketContext';

const NotificationSettings = ({ isOpen, onClose }) => {
  const { showBrowserNotification, playNotificationSound } = useSocket();
  const [settings, setSettings] = useState({
    browserNotifications: true,
    soundNotifications: true,
    showPreview: true
  });

  // Load settings from localStorage on mount
  useEffect(() => {
    const savedSettings = localStorage.getItem('chatNotificationSettings');
    if (savedSettings) {
      try {
        setSettings(JSON.parse(savedSettings));
      } catch (error) {
        console.error('Error loading notification settings:', error);
      }
    }
  }, []);

  // Save settings to localStorage whenever they change
  useEffect(() => {
    localStorage.setItem('chatNotificationSettings', JSON.stringify(settings));
  }, [settings]);

  const handleSettingChange = (key, value) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const testNotification = () => {
    if (settings.browserNotifications) {
      showBrowserNotification(
        'Test Notification',
        'This is how chat notifications will appear',
        null
      );
    }
    
    if (settings.soundNotifications) {
      playNotificationSound();
    }
  };

  const requestNotificationPermission = async () => {
    if ('Notification' in window) {
      const permission = await Notification.requestPermission();
      if (permission === 'granted') {
        handleSettingChange('browserNotifications', true);
      }
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-lg font-semibold">Notification Settings</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 text-xl"
          >
            ×
          </button>
        </div>

        <div className="space-y-4">
          {/* Browser Notifications */}
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              {settings.browserNotifications ? (
                <MdNotifications className="text-blue-500 mr-3" size={20} />
              ) : (
                <MdNotificationsOff className="text-gray-400 mr-3" size={20} />
              )}
              <div>
                <div className="font-medium">Browser Notifications</div>
                <div className="text-sm text-gray-500">Show desktop notifications for new messages</div>
              </div>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={settings.browserNotifications}
                onChange={(e) => {
                  if (e.target.checked && Notification.permission !== 'granted') {
                    requestNotificationPermission();
                  } else {
                    handleSettingChange('browserNotifications', e.target.checked);
                  }
                }}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>

          {/* Sound Notifications */}
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              {settings.soundNotifications ? (
                <MdVolumeUp className="text-green-500 mr-3" size={20} />
              ) : (
                <MdVolumeOff className="text-gray-400 mr-3" size={20} />
              )}
              <div>
                <div className="font-medium">Sound Notifications</div>
                <div className="text-sm text-gray-500">Play sound when receiving messages</div>
              </div>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={settings.soundNotifications}
                onChange={(e) => handleSettingChange('soundNotifications', e.target.checked)}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>

          {/* Message Preview */}
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className="w-5 h-5 bg-purple-500 rounded mr-3 flex items-center justify-center">
                <span className="text-white text-xs">👁</span>
              </div>
              <div>
                <div className="font-medium">Message Preview</div>
                <div className="text-sm text-gray-500">Show message content in notifications</div>
              </div>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={settings.showPreview}
                onChange={(e) => handleSettingChange('showPreview', e.target.checked)}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>
        </div>

        {/* Test Button */}
        <div className="mt-6 pt-4 border-t">
          <button
            onClick={testNotification}
            className="w-full bg-blue-500 text-white py-2 px-4 rounded-lg hover:bg-blue-600 transition-colors"
          >
            Test Notifications
          </button>
        </div>

        {/* Permission Status */}
        {typeof window !== 'undefined' && 'Notification' in window && (
          <div className="mt-4 text-sm text-gray-500 text-center">
            Notification permission: {Notification.permission}
          </div>
        )}
      </div>
    </div>
  );
};

export default NotificationSettings;
