import React, { useState } from 'react';
import {
  getUserProfileImageUrl,
  getUserInitials,
  getUserAvatarColor,
  getFallbackAvatarUrl
} from '../../utils/imageUtils'; // Import UPLOADS_URL

// Function to generate a color based on user name
const stringToColor = (string) => {
  if (!string) return '#6e6e6e';
  
  let hash = 0;
  for (let i = 0; i < string.length; i++) {
    hash = string.charCodeAt(i) + ((hash << 5) - hash);
  }
  
  let color = '#';
  for (let i = 0; i < 3; i++) {
    const value = (hash >> (i * 8)) & 0xff;
    color += `00${value.toString(16)}`.slice(-2);
  }
  
  return color;
};

// Enhanced function to create initials with multiple fallbacks
const getInitials = (user) => {
  if (!user) return '?';

  // Try to get name from multiple sources
  const name = user.name || user.displayName || user.email?.split('@')[0];

  if (!name) {
    // If no name available, use ID or fallback
    return user._id ? user._id.slice(-2).toUpperCase() : '?';
  }

  const nameParts = name.split(' ').filter(part => part.length > 0);

  if (nameParts.length === 0) return '?';
  if (nameParts.length === 1) {
    return nameParts[0].charAt(0).toUpperCase();
  }

  return `${nameParts[0].charAt(0)}${nameParts[nameParts.length - 1].charAt(0)}`.toUpperCase();
};

// Status indicator component for Tailwind CSS
const StatusIndicator = ({ isOnline, size = 40 }) => {
  const indicatorSize = Math.max(8, size * 0.2);
  const position = size > 32 ? 'bottom-0 right-0' : 'bottom-0 right-0';

  return (
    <div className={`absolute ${position} transform translate-x-1 translate-y-1`}>
      <div
        className={`rounded-full border-2 border-white ${
          isOnline ? 'bg-green-500' : 'bg-gray-400'
        }`}
        style={{
          width: `${indicatorSize}px`,
          height: `${indicatorSize}px`
        }}
      >
        {isOnline && (
          <div
            className="absolute inset-0 rounded-full bg-green-500 animate-ping opacity-75"
            style={{
              width: `${indicatorSize}px`,
              height: `${indicatorSize}px`
            }}
          />
        )}
      </div>
    </div>
  );
};

const UserAvatar = ({
  user,
  size = 40,
  showStatus = false,
  isOnline = false,
  className = '',
  ...props
}) => {
  const [imageError, setImageError] = useState(false);
  const [imageLoading, setImageLoading] = useState(true);

  // Enhanced name resolution for avatar
  const getDisplayName = (userData) => {
    return userData?.name ||
           userData?.displayName ||
           userData?.email?.split('@')[0] ||
           `User ${userData?._id?.slice(-4) || 'Unknown'}`;
  };

  // If no user, show empty avatar
  if (!user) {
    return (
      <div
        className={`relative inline-block ${className}`}
        style={{ width: size, height: size }}
      >
        <div
          className="flex items-center justify-center rounded-full bg-gray-500 text-white font-medium"
          style={{
            width: size,
            height: size,
            fontSize: `${size * 0.4}px`
          }}
        >
          ?
        </div>
      </div>
    );
  }

  const displayName = getDisplayName(user);
  const initials = getUserInitials(user);
  const avatarColor = getUserAvatarColor(displayName);

  // Use centralized image URL processing
  const profileImageUrl = getUserProfileImageUrl(user);
  const fallbackUrl = getFallbackAvatarUrl(user);
  const shouldShowImage = profileImageUrl && !imageError;

  const handleImageLoad = () => {
    setImageLoading(false);
    setImageError(false);
  };

  const handleImageError = (error) => {
    if (import.meta.env.DEV) {
      console.error('❌ UserAvatar primary image failed, trying fallback:', {
        src: error.target.src,
        userName: displayName,
        fallbackUrl,
        error: error?.type || 'unknown'
      });
    }

    // Try fallback URL if available and not already tried
    if (fallbackUrl && error.target.src !== fallbackUrl) {
      error.target.src = fallbackUrl;
      return; // Don't set error state yet, let fallback try
    }

    // Both primary and fallback failed, show initials
    setImageLoading(false);
    setImageError(true);
  };

  // Create avatar element
  const avatar = (
    <div
      className={`relative inline-block ${className}`}
      style={{ width: size, height: size }}
    >
      {shouldShowImage ? (
        <>
          {imageLoading && (
            <div
              className="absolute inset-0 flex items-center justify-center rounded-full bg-gray-200 animate-pulse"
              style={{ width: size, height: size }}
            />
          )}
          <img
            src={profileImageUrl}
            alt={displayName}
            className={`rounded-full object-cover transition-opacity duration-200 ${
              imageLoading ? 'opacity-0' : 'opacity-100'
            }`}
            style={{ width: size, height: size }}
            onLoad={handleImageLoad}
            onError={handleImageError}
            {...props}
          />
        </>
      ) : (
        <div
          className="flex items-center justify-center rounded-full text-white font-medium"
          style={{
            width: size,
            height: size,
            backgroundColor: avatarColor,
            fontSize: `${size * 0.4}px`
          }}
        >
          {initials}
        </div>
      )}

      {showStatus && <StatusIndicator isOnline={isOnline} size={size} />}
    </div>
  );

  return avatar;
};

export default UserAvatar;

 