/**
 * Custom error class for application-specific errors
 */
class AppError extends Error {
  constructor(message, statusCode, errorCode) {
    super(message);
    this.statusCode = statusCode;
    this.errorCode = errorCode;
    this.status = `${statusCode}`.startsWith('4') ? 'fail' : 'error';
    this.isOperational = true;

    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * Error types for different scenarios
 */
const ErrorTypes = {
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  AUTHENTICATION_ERROR: 'AUTHENTICATION_ERROR',
  AUTHORIZATION_ERROR: 'AUTHORIZATION_ERROR',
  NOT_FOUND_ERROR: 'NOT_FOUND_ERROR',
  DATABASE_ERROR: 'DATABASE_ERROR',
  API_ERROR: 'API_ERROR',
  UNKNOWN_ERROR: 'UNKNOWN_ERROR'
};

/**
 * Error handler middleware
 * @param {Error} err - The error object
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const errorHandler = (err, req, res, next) => {
  err.statusCode = err.statusCode || 500;
  err.status = err.status || 'error';

  // Development error response
  if (process.env.NODE_ENV === 'development') {
    res.status(err.statusCode).json({
      status: err.status,
      error: err,
      message: err.message,
      stack: err.stack
    });
  } 
  // Production error response
  else {
    // Operational, trusted error: send message to client
    if (err.isOperational) {
      res.status(err.statusCode).json({
        status: err.status,
        message: err.message
      });
    } 
    // Programming or other unknown error: don't leak error details
    else {
      // Log error for debugging
      console.error('ERROR 💥', err);

      // Send generic message
      res.status(500).json({
        status: 'error',
        message: 'Something went wrong!'
      });
    }
  }
};

/**
 * Handle specific error types
 */
const handleSpecificErrors = (err) => {
  let error = { ...err };
  error.message = err.message;

  // Handle Mongoose validation errors
  if (err.name === 'ValidationError') {
    error = new AppError(
      Object.values(err.errors).map(val => val.message).join('. '),
      400,
      ErrorTypes.VALIDATION_ERROR
    );
  }

  // Handle Mongoose duplicate key errors
  if (err.code === 11000) {
    const value = err.errmsg.match(/(["'])(\\?.)*?\1/)[0];
    error = new AppError(
      `Duplicate field value: ${value}. Please use another value!`,
      400,
      ErrorTypes.VALIDATION_ERROR
    );
  }

  // Handle Mongoose cast errors
  if (err.name === 'CastError') {
    error = new AppError(
      `Invalid ${err.path}: ${err.value}.`,
      400,
      ErrorTypes.VALIDATION_ERROR
    );
  }

  // Handle JWT errors
  if (err.name === 'JsonWebTokenError') {
    error = new AppError(
      'Invalid token. Please log in again!',
      401,
      ErrorTypes.AUTHENTICATION_ERROR
    );
  }

  // Handle JWT expired errors
  if (err.name === 'TokenExpiredError') {
    error = new AppError(
      'Your token has expired! Please log in again.',
      401,
      ErrorTypes.AUTHENTICATION_ERROR
    );
  }

  return error;
};

/**
 * Async error handler wrapper
 * @param {Function} fn - Async function to wrap
 */
const catchAsync = fn => {
  return (req, res, next) => {
    fn(req, res, next).catch(next);
  };
};

module.exports = {
  AppError,
  ErrorTypes,
  errorHandler,
  handleSpecificErrors,
  catchAsync
}; 