import React from 'react';

/**
 * Call-to-Action section component for the landing page - "Get The Latest Updates"
 * @returns {JSX.Element} - Rendered component
 */
const CTASection = () => {
  return (
    <section className="bg-gray-900 py-16 sm:py-20 px-6 sm:px-8 md:px-16">
      <div 
        className="container mx-auto flex flex-col md:flex-row items-center justify-between gap-8 md:gap-12"
        data-aos="fade-up" 
        data-aos-duration="800"
      >
        <h2 className="text-3xl sm:text-4xl font-bold text-white text-center md:text-left leading-tight">
          Get The Latest Updates
        </h2>
        
        <form className="flex flex-col sm:flex-row items-center gap-4 w-full md:w-auto">
          <input 
            type="email" 
            placeholder="Enter your email" 
            className="w-full sm:w-auto flex-grow px-6 py-3 rounded-lg bg-gray-800 text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-cyan-500 border border-gray-700"
            aria-label="Email for updates"
          />
          <button 
            type="submit" 
            className="w-full sm:w-auto px-8 py-3 bg-gradient-to-r from-teal-400 to-cyan-500 text-gray-900 font-semibold rounded-lg hover:from-teal-500 hover:to-cyan-600 transition-all duration-300 shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-900 focus:ring-cyan-500"
          >
            Subscribe
          </button>
        </form>
      </div>
    </section>
  );
};

export default CTASection;
