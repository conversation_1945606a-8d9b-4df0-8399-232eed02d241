{"name": "task-manager", "version": "1.0.0", "description": "", "main": "server.js", "scripts": {"start": "node server.js", "test": "node server.js", "dev": "nodemon server.js", "audit": "npm audit"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"axios": "^1.10.0", "axios-cookiejar-support": "^6.0.2", "bcrypt": "^6.0.0", "bcryptjs": "^3.0.2", "colors": "^1.4.0", "compression": "^1.8.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "csurf": "^1.11.0", "dotenv": "^16.5.0", "exceljs": "^4.4.0", "express": "^4.21.2", "express-async-handler": "^1.2.0", "express-mongo-sanitize": "^2.2.0", "express-rate-limit": "^7.5.1", "helmet": "^8.1.0", "isomorphic-dompurify": "^2.25.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.15.0", "morgan": "^1.10.0", "multer": "^2.0.1", "node-fetch": "^3.3.2", "nodemon": "^3.1.10", "sharp": "^0.34.2", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "tough-cookie": "^5.1.2", "validator": "^13.15.15"}, "devDependencies": {"jest": "^30.0.3", "supertest": "^7.1.1"}}