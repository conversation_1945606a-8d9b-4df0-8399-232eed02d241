import React, { useEffect, useState, useRef } from 'react';
import AOS from 'aos';

// Custom hooks
import useScrollSpy from '../hooks/useScrollSpy';

// Layout components
import Navbar from '../components/layout/Navbar';
import Footer from '../components/layout/Footer';

// Page-specific components
import Hero from './LandingPage/components/Hero';
import Partners from './LandingPage/components/Partners';
import FeatureSection1 from './LandingPage/components/FeatureSection1';
import FeatureSection2 from './LandingPage/components/FeatureSection2';
import FeatureSection3 from './LandingPage/components/FeatureSection3';
import Stats from './LandingPage/components/Stats';
import Pricing from './LandingPage/components/Pricing';
import FAQ from './LandingPage/components/FAQ';


// Data
import { faqDataList } from './LandingPage/data/faqData';
import { navLinksData } from './LandingPage/data/navData';

/**
 * Main landing page component that orchestrates all sections
 * @returns {JSX.Element} - Rendered component
 */
const LandingPage = () => {
  // State for FAQ toggle
  const [openFAQ, setOpenFAQ] = useState(null);
  
  // Refs for sections (for scroll spy)
  const navRef = useRef(null);
  const sectionRefs = {
    hero: useRef(null),
    'features-main': useRef(null),
    pricing: useRef(null),
    faq: useRef(null),
    about: useRef(null),
  };
  
  // Get active section from scroll position
  const activeSection = useScrollSpy({ 
    sectionRefs, 
    offset: navRef.current ? navRef.current.offsetHeight : 70 
  });

  // Initialize AOS animations
  useEffect(() => {
    AOS.init({
      duration: 800,
      once: true,
      offset: 100,
      easing: 'ease-out-cubic',
    });
    
    // Handle navbar styling on scroll
    const mainNav = navRef.current;
    
    const handleScroll = () => {
      if (mainNav) {
        if (window.pageYOffset > mainNav.offsetHeight / 2) {
          mainNav.classList.remove('bg-transparent', 'py-5');
          mainNav.classList.add('bg-white/90', 'backdrop-blur-lg', 'shadow-md', 'py-4');
        } else {
          mainNav.classList.add('bg-transparent', 'py-5');
          mainNav.classList.remove('bg-white/90', 'backdrop-blur-lg', 'shadow-md', 'py-4');
        }
      }
    };
    
    // Add scroll event listener
    window.addEventListener('scroll', handleScroll);
    
    // Set smooth scrolling
    document.documentElement.classList.add('scroll-smooth');
    
    // Initial call
    handleScroll();
    
    // Clean up
    return () => {
      window.removeEventListener('scroll', handleScroll);
      document.documentElement.classList.remove('scroll-smooth');
    };
  }, []);

  // Navigation click handler for smooth scrolling
  const handleNavLinkClick = (e, targetId) => {
    e.preventDefault();
    const targetElement = document.querySelector(targetId);
    
    if (targetElement && navRef.current) {
      const offsetTop = targetElement.offsetTop - navRef.current.offsetHeight;
      window.scrollTo({ top: offsetTop, behavior: 'smooth' });
    } else if (targetElement) {
      targetElement.scrollIntoView({ behavior: 'smooth' });
    }
  };

  // FAQ toggle handler
  const handleFAQToggle = (id) => {
    setOpenFAQ(openFAQ === id ? null : id);
  };

  return (
    <>
      <Navbar
        navRef={navRef}
        navLinks={navLinksData}
        activeSection={activeSection}
        handleNavLinkClick={handleNavLinkClick}
      />
      
      <main>
        <div ref={sectionRefs.hero} id="hero">
          <Hero />
        </div>
        
        <Partners />
        
        <div ref={sectionRefs['features-main']} id="features-main">
          <FeatureSection1 />
        </div>
        
        <FeatureSection2 />
        <FeatureSection3 />
        <Stats />
        
        <div ref={sectionRefs.pricing} id="pricing">
          <Pricing />
        </div>
        
        <div ref={sectionRefs.faq} id="faq">
          <FAQ 
            faqData={faqDataList} 
            openFAQ={openFAQ} 
            handleToggle={handleFAQToggle} 
          />
        </div>
        
      </main>
      
      <div ref={sectionRefs.about} id="about">
        <Footer />
      </div>
    </>
  );
};

export default LandingPage;
