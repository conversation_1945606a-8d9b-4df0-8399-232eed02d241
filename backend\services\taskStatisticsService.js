/**
 * Task Statistics Service
 * Centralized service for all task counting and statistics operations
 * Eliminates code duplication across controllers
 */

const Task = require('../models/Task');
const mongoose = require('mongoose');

/**
 * Get task counts by status for a user or all tasks (admin)
 * @param {Object} user - User object with role and _id
 * @returns {Object} Task counts by status
 */
const getTaskStatusCounts = async (user) => {
  const isAdmin = user.role === 'admin';
  const baseFilter = isAdmin ? {} : { team: user._id };

  // Use efficient aggregation query
  const statusCounts = await Task.aggregate([
    { $match: baseFilter },
    { $group: { _id: '$status', count: { $sum: 1 } } }
  ]);

  // Get the total count
  const allTasks = statusCounts.reduce((sum, item) => sum + item.count, 0);

  // Build the response object with counts for each status
  return {
    allTasks,
    totalTasks: allTasks,
    pendingTasks: statusCounts.find(item => item._id === 'todo')?.count || 0,
    inProgressTasks: statusCounts.find(item => item._id === 'in-progress')?.count || 0,
    completedTasks: statusCounts.find(item => item._id === 'completed')?.count || 0
  };
};

/**
 * Get comprehensive task statistics including overdue tasks
 * @param {String} userId - Optional user ID for user-specific stats
 * @returns {Object} Comprehensive task statistics
 */
const getTaskStatistics = async (userId = null) => {
  let matchStage = {};
  if (userId) {
    try {
      const objectId = typeof userId === 'string' ? new mongoose.Types.ObjectId(userId) : userId;
      matchStage = { team: objectId };
    } catch (error) {
      matchStage = { team: userId };
    }
  }

  const stats = await Task.aggregate([
    { $match: matchStage },
    {
      $addFields: {
        progress: {
          $cond: {
            if: { $eq: [{ $size: { $ifNull: ["$todoCheckList", []] } }, 0] },
            then: 0,
            else: {
              $round: [
                {
                  $multiply: [
                    {
                      $divide: [
                        { $size: { $filter: { input: "$todoCheckList", as: "item", cond: { $eq: ["$$item.completed", true] } } } },
                        { $size: "$todoCheckList" }
                      ]
                    },
                    100
                  ]
                }
              ]
            }
          }
        }
      }
    },
    {
      $addFields: {
        calculatedStatus: {
          $switch: {
            branches: [
              { case: { $eq: ["$progress", 100] }, then: "completed" },
              { case: { $gt: ["$progress", 0] }, then: "in-progress" }
            ],
            default: "todo"
          }
        }
      }
    },
    {
      $facet: {
        totalTasks: [{ $count: "count" }],
        byStatus: [
          { $group: { _id: "$calculatedStatus", count: { $sum: 1 } } }
        ],
        overdueTasks: [
          {
            $match: {
              calculatedStatus: { $ne: "completed" },
              deadline: { $lt: new Date() }
            }
          },
          { $count: "count" }
        ]
      }
    }
  ]);

  const byStatus = stats[0].byStatus || [];
  return {
    totalTasks: stats[0].totalTasks[0]?.count || 0,
    pendingTasks: byStatus.find(item => item._id === "todo")?.count || 0,
    inProgressTasks: byStatus.find(item => item._id === "in-progress")?.count || 0,
    completedTasks: byStatus.find(item => item._id === "completed")?.count || 0,
    overdueTasks: stats[0].overdueTasks[0]?.count || 0
  };
};

/**
 * Get task distribution by a specific field (status, priority, etc.)
 * @param {String} field - Field to group by (status, priority, etc.)
 * @param {String} userId - Optional user ID for user-specific distribution
 * @returns {Array} Distribution data
 */
const getDistributionByField = async (field, userId = null) => {
  let matchStage = {};
  if (userId) {
    try {
      const objectId = typeof userId === 'string' ? new mongoose.Types.ObjectId(userId) : userId;
      matchStage = { team: objectId };
    } catch (error) {
      matchStage = { team: userId };
    }
  }

  return await Task.aggregate([
    { $match: matchStage },
    { $group: { _id: `$${field}`, count: { $sum: 1 } } }
  ]);
};

/**
 * Get task counts for a specific user (used in user management)
 * @param {String} userId - User ID
 * @returns {Object} Task counts for the user
 */
const getUserTaskCounts = async (userId) => {
  let teamValue;
  try {
    teamValue = typeof userId === 'string' ? new mongoose.Types.ObjectId(userId) : userId;
  } catch (error) {
    teamValue = userId;
  }

  const statusCounts = await Task.aggregate([
    { $match: { team: teamValue } },
    { $group: { _id: '$status', count: { $sum: 1 } } }
  ]);

  return {
    pendingTasks: statusCounts.find(item => item._id === 'todo')?.count || 0,
    inProgressTasks: statusCounts.find(item => item._id === 'in-progress')?.count || 0,
    completedTasks: statusCounts.find(item => item._id === 'completed')?.count || 0,
    totalTasks: statusCounts.reduce((sum, item) => sum + item.count, 0)
  };
};

/**
 * Get task counts for multiple users efficiently
 * @param {Array} userIds - Array of user IDs
 * @returns {Object} Map of userId to task counts
 */
const getMultipleUserTaskCounts = async (userIds) => {
  const taskCounts = await Task.aggregate([
    { $match: { team: { $in: userIds } } },
    { $unwind: '$team' },
    { $match: { team: { $in: userIds } } },
    {
      $group: {
        _id: { userId: '$team', status: '$status' },
        count: { $sum: 1 }
      }
    }
  ]);
  
  // Initialize result object
  const result = {};
  userIds.forEach(userId => {
    result[userId.toString()] = {
      pendingTasks: 0,
      inProgressTasks: 0,
      completedTasks: 0,
      totalTasks: 0
    };
  });
  
  // Populate counts
  taskCounts.forEach(item => {
    const userId = item._id.userId.toString();
    const status = item._id.status;
    const count = item.count;
    
    if (result[userId]) {
      result[userId].totalTasks += count;
      
      if (status === 'todo') result[userId].pendingTasks = count;
      else if (status === 'in-progress') result[userId].inProgressTasks = count;
      else if (status === 'completed') result[userId].completedTasks = count;
    }
  });
  
  return result;
};

/**
 * Calculate task progress based on checklist completion
 * @param {Object} task - Task object with todoCheckList
 * @returns {Number} Progress percentage (0-100)
 */
const calculateTaskProgress = (task) => {
  if (!task.todoCheckList || task.todoCheckList.length === 0) {
    return 0;
  }

  
  const totalItems = task.todoCheckList.length;
  const completedCount = task.todoCheckList.filter(item => item.completed).length;
  
  return Math.round((completedCount / totalItems) * 100);
};

/**
 * Update task status based on progress
 * @param {Object} task - Task object to update
 * @returns {String} Updated status
 */
const updateTaskStatusByProgress = (task) => {
  const progress = calculateTaskProgress(task);

  if (progress === 100) {
    return "completed";
  } else if (progress > 0) {
    return "in-progress";
  } else {
    return "todo";
  }
};

module.exports = {
  getTaskStatusCounts,
  getTaskStatistics,
  getDistributionByField,
  getUserTaskCounts,
  getMultipleUserTaskCounts,
  calculateTaskProgress,
  updateTaskStatusByProgress
};
