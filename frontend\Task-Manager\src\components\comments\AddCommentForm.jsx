import React, { useState } from 'react';
import axiosInstance from '../../utils/axiosInstance';
import { API_PATHS } from '../../utils/apiPaths';
import { useUserAuth } from '../../hooks/useUserAuth';
import toast from 'react-hot-toast';
import { LuS<PERSON>, <PERSON><PERSON>oader } from 'react-icons/lu';
import AddAttachmentsInput from '../Inputs/AddAttachmentsInput';
import UserAvatar from '../common/UserAvatar'; // Import UserAvatar

const AddCommentForm = ({ taskId, onCommentAdded }) => {
  const [content, setContent] = useState('');
  const [attachments, setAttachments] = useState([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { user } = useUserAuth();

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!content.trim() && attachments.length === 0) {
      toast.warning('Comment or attachment cannot be empty.');
      return;
    }

    setIsSubmitting(true);
    const formData = new FormData();
    formData.append('content', content);

    attachments.forEach((attachment) => {
      if (attachment.file) {
        formData.append('attachments', attachment.file);
      } else if (attachment.isLink) {
        formData.append('links', JSON.stringify({ name: attachment.name, url: attachment.url }));
      }
    });

    try {
      const response = await axiosInstance.post(API_PATHS.COMMENTS.CREATE(taskId), formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      onCommentAdded(response.data);
      setContent('');
      setAttachments([]);
      toast.success('Comment added successfully.');
    } catch (error) {
      toast.error('Failed to add comment.');
      console.error('Failed to submit comment:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="flex items-start space-x-3">
      <UserAvatar user={user} size={40} className="flex-shrink-0" /> {/* Use UserAvatar component */}
      <div className="flex-1">
        <textarea
          value={content}
          onChange={(e) => setContent(e.target.value)}
          placeholder="Add a comment..."
          className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition"
          rows="3"
          disabled={isSubmitting}
        />
        <AddAttachmentsInput attachments={attachments} setAttachments={setAttachments} />
        <div className="flex justify-end mt-2">
          <button
            type="submit"
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:bg-blue-300 flex items-center transition"
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <LuLoader className="animate-spin mr-2" />
            ) : (
              <LuSend className="mr-2" />
            )}
            {isSubmitting ? 'Posting...' : 'Post Comment'}
          </button>
        </div>
      </div>
    </form>
  );
};

export default AddCommentForm;
