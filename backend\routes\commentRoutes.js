const express = require('express');
const router = express.Router({ mergeParams: true });
const { createComment, getCommentsForTask, pinComment } = require('../controllers/commentController');
const { enhancedProtect } = require('../middleware/enhancedAuthMiddleware');

// Route to get all comments for a task and create a new comment
router.route('/')
  .get(enhancedProtect, getCommentsForTask)
  .post(enhancedProtect, createComment);

// Route to pin a comment - using a separate route for clarity
router.route('/:commentId/pin')
  .put(enhancedProtect, pinComment);

module.exports = router;
