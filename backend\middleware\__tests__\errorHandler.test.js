const errorHandler = require('../errorHandler');

// Helper to mock req, res, next
const mockReq = (overrides = {}) => ({ ...overrides });
const mockRes = () => {
  const res = {};
  res.status = jest.fn().mockReturnValue(res);
  res.json = jest.fn().mockReturnValue(res);
  return res;
};
const mockNext = jest.fn();

describe('errorHandler middleware', () => {
  let oldEnv;
  beforeAll(() => { oldEnv = process.env.NODE_ENV; });
  afterEach(() => { jest.clearAllMocks(); });
  afterAll(() => { process.env.NODE_ENV = oldEnv; });

  it('should handle CastError as 404', () => {
    const err = { name: 'CastError', message: 'Bad ObjectId' };
    const req = mockReq();
    const res = mockRes();
    errorHandler(err, req, res, mockNext);
    expect(res.status).toHaveBeenCalledWith(404);
    expect(res.json).toHaveBeenCalledWith({ success: false, error: 'Resource not found' });
  });

  it('should handle duplicate key error as 400', () => {
    const err = { code: 11000, message: 'Duplicate key' };
    const req = mockReq();
    const res = mockRes();
    errorHandler(err, req, res, mockNext);
    expect(res.status).toHaveBeenCalledWith(400);
    expect(res.json).toHaveBeenCalledWith({ success: false, error: 'Duplicate field value entered' });
  });

  it('should handle validation error as 400', () => {
    const err = { name: 'ValidationError', errors: { a: { message: 'A required' }, b: { message: 'B required' } } };
    const req = mockReq();
    const res = mockRes();
    errorHandler(err, req, res, mockNext);
    expect(res.status).toHaveBeenCalledWith(400);
    expect(res.json).toHaveBeenCalledWith({ success: false, error: ['A required', 'B required'] });
  });

  it('should mask error message in production', () => {
    process.env.NODE_ENV = 'production';
    const err = { message: 'Sensitive error' };
    const req = mockReq();
    const res = mockRes();
    errorHandler(err, req, res, mockNext);
    expect(res.status).toHaveBeenCalledWith(500);
    expect(res.json).toHaveBeenCalledWith({ success: false, error: 'An unexpected error occurred' });
  });

  it('should show error message in development', () => {
    process.env.NODE_ENV = 'development';
    const err = { message: 'Dev error' };
    const req = mockReq();
    const res = mockRes();
    errorHandler(err, req, res, mockNext);
    expect(res.status).toHaveBeenCalledWith(500);
    expect(res.json).toHaveBeenCalledWith({ success: false, error: 'Dev error' });
  });
});
