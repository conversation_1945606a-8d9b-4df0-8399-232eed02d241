import React from 'react';
import PropTypes from 'prop-types';

/**
 * Skeleton loader component for different UI elements
 * @param {Object} props - Component props
 * @param {string} props.type - Type of skeleton to render (dashboard, card, table, etc.)
 * @param {number} props.count - Number of skeleton items to render
 * @returns {JSX.Element} Rendered component
 */
const SkeletonLoader = ({ type = 'card', count = 1 }) => {
  const renderSkeleton = () => {
    switch (type) {
      case 'dashboard':
        return <DashboardSkeleton />;
      case 'table':
        return <TableSkeleton rows={count} />;
      case 'card':
        return <CardSkeleton count={count} />;
      case 'list':
        return <ListSkeleton count={count} />;
      default:
        return <CardSkeleton count={count} />;
    }
  };

  return renderSkeleton();
};

/**
 * Skeleton for dashboard layout
 */
const DashboardSkeleton = () => {
  return (
    <div className="space-y-6 animate-pulse">
      {/* Welcome section skeleton */}
      <div className="h-48 bg-gray-200 rounded-2xl w-full"></div>
      
      {/* Stats cards skeleton */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        {[...Array(4)].map((_, i) => (
          <div key={i} className="h-24 bg-gray-200 rounded-xl"></div>
        ))}
      </div>
      
      {/* Recent tasks skeleton */}
      <div className="bg-gray-200 rounded-xl p-6 space-y-4">
        <div className="flex justify-between">
          <div className="h-6 bg-gray-300 rounded w-1/4"></div>
          <div className="h-6 bg-gray-300 rounded w-1/6"></div>
        </div>
        
        <div className="space-y-3">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="h-16 bg-gray-300 rounded"></div>
          ))}
        </div>
      </div>
    </div>
  );
};

/**
 * Skeleton for table layout
 */
const TableSkeleton = ({ rows = 5 }) => {
  return (
    <div className="overflow-hidden rounded-lg border border-gray-200 animate-pulse">
      {/* Table header */}
      <div className="bg-gray-100 p-4">
        <div className="h-8 bg-gray-300 rounded w-1/4"></div>
      </div>
      
      {/* Table rows */}
      <div className="divide-y divide-gray-200">
        {[...Array(rows)].map((_, i) => (
          <div key={i} className="p-4">
            <div className="flex justify-between">
              <div className="h-6 bg-gray-200 rounded w-2/3"></div>
              <div className="h-6 bg-gray-200 rounded w-1/4"></div>
            </div>
            <div className="mt-2 h-4 bg-gray-200 rounded w-1/3"></div>
          </div>
        ))}
      </div>
    </div>
  );
};

/**
 * Skeleton for card layout
 */
const CardSkeleton = ({ count = 1 }) => {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
      {[...Array(count)].map((_, i) => (
        <div key={i} className="bg-white rounded-lg shadow-md p-6 animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-3/4 mb-4"></div>
          <div className="space-y-2">
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded w-5/6"></div>
            <div className="h-4 bg-gray-200 rounded w-4/6"></div>
          </div>
          <div className="mt-6 h-8 bg-gray-200 rounded w-1/3"></div>
        </div>
      ))}
    </div>
  );
};

/**
 * Skeleton for list layout
 */
const ListSkeleton = ({ count = 5 }) => {
  return (
    <div className="space-y-3 animate-pulse">
      {[...Array(count)].map((_, i) => (
        <div key={i} className="bg-white rounded-lg p-4 border border-gray-100">
          <div className="h-5 bg-gray-200 rounded w-1/2 mb-2"></div>
          <div className="h-4 bg-gray-200 rounded w-1/3"></div>
        </div>
      ))}
    </div>
  );
};

SkeletonLoader.propTypes = {
  type: PropTypes.oneOf(['dashboard', 'table', 'card', 'list']),
  count: PropTypes.number
};

export default SkeletonLoader; 