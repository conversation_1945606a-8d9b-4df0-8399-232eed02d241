import axiosInstance from './axiosInstance';
import { API_PATHS, BASE_URL, UPLOADS_URL } from './apiPaths';

/**
 * Debug utilities for troubleshooting API and image loading issues
 */

/**
 * Test backend connectivity
 */
export const testBackendConnectivity = async () => {
  // This function is disabled to remove console logs.
};

/**
 * Test image URL accessibility
 */
export const testImageUrls = async (imageUrls) => {
  // This function is disabled to remove console logs.
  return [];
};

/**
 * Debug user data structure
 */
export const debugUserData = (users) => {
  // This function is disabled to remove console logs.
};

/**
 * Debug task data structure
 */
export const debugTaskData = (tasks) => {
  // This function is disabled to remove console logs.
};

/**
 * Comprehensive debug session
 */
export const runFullDebugSession = async () => {
  // This function is disabled to remove console logs.
};

/**
 * Quick image URL validation
 */
export const validateImageUrl = (url) => {
  return new Promise((resolve) => {
    if (!url) {
      resolve({ valid: false, reason: 'No URL provided' });
      return;
    }

    const img = new Image();
    img.onload = () => resolve({ valid: true, url });
    img.onerror = () => resolve({ valid: false, reason: 'Failed to load', url });
    img.src = url;

    // Timeout after 5 seconds
    setTimeout(() => resolve({ valid: false, reason: 'Timeout', url }), 5000);
  });
};

