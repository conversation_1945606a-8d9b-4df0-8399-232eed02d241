import axios from "axios";
import { BASE_URL } from "./apiPaths";
import { fetchCsrfToken, attachCsrfToken } from "./csrf";

// Request cache to prevent duplicate requests
const requestCache = new Map();
const pendingRequests = new Map(); // Track pending requests
const CACHE_DURATION = 5000; // 5 seconds

const axiosInstance = axios.create({
  baseURL: BASE_URL,
  timeout: 15000, // Increased timeout
  headers: {
    "Content-Type": "application/json",
    Accept: "application/json",
  },
  // Reject on 4xx and 5xx so that unauthorized or bad requests trigger catch blocks
  validateStatus: (status) => status >= 200 && status < 400,
  withCredentials: true, // Enable credentials for CORS
});

// Utility: Log only in development
const devLog = (...args) => {
  if (import.meta.env.DEV) {
    // eslint-disable-next-line no-console
    console.log('[Axios]', ...args);
  }
};

// Request Interceptor with caching
axiosInstance.interceptors.request.use(
  async (config) => {
    // Attach CSRF token for non-GET/OPTIONS requests
    if (config.method && !['get','options'].includes(config.method.toLowerCase())) {
      // If no CSRF token, fetch it first
      if (!window.__csrfTokenInit) {
        await fetchCsrfToken();
        window.__csrfTokenInit = true;
      }
      attachCsrfToken(config);
    }
    // The browser now automatically sends the httpOnly accessToken cookie with `withCredentials: true`.
    // No need to manually set the Authorization header, but attach Authorization header for SPA using stored token.
    const token = localStorage.getItem('token') || localStorage.getItem('accessToken');
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }

    // Create cache key for GET requests
    if (config.method === 'get') {
      const cacheKey = `${config.method}:${config.url}:${JSON.stringify(config.params || {})}`;
      const cached = requestCache.get(cacheKey);

      if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
        devLog('Serving from cache:', cacheKey);
        config.adapter = () => Promise.resolve(cached.response);
      } else {
        // Check if there's already a pending request for this key
        const pendingRequest = pendingRequests.get(cacheKey);
        if (pendingRequest) {
          devLog('Deduplicating GET request:', cacheKey);
          // Return the pending promise
          config.adapter = () => pendingRequest;
        } else {
          // Store request for caching and deduplication
          config.cacheKey = cacheKey;
        }
      }
    }

    // Simple pending request tracking without recursive axios calls
    if (config.method === 'get' && config.cacheKey) {
      const pendingRequest = pendingRequests.get(config.cacheKey);
      if (pendingRequest) {
        devLog('Pending GET request found, returning existing promise:', config.cacheKey);
        config.adapter = () => pendingRequest;
      }
    }

    return config;
  },
  (error) => {
    devLog('❌ Request interceptor error:', error);
    return Promise.reject(error);
  }
);

// Response Interceptor with caching and error handling
axiosInstance.interceptors.response.use(
  (response) => {
    // Cache successful GET responses
    if (response.config.method === 'get' && response.config.cacheKey && response.status === 200) {
      const cacheKey = response.config.cacheKey;
      devLog('Caching GET response:', cacheKey);
      requestCache.set(cacheKey, {
        response: response,
        timestamp: Date.now(),
      });
      // Clean up old cache entries
      setTimeout(() => {
        requestCache.delete(cacheKey);
      }, CACHE_DURATION);
    }

    return response;
  },
  async (error) => {
    // Make the error handler async
    // Clean up pending requests on error
    if (error.config?.cacheKey) {
      pendingRequests.delete(error.config.cacheKey);
    }

    // If CSRF error, try to refresh CSRF token and retry once
    if (
      error.response &&
      error.response.status === 403 &&
      error.response.data?.message?.toLowerCase().includes('csrf')
    ) {
      if (import.meta.env.DEV) {
        console.log('🔄 CSRF token expired, fetching a new one and retrying...');
      }
      await fetchCsrfToken();
      const config = error.config;
      attachCsrfToken(config);
      return axiosInstance(config); // retry once
    }

    devLog('❌ Response interceptor error:', error);

    // Handle common errors globally
    if (error.response) {
      devLog(`Server responded with ${error.response.status}:`, error.response.data);
      // Handle specific status codes
      switch (error.response.status) {
        case 401:
          devLog('🚫 Unauthorized access (401). Redirecting to login.');
          window.location.href = '/login';
          break;
        case 403:
          devLog('⛔ Forbidden (403). Check permissions.');
          break;
        case 404:
          devLog('🤷 Not Found (404).');
          break;
        case 500:
          devLog('🔥 Internal Server Error (500).');
          break;
        default:
          devLog(`Unhandled status code: ${error.response.status}`);
      }
    } else if (error.request) {
      devLog('❌ No response received:', error.request);
      // Network error or timeout
      error.code = 'NETWORK_ERROR';
      error.message = 'Network error: Please check your internet connection';
    } else {
      devLog('❌ Error setting up request:', error.message);
    }

    return Promise.reject(error);
  }
);

export default axiosInstance;
