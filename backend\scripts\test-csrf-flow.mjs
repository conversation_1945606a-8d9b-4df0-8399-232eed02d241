// Automated CSRF protection test for Task Manager backend
import axios from 'axios';
import { CookieJar } from 'tough-cookie';
import dotenv from 'dotenv';

dotenv.config(); // Load environment variables from .env file
import { wrapper } from 'axios-cookiejar-support';
import mongoose from 'mongoose';
import User from '../models/User.js';

const BASE_URL = 'http://localhost:3000/api';
const TEST_USER = {
  email: `csrfuser${Date.now()}@example.com`,
  password: 'TestUser@123',
  name: `CSRF Test User ${Date.now()}`
};

async function main() {
  const jar = new CookieJar();
  const client = wrapper(axios.create({ baseURL: BASE_URL, jar, withCredentials: true }));



  // 0. Fetch CSRF token for registration
  let res = await client.get('/auth/csrf-token');
  let csrfToken = res.data.csrfToken;
  if (!csrfToken) throw new Error('Failed to fetch CSRF token for registration');
  let cookies = await jar.getCookies(BASE_URL);
  let xsrfCookie = cookies.find(c => c.key === 'XSRF-TOKEN');
  if (!xsrfCookie) throw new Error('XSRF-TOKEN cookie missing from jar after CSRF fetch!');

  // 1. Register/login
  try {
    res = await client.post('/auth/register', TEST_USER, { headers: { 'x-csrf-token': csrfToken } });
  } catch (e) {
    console.error('Register failed:', e.message);
    if (e.response) console.error('Register error response:', e.response.data);
    res = await client.post('/auth/login', { email: TEST_USER.email, password: TEST_USER.password });
  }

  // 1b. Promote user to admin in DB
  const MONGO_URI = process.env.MONGO_URI;
  if (!MONGO_URI) {
    console.error('FATAL: MONGO_URI not found in environment. Make sure it is set in your .env file.');
    process.exit(1);
  }
  await mongoose.connect(MONGO_URI, { useNewUrlParser: true, useUnifiedTopology: true });
  try {
    let user = null;
    const maxRetries = 5;
    const retryDelay = 300; // ms

    for (let i = 0; i < maxRetries; i++) {
      user = await User.findOne({ email: TEST_USER.email });
      if (user) {
        break;
      }
      if (i < maxRetries - 1) {
        await new Promise(resolve => setTimeout(resolve, retryDelay));
      }
    }

    if (!user) {
      throw new Error('User not found in DB after registration, even after retries.');
    }

    user.role = 'admin';
    await user.save();

  } catch (dbError) {
    console.error('FATAL: DB operation failed.', dbError);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
  }

  // 1c. Log in again to refresh session as admin
  const loginResponse = await client.post('/auth/login', { email: TEST_USER.email, password: TEST_USER.password }, {
    headers: { 'x-csrf-token': csrfToken }
  });
  const accessToken = loginResponse.data.accessToken;
  if (!accessToken) {
    console.error('FATAL: Did not receive access token after admin login.');
    process.exit(1);
  }

  // 2. Fetch a NEW CSRF token for the new session (after admin login)
  res = await client.get('/auth/csrf-token');
  csrfToken = res.data.csrfToken;
  if (!csrfToken) throw new Error('Failed to fetch CSRF token after admin login');
  cookies = await jar.getCookies(BASE_URL);
  xsrfCookie = cookies.find(c => c.key === 'XSRF-TOKEN');
  if (!xsrfCookie) throw new Error('XSRF-TOKEN cookie missing from jar after CSRF fetch!');

  // Now use csrfToken and jar for all protected requests
  try {
    // Prepare headers with both Access and CSRF tokens
    const headers = {
      'Authorization': `Bearer ${accessToken}`,
      'x-csrf-token': csrfToken
    };
    // 3. Create a task with CSRF token (should succeed)
    try {
      res = await client.post('/tasks', {
        title: 'CSRF Test Task',
        description: 'This is a CSRF test',
        assignedTo: []
      }, {
        headers
      });
    } catch (e) {
      if (e.response) {
        console.error('POST /tasks failed:', e.response.status, e.response.data);
      } else {
        console.error('POST /tasks failed:', e.message);
      }
      throw e;
    }
    if (res.status !== 201 || !res.data.task) throw new Error('Failed to create task with CSRF token');
    const taskId = res.data.task._id;
    // 4. Try to create a task without CSRF token (should fail)
    let failed = false;
    try {
      await client.post('/tasks', {
        title: 'CSRF Fail Task',
        description: 'Should fail',
        assignedTo: []
      });
    } catch (e) {
      failed = true;
      if (e.response && e.response.status === 403 && e.response.data.message.toLowerCase().includes('csrf')) {
        // This is the expected outcome
      } else {
        throw e;
      }
    }
    if (!failed) throw new Error('Request without CSRF token did NOT fail');
    // 5. Cleanup: delete the created task
    if (taskId) {
      await client.delete(`/tasks/${taskId}`, { headers });
    }
    console.log('All CSRF tests passed!');
  } catch (e) {
    console.error('CSRF token fetch or POST failed:', e.message);
    if (e.response) console.error('Error response:', e.response.data);
    throw e;
  }
}

main().catch(e => {
  console.error('CSRF Test failed:', e.message);
  if (e.stack) console.error(e.stack);
  if (e.response) {
    console.error('Axios error response:', {
      status: e.response.status,
      headers: e.response.headers,
      data: e.response.data
    });
  }
  process.exit(1);
});
