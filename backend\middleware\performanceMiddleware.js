/**
 * Performance Monitoring and Optimization Middleware
 * Tracks response times, memory usage, and implements caching
 */

const compression = require('compression');

/**
 * Response time tracking middleware
 */
const responseTimeMiddleware = (req, res, next) => {
  const start = Date.now();

  // Set the header before the response is sent
  const originalSend = res.send;
  const originalJson = res.json;
  const originalEnd = res.end;

  const addResponseTimeHeader = () => {
    const duration = Date.now() - start;

    // Only set header if headers haven't been sent yet
    if (!res.headersSent) {
      res.set('X-Response-Time', `${duration}ms`);
    }

    // Log slow requests (over 1 second)
    if (duration > 1000) {
      console.warn(`Slow request: ${req.method} ${req.path} took ${duration}ms`);
    }
  };

  // Override response methods to add timing header
  res.send = function(data) {
    addResponseTimeHeader();
    return originalSend.call(this, data);
  };

  res.json = function(data) {
    addResponseTimeHeader();
    return originalJson.call(this, data);
  };

  res.end = function(data) {
    addResponseTimeHeader();
    return originalEnd.call(this, data);
  };

  next();
};

/**
 * Memory usage monitoring middleware
 */
const memoryMonitoringMiddleware = (req, res, next) => {
  // Only monitor memory in development and for specific heavy endpoints
  if (process.env.NODE_ENV === 'development' && process.env.DEBUG_MEMORY === 'true') {
    const memUsage = process.memoryUsage();

    // Log memory usage for heavy endpoints only
    if (req.path.includes('/dashboard') || req.path.includes('/reports')) {
      console.log(`Memory usage for ${req.path}:`, {
        rss: Math.round(memUsage.rss / 1024 / 1024) + 'MB',
        heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024) + 'MB',
        heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024) + 'MB'
      });
    }
  }

  next();
};

/**
 * Simple in-memory cache for API responses
 */
class ApiCache {
  constructor() {
    this.cache = new Map();
    this.timestamps = new Map();
    this.defaultTTL = 5 * 60 * 1000; // 5 minutes
    this.maxSize = 100;
    
    // Clean up expired items every minute
    setInterval(() => this.cleanup(), 60 * 1000);
  }

  generateKey(req) {
    const { method, path, query, user } = req;
    const userId = user?._id?.toString() || 'anonymous';
    const queryString = JSON.stringify(query);
    return `${method}:${path}:${userId}:${queryString}`;
  }

  set(key, value, ttl = this.defaultTTL) {
    if (this.cache.size >= this.maxSize) {
      const oldestKey = this.cache.keys().next().value;
      this.delete(oldestKey);
    }

    this.cache.set(key, value);
    this.timestamps.set(key, Date.now() + ttl);
  }

  get(key) {
    const timestamp = this.timestamps.get(key);
    
    if (!timestamp || Date.now() > timestamp) {
      this.delete(key);
      return null;
    }
    
    return this.cache.get(key);
  }

  delete(key) {
    this.cache.delete(key);
    this.timestamps.delete(key);
  }

  cleanup() {
    const now = Date.now();
    
    for (const [key, timestamp] of this.timestamps.entries()) {
      if (now > timestamp) {
        this.delete(key);
      }
    }
  }

  invalidatePattern(pattern) {
    const regex = new RegExp(pattern);
    const keysToDelete = [];
    
    for (const key of this.cache.keys()) {
      if (regex.test(key)) {
        keysToDelete.push(key);
      }
    }
    
    keysToDelete.forEach(key => this.delete(key));
    return keysToDelete.length;
  }
}

const apiCache = new ApiCache();

/**
 * Cache middleware for GET requests
 */
const cacheMiddleware = (ttl = 5 * 60 * 1000) => {
  return (req, res, next) => {
    // Only cache GET requests
    if (req.method !== 'GET') {
      return next();
    }

    // Skip caching for certain paths
    const skipPaths = ['/auth', '/upload', '/download'];
    if (skipPaths.some(path => req.path.includes(path))) {
      return next();
    }

    const key = apiCache.generateKey(req);
    const cached = apiCache.get(key);

    if (cached) {
      if (!res.headersSent) {
        res.set('X-Cache', 'HIT');
      }
      return res.json(cached);
    }

    // Override res.json to cache the response
    const originalJson = res.json;
    res.json = function(data) {
      // Only cache successful responses
      if (res.statusCode === 200) {
        apiCache.set(key, data, ttl);
        if (!res.headersSent) {
          res.set('X-Cache', 'MISS');
        }
      }

      return originalJson.call(this, data);
    };

    next();
  };
};

/**
 * Cache invalidation middleware for write operations
 */
const cacheInvalidationMiddleware = (req, res, next) => {
  // Only invalidate for write operations
  if (['POST', 'PUT', 'PATCH', 'DELETE'].includes(req.method)) {
    res.on('finish', () => {
      // Only invalidate on successful operations
      if (res.statusCode >= 200 && res.statusCode < 300) {
        let pattern = '';
        
        if (req.path.includes('/tasks')) {
          pattern = '(tasks|dashboard)';
        } else if (req.path.includes('/users')) {
          pattern = '(users|dashboard)';
        } else if (req.path.includes('/notifications')) {
          pattern = 'notifications';
        }
        
        if (pattern) {
          const invalidated = apiCache.invalidatePattern(pattern);
          if (process.env.NODE_ENV === 'development') {
            console.log(`Invalidated ${invalidated} cache entries for pattern: ${pattern}`);
          }
        }
      }
    });
  }
  
  next();
};

/**
 * Request size limiting middleware
 */
const requestSizeLimitMiddleware = (limit = '10mb') => {
  return (req, res, next) => {
    const contentLength = parseInt(req.get('content-length') || '0');
    const maxSize = parseSize(limit);
    
    if (contentLength > maxSize) {
      return res.status(413).json({
        success: false,
        message: `Request too large. Maximum size is ${limit}`
      });
    }
    
    next();
  };
};

/**
 * Parse size string to bytes
 */
const parseSize = (size) => {
  const units = {
    b: 1,
    kb: 1024,
    mb: 1024 * 1024,
    gb: 1024 * 1024 * 1024
  };
  
  const match = size.toString().toLowerCase().match(/^(\d+(?:\.\d+)?)\s*(b|kb|mb|gb)?$/);
  
  if (!match) {
    throw new Error('Invalid size format');
  }
  
  const value = parseFloat(match[1]);
  const unit = match[2] || 'b';
  
  return Math.floor(value * units[unit]);
};

/**
 * Database connection pooling optimization
 */
const optimizeMongoose = (mongoose) => {
  // Optimize connection pool settings
  mongoose.set('maxPoolSize', 10); // Maximum number of connections
  mongoose.set('serverSelectionTimeoutMS', 5000); // How long to try selecting a server
  mongoose.set('socketTimeoutMS', 45000); // How long a send or receive on a socket can take
  mongoose.set('bufferMaxEntries', 0); // Disable mongoose buffering
  mongoose.set('bufferCommands', false); // Disable mongoose buffering
};

/**
 * Health check endpoint with performance metrics
 */
const healthCheckEndpoint = (req, res) => {
  const memUsage = process.memoryUsage();
  const uptime = process.uptime();
  
  const health = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: Math.floor(uptime),
    memory: {
      rss: Math.round(memUsage.rss / 1024 / 1024),
      heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024),
      heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024),
      external: Math.round(memUsage.external / 1024 / 1024)
    },
    cache: {
      size: apiCache.cache.size,
      maxSize: apiCache.maxSize
    }
  };
  
  res.json(health);
};

/**
 * Performance metrics endpoint
 */
const metricsEndpoint = (req, res) => {
  const metrics = {
    cache: {
      size: apiCache.cache.size,
      maxSize: apiCache.maxSize,
      hitRate: 0 // Would need to implement hit/miss tracking
    },
    memory: process.memoryUsage(),
    uptime: process.uptime(),
    version: process.version,
    platform: process.platform,
    arch: process.arch
  };
  
  res.json(metrics);
};

/**
 * Compression middleware configuration
 */
const compressionMiddleware = compression({
  // Only compress responses larger than 1kb
  threshold: 1024,
  
  // Compression level (1-9, 6 is default)
  level: 6,
  
  // Only compress these MIME types
  filter: (req, res) => {
    if (req.headers['x-no-compression']) {
      return false;
    }
    
    return compression.filter(req, res);
  }
});

module.exports = {
  responseTimeMiddleware,
  memoryMonitoringMiddleware,
  cacheMiddleware,
  cacheInvalidationMiddleware,
  requestSizeLimitMiddleware,
  compressionMiddleware,
  optimizeMongoose,
  healthCheckEndpoint,
  metricsEndpoint,
  apiCache
};
