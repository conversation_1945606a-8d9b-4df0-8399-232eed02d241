import React from 'react';

/**
 * First feature section component for the landing page
 * @returns {JSX.Element} - Rendered component
 */
const FeatureSection1 = () => {
  return (
    <section id="features-main" className="py-20 md:py-28 px-6 sm:px-8 md:px-16 bg-white">
      <div className="container mx-auto flex flex-col lg:flex-row items-center gap-16 lg:gap-20">
        {/* Team Chat Feature Image */}
        <div className="lg:w-1/2" data-aos="fade-right" data-aos-duration="800">
          <div className="bg-green-50 p-5 sm:p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300 h-[420px] sm:h-[460px] flex flex-col">
            {/* Chat Header */}
            <div className="flex items-center pb-4 border-b border-green-100">
              <div className="flex -space-x-2 mr-3">
                <img src="https://randomuser.me/api/portraits/women/44.jpg" alt="User Avatar" className="w-10 h-10 rounded-full border-2 border-white" />
                <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="User Avatar" className="w-10 h-10 rounded-full border-2 border-white" />
              </div>
              <h4 className="text-base font-semibold text-slate-700">Team Chat</h4>
              <button className="ml-auto text-xs bg-green-100 text-green-600 px-2 py-1 rounded-full font-medium hover:bg-green-200">+ New</button>
            </div>
            
            {/* Chat Messages */}
            <div className="flex-grow py-4 overflow-y-auto space-y-4">
              {/* User 1 */}
              <div className="flex items-start">
                <img src="https://randomuser.me/api/portraits/women/44.jpg" alt="Maria Warner" className="w-8 h-8 rounded-full mr-2" />
                <div>
                  <div className="flex items-center">
                    <span className="font-medium text-sm text-slate-800">Maria Warner</span>
                    <span className="ml-2 text-xs text-slate-500">10:30 AM today</span>
                  </div>
                  <div className="mt-1 bg-white p-2 rounded-lg rounded-tl-none shadow-sm text-sm text-slate-700">
                    Has everyone reviewed the latest design updates?
                  </div>
                </div>
              </div>
              
              {/* User 2 */}
              <div className="flex items-start">
                <img src="https://randomuser.me/api/portraits/women/68.jpg" alt="Esther Howard" className="w-8 h-8 rounded-full mr-2" />
                <div>
                  <div className="flex items-center">
                    <span className="font-medium text-sm text-slate-800">Esther Howard</span>
                    <span className="ml-2 text-xs text-slate-500">10:32 AM today</span>
                    <span className="ml-2 w-2 h-2 bg-yellow-400 rounded-full"></span>
                  </div>
                  <div className="mt-1 bg-white p-2 rounded-lg rounded-tl-none shadow-sm text-sm text-slate-700">
                    Yes, I've added my comments to the Figma file.
                  </div>
                </div>
              </div>
              
              {/* User 3 */}
              <div className="flex items-start">
                <img src="https://randomuser.me/api/portraits/men/36.jpg" alt="Ralph Edwards" className="w-8 h-8 rounded-full mr-2" />
                <div>
                  <div className="flex items-center">
                    <span className="font-medium text-sm text-slate-800">Ralph Edwards</span>
                    <span className="ml-2 text-xs text-slate-500">10:35 AM today</span>
                  </div>
                  <div className="mt-1 bg-white p-2 rounded-lg rounded-tl-none shadow-sm text-sm text-slate-700">
                    I'm still working on it. Will update by EOD.
                  </div>
                </div>
              </div>
              
              {/* User 4 */}
              <div className="flex items-start">
                <img src="https://randomuser.me/api/portraits/women/24.jpg" alt="Theresa Gallo" className="w-8 h-8 rounded-full mr-2" />
                <div>
                  <div className="flex items-center">
                    <span className="font-medium text-sm text-slate-800">Theresa Gallo</span>
                    <span className="ml-2 text-xs text-slate-500">10:38 AM today</span>
                  </div>
                  <div className="mt-1 bg-white p-2 rounded-lg rounded-tl-none shadow-sm text-sm text-slate-700">
                    @Ralph can you share your progress so far?
                  </div>
                </div>
              </div>
            </div>
            
            {/* Group Members */}
            <div className="py-3 border-t border-green-100">
              <div className="flex items-center">
                <div className="flex -space-x-2">
                  <img src="https://randomuser.me/api/portraits/women/44.jpg" alt="User Avatar" className="w-7 h-7 rounded-full border-2 border-white" />
                  <img src="https://randomuser.me/api/portraits/women/68.jpg" alt="User Avatar" className="w-7 h-7 rounded-full border-2 border-white" />
                  <img src="https://randomuser.me/api/portraits/men/36.jpg" alt="User Avatar" className="w-7 h-7 rounded-full border-2 border-white" />
                </div>
                <div className="flex items-center justify-center w-7 h-7 rounded-full bg-green-100 text-green-600 text-xs font-medium border-2 border-white -ml-2">+3</div>
                <button className="ml-auto text-sm text-green-600 hover:text-green-700">View all</button>
              </div>
            </div>
            
            {/* Chat Input */}
            <div className="mt-auto pt-3 border-t border-green-100">
              <div className="flex items-center bg-white rounded-lg px-3 py-2">
                <input type="text" placeholder="Type your message..." className="flex-grow text-sm outline-none" />
                <button className="ml-2 text-green-600 hover:text-green-700">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
        
        {/* Feature Content */}
        <div className="lg:w-1/2 text-center lg:text-left" data-aos="fade-left" data-aos-duration="800" data-aos-delay="150">
          <span className="text-blue-600 font-semibold text-sm tracking-wider uppercase">FEATURES —</span>
          <h2 className="text-4xl sm:text-5xl font-extrabold text-slate-900 mt-3 mb-7 leading-tight">Effortless, real time connectivity</h2>
          <p className="text-slate-600 mb-9 text-lg leading-relaxed">Enjoy seamless, real-time communication and collaboration with your team. Stay effortlessly connected and keep projects on track without interruptions.</p>
          <button className="btn-primary">Get a free consultation</button>
        </div>
      </div>
    </section>
  );
};

export default FeatureSection1;
