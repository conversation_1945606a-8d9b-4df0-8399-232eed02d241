// This module holds the single instance of the Socket.IO server
// so it can be accessed from other parts of the application without circular dependencies.

let io = null;

const setSocketInstance = (socketInstance) => {
  io = socketInstance;
};

const getSocketInstance = () => {
  if (!io) {
    // This should not happen in the normal flow of the application
    // as the instance is set on server startup.
    console.error('Socket.IO has not been initialized.');
  }
  return io;
};

module.exports = {
  setSocketInstance,
  getSocketInstance,
};
