import axios from 'axios';
import User from '../models/User.js'; // Use .js extension for ESM

export const BASE_URL = 'http://localhost:3000/api';
import dotenv from 'dotenv';
import path from 'path';

// Load environment variables from .env file at the root of the backend directory
dotenv.config({ path: path.resolve(__dirname, '../.env') });

export const ADMIN_INVITE_TOKEN = process.env.ADMIN_INVITE_TOKEN;

// --- Logging Utilities ---
export const log = (message) => console.log(`[INFO] ${message}`);
export const logError = (message) => console.error(`[ERROR] ${message}`);

// --- User Management ---
const testUsers = {};

const getCsrfToken = async (client) => {
    const response = await client.get('/auth/csrf-token');
    if (!response.data.csrfToken) {
        throw new Error('Failed to fetch CSRF token.');
    }
    return response.data.csrfToken;
};

export const registerNewUser = async (client, userIdentifier) => {
    const userData = {
        email: `testuser_${userIdentifier}_${Date.now()}@example.com`,
        password: 'Password123!',
        name: `Test User ${userIdentifier}`,
    };
    testUsers[userIdentifier] = userData;

    const csrfToken = await getCsrfToken(client);
    const response = await client.post('/auth/register', userData, { 
        headers: { 'x-csrf-token': csrfToken }
    });
    
    const userId = response.data.user._id;
    testUsers[userIdentifier].userId = userId;
    return { ...userData, userId };
};

export const loginUser = async (client, userIdentifier) => {
    const { email, password } = testUsers[userIdentifier];
    const csrfToken = await getCsrfToken(client);
    const response = await client.post('/auth/login', { email, password }, { 
        headers: { 'x-csrf-token': csrfToken }
    });
    return response.data;
};

export const logoutUser = async (client) => {
    const csrfToken = await getCsrfToken(client);
    await client.post('/auth/logout', {}, { 
        headers: { 'x-csrf-token': csrfToken }
    });
};

export const cleanupUser = async (userIdentifier) => {
    const userData = testUsers[userIdentifier];
    if (!userData || !userData.email) return;
    try {
        await User.deleteOne({ email: userData.email });
        log(`Cleaned up user: ${userData.email}`);
    } catch (error) {
        logError(`Failed to clean up user ${userData.email}: ${error.message}`);
    }
};

export const promoteUserToAdmin = async (client, userId) => {
    const csrfToken = await getCsrfToken(client);
    await client.post(`/users/${userId}/promote`, { adminInviteToken: ADMIN_INVITE_TOKEN }, {
        headers: { 'x-csrf-token': csrfToken }
    });
    log(`Promoted user ${userId} to admin.`);
};

// --- Task Management ---
export const createTask = async (client, title, creatorId) => {
    const csrfToken = await getCsrfToken(client);
    const response = await client.post('/tasks', {
        title,
        description: 'A test task',
        status: 'todo',
        createdBy: creatorId
    }, { 
        headers: { 'x-csrf-token': csrfToken }
    });
    return response.data.task;
};

export const updateTask = async (client, taskId, updates) => {
    const csrfToken = await getCsrfToken(client);
    const response = await client.put(`/tasks/${taskId}`, updates, { 
        headers: { 'x-csrf-token': csrfToken }
    });
    return response.data.task;
};

// --- Notification Management ---
export const getNotifications = async (client, params = {}) => {
    const response = await client.get('/notifications', { params });
    return response.data;
};
