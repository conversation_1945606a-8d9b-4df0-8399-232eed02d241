/**
 * Token validation utilities
 */

/**
 * Check if a JWT token is expired
 * @param {string} token - JWT token
 * @returns {boolean} - True if token is expired
 */
export const isTokenExpired = (token) => {
  if (!token) return true;
  
  try {
    // Remove Bearer prefix if present
    const cleanToken = token.startsWith('Bearer ') ? token.substring(7) : token;
    
    // Decode JWT payload (without verification)
    const payload = JSON.parse(atob(cleanToken.split('.')[1]));
    
    // Check expiration
    const currentTime = Math.floor(Date.now() / 1000);
    return payload.exp < currentTime;
  } catch (error) {
    if (import.meta.env.DEV) {
      console.error('Error checking token expiration:', error);
    }
    return true; // Assume expired if we can't parse
  }
};

/**
 * Get token payload without verification
 * @param {string} token - JWT token
 * @returns {object|null} - Token payload or null if invalid
 */
export const getTokenPayload = (token) => {
  if (!token) return null;
  
  try {
    const cleanToken = token.startsWith('Bearer ') ? token.substring(7) : token;
    return JSON.parse(atob(cleanToken.split('.')[1]));
  } catch (error) {
    if (import.meta.env.DEV) {
      console.error('Error parsing token payload:', error);
    }
    return null;
  }
};

/**
 * Validate token format and structure
 * @param {string} token - JWT token
 * @returns {boolean} - True if token format is valid
 */
export const isValidTokenFormat = (token) => {
  if (!token || typeof token !== 'string') return false;
  
  const cleanToken = token.startsWith('Bearer ') ? token.substring(7) : token;
  const parts = cleanToken.split('.');
  
  return parts.length === 3 && parts.every(part => part.length > 0);
};

/**
 * Debug token information
 * @param {string} token - JWT token
 * @returns {object} - Token debug information
 */
export const debugToken = (token) => {
  const info = {
    exists: !!token,
    format: isValidTokenFormat(token),
    expired: false,
    payload: null,
    timeToExpiry: null
  };
  
  if (token && info.format) {
    info.expired = isTokenExpired(token);
    info.payload = getTokenPayload(token);
    
    if (info.payload && info.payload.exp) {
      const currentTime = Math.floor(Date.now() / 1000);
      info.timeToExpiry = info.payload.exp - currentTime;
    }
  }
  
  return info;
};
