import React, { useState, useEffect, useRef } from 'react';
import { LuBell, LuSettings, LuX, LuTrash2 } from 'react-icons/lu';
import { MdAssignment, MdMessage, MdNotifications } from 'react-icons/md';
import { useNavigate } from 'react-router-dom';
import { useNotifications } from '../../contexts/NotificationContext';
import { toast } from 'react-hot-toast';
import { formatDistanceToNow } from 'date-fns';
import { BASE_URL } from '../../utils/apiPaths';

const NotificationDropdown = ({ isOpen, onClose }) => {
  const navigate = useNavigate();
  const {
    notifications,
    unreadCount,
    loading,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    deleteAllNotifications
  } = useNotifications();
  const dropdownRef = useRef(null);

  // State to prevent double-clicking
  const [isMarkingAllRead, setIsMarkingAllRead] = useState(false);
  const [isClearingAll, setIsClearingAll] = useState(false);

  // Get icon and color for notification type
  const getNotificationIcon = (type) => {
    switch (type) {
      case 'task_assigned':
        return { icon: MdAssignment, color: 'text-blue-500' };
      case 'chat_message':
        return { icon: MdMessage, color: 'text-green-500' };
      default:
        return { icon: MdNotifications, color: 'text-gray-500' };
    }
  };

  // Format notification time
  const formatNotificationTime = (createdAt) => {
    try {
      return formatDistanceToNow(new Date(createdAt), { addSuffix: true });
    } catch {
      return 'Recently';
    }
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, onClose]);

  // Handle notification click with navigation
  const handleNotificationClick = async (notification) => {
    // Mark as read first
    const response = await markAsRead(notification._id);
    if (!response.success) {
      toast.error(response.error || 'Failed to mark notification as read');
      return;
    }

    // Handle navigation based on notification type
    if (notification.type === 'chat_message' && notification.data?.conversationId) {
      // Navigate to chat with the specific conversation
      navigate(`/chat?conversationId=${notification.data.conversationId}`);
      onClose(); // Close the dropdown
    } else if (notification.data?.taskId) {
      // Navigate to task details
      navigate(`/user/view-task-details/${notification.data.taskId}`);
      onClose();
    }
  };

  // Handle mark all as read with error handling
  const handleMarkAllAsRead = async (e) => {
    e.preventDefault();
    e.stopPropagation();

    if (isMarkingAllRead) return; // Prevent double-clicking

    try {
      setIsMarkingAllRead(true);
      const response = await markAllAsRead();

      if (response.success) {
        toast.success('All notifications marked as read');
      } else {
        toast.error(response.error || 'Failed to mark all notifications as read');
      }
    } catch {
      toast.error('Failed to mark all notifications as read');
    } finally {
      // Small delay to show feedback, then reset
      setTimeout(() => setIsMarkingAllRead(false), 100);
    }
  };

  // Handle delete notification with error handling
  const handleDeleteNotification = async (notificationId) => {
    const response = await deleteNotification(notificationId);
    if (!response.success) {
      toast.error(response.error || 'Failed to delete notification');
    }
  };

  // Handle clear all notifications with error handling
  const handleClearAllNotifications = async (e) => {
    e.preventDefault();
    e.stopPropagation();

    if (isClearingAll) return; // Prevent double-clicking

    try {
      setIsClearingAll(true);
      const response = await deleteAllNotifications();

      if (response.success) {
        toast.success(response.message || 'All notifications cleared');
      } else {
        toast.error(response.error || 'Failed to clear all notifications');
      }
    } catch {
      toast.error('Failed to clear all notifications');
    } finally {
      // Small delay to show feedback, then reset
      setTimeout(() => setIsClearingAll(false), 100);
    }
  };

  if (!isOpen) return null;

  return (
    <div
      ref={dropdownRef}
      className="absolute right-0 mt-2 w-96 bg-white rounded-lg shadow-xl border border-gray-200 z-50 max-h-[32rem] overflow-hidden"
    >
      {/* Header */}
      <div className="bg-blue-500 text-white px-4 py-3 flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <h3 className="font-semibold">Notifications</h3>
          {unreadCount > 0 && (
            <span className="bg-blue-600 text-xs px-2 py-1 rounded-full">
              {unreadCount}
            </span>
          )}
        </div>
        <button
          onClick={onClose}
          className="text-white hover:text-gray-200 transition-colors"
        >
          <LuX className="w-5 h-5" />
        </button>
      </div>

      {/* Notifications List */}
      <div className="max-h-80 overflow-y-auto">
        {loading ? (
          <div className="p-6 text-center text-gray-500">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
            <p>Loading notifications...</p>
          </div>
        ) : notifications.length === 0 ? (
          <div className="p-6 text-center text-gray-500">
            <LuBell className="w-12 h-12 mx-auto mb-2 text-gray-300" />
            <p>No notifications yet</p>
          </div>
        ) : (
          <div className="divide-y divide-gray-100">
            {notifications.map((notification) => {
              const { icon: IconComponent, color: iconColor } = getNotificationIcon(notification.type);
              const triggeredByUser = notification.triggeredBy;

              return (
                <div
                  key={notification._id}
                  className={`p-4 hover:bg-gray-50 transition-colors cursor-pointer relative group ${
                    !notification.isRead ? 'bg-blue-50' : ''
                  }`}
                  onClick={() => handleNotificationClick(notification)}
                >
                  <div className="flex items-start space-x-3">
                    {/* Avatar or Icon */}
                    <div className="relative flex-shrink-0">
                      {triggeredByUser ? (
                        <>
                          {triggeredByUser.profileImageUrl ? (
                            <img
                              src={`${BASE_URL}${triggeredByUser.profileImageUrl}`}
                              alt={triggeredByUser.name}
                              className="w-10 h-10 rounded-full object-cover"
                              onError={(e) => {
                                e.target.style.display = 'none';
                                e.target.nextSibling.style.display = 'flex';
                              }}
                            />
                          ) : null}

                          {/* Fallback avatar with user initial */}
                          <div
                            className={`w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center ${
                              triggeredByUser.profileImageUrl ? 'hidden' : ''
                            }`}
                          >
                            <span className="text-blue-600 font-medium text-sm">
                              {triggeredByUser.name?.charAt(0)?.toUpperCase() || 'U'}
                            </span>
                          </div>

                          {/* Small icon overlay for user notifications */}
                          <div className="absolute -bottom-1 -right-1 w-5 h-5 bg-white rounded-full flex items-center justify-center border border-gray-200">
                            <IconComponent className={`w-3 h-3 ${iconColor}`} />
                          </div>
                        </>
                      ) : (
                        /* System notification icon */
                        <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                          <IconComponent className={`w-5 h-5 ${iconColor}`} />
                        </div>
                      )}
                    </div>

                    {/* Content */}
                    <div className="flex-1 min-w-0">
                      <p className="text-sm text-gray-900 break-words">
                        <span className="font-medium">{notification.title}</span>
                      </p>
                      <p className="text-sm text-gray-600 mt-1 break-words">
                        {notification.message}
                      </p>
                      <p className="text-xs text-gray-500 mt-1">
                        {formatNotificationTime(notification.createdAt)}
                      </p>
                    </div>

                    {/* Unread indicator */}
                    {!notification.isRead && (
                      <div className="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0 mt-2"></div>
                    )}

                    {/* Delete button */}
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDeleteNotification(notification._id);
                      }}
                      className="opacity-0 group-hover:opacity-100 text-gray-400 hover:text-red-500 transition-all"
                    >
                      <LuTrash2 className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>

      {/* Footer */}
      {notifications.length > 0 && (
        <div className="border-t border-gray-100 p-3 space-y-2">
          {unreadCount > 0 && (
            <button
              onClick={handleMarkAllAsRead}
              disabled={isMarkingAllRead}
              className={`w-full text-sm font-medium transition-colors py-2 ${
                isMarkingAllRead
                  ? 'text-gray-400 cursor-not-allowed'
                  : 'text-blue-600 hover:text-blue-800'
              }`}
            >
              {isMarkingAllRead ? 'Marking as read...' : 'Mark all as read'}
            </button>
          )}
          <button
            onClick={handleClearAllNotifications}
            disabled={isClearingAll}
            className={`w-full text-sm font-medium transition-colors py-2 border-t border-gray-100 ${
              isClearingAll
                ? 'text-gray-400 cursor-not-allowed'
                : 'text-red-600 hover:text-red-800'
            }`}
          >
            {isClearingAll ? 'Clearing...' : 'Clear all notifications'}
          </button>
        </div>
      )}
    </div>
  );
};

export default NotificationDropdown;
