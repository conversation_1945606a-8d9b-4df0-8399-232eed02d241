/**
 * Refresh Token Middleware
 * Handles token refresh and validation with improved error handling
 */

const jwt = require('jsonwebtoken');
const User = require('../models/User');
const tokenService = require('../services/tokenService');

// Token refresh lock map to prevent concurrent refreshes
const refreshLocks = new Map();

const refreshTokenMiddleware = async (req, res, next) => {
  try {
    // Defensive: check req.cookies exists
    if (!req.cookies) {
      return res.status(400).json({ 
        success: false, 
        message: 'Cookies not parsed',
        code: 'COOKIES_MISSING'
      });
    }

    // Get refresh token from cookie
    const refreshToken = req.cookies.refreshToken;
    if (!refreshToken) {
      return res.status(401).json({ 
        success: false, 
        message: 'Refresh token required',
        code: 'REFRESH_TOKEN_MISSING'
      });
    }

    // Prevent concurrent refresh attempts for the same token
    if (refreshLocks.get(refreshToken)) {
      return res.status(429).json({
        success: false,
        message: 'Token refresh in progress',
        code: 'REFRESH_IN_PROGRESS'
      });
    }

    refreshLocks.set(refreshToken, true);

    try {
      // Verify refresh token
      let decoded;
      try {
        decoded = jwt.verify(refreshToken, process.env.REFRESH_TOKEN_SECRET);
      } catch (err) {
        if (err.name === 'TokenExpiredError') {
          return res.status(401).json({
            success: false,
            message: 'Refresh token has expired',
            code: 'REFRESH_TOKEN_EXPIRED'
          });
        }
        throw err;
      }

      // Find user and check if refresh token exists
      const user = await User.findById(decoded.id);
      if (!user || !user.refreshTokens || !user.refreshTokens.includes(refreshToken)) {
        return res.status(401).json({
          success: false,
          message: 'Refresh token not recognized',
          code: 'INVALID_REFRESH_TOKEN'
        });
      }

      // Generate new tokens
      const { accessToken, refreshToken: newRefreshToken } = await tokenService.generateTokens(user._id);

      // Update tokens atomically
      await User.findByIdAndUpdate(user._id, {
        $pull: { refreshTokens: refreshToken },
        $push: { refreshTokens: newRefreshToken }
      }, { new: true });

      // Set cookies
      res.cookie('accessToken', accessToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 15 * 60 * 1000 // 15 minutes
      });

      res.cookie('refreshToken', newRefreshToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 7 * 24 * 60 * 60 * 1000 // 7 days
      });

      req.user = user;
      next();
    } finally {
      // Always clean up the lock
      refreshLocks.delete(refreshToken);
    }
  } catch (error) {
    console.error('Token refresh error:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error during token refresh',
      code: 'REFRESH_ERROR'
    });
  }
};

module.exports = { refreshTokenMiddleware };
