/**
 * Task Controller - Refactored
 * Uses service layer for better separation of concerns
 */

const { handleServerError } = require('../utils/errorHandlers');
const { buildTaskFilters } = require('../utils/taskHelpers');
const { getTaskStatusCounts } = require('../services/taskStatisticsService');
const taskCrudService = require('../services/taskCrudService');

const {
  getTasks: getTasksFromService,
  getTaskById: getTaskByIdFromService,
  createTask: createTaskFromService,
  updateTask: updateTaskFromService,
  deleteTask: deleteTaskFromService,
  updateTaskStatus: updateTaskStatusFromService,
  updateTaskChecklist: updateTaskChecklistFromService,
  bulkDeleteTasks: bulkDeleteTasksFromService,
  checkTaskAccess
} = taskCrudService;
const {
  getAdminDashboardData,
  getUserDashboardData: getUserDashboardDataFromService
} = require('../services/taskDashboardService');
const User = require('../models/User');
const {
  createTaskAssignmentNotifications,
  createTaskDeletionNotifications,
  createTaskUserRemovedNotification,

  createTaskUserReplacedNotification,
} = require('../services/taskNotificationService');

// @desc    Get all tasks (Admin: all, Users: only assigned tasks)
// @route   GET /api/tasks/
// @access  Private
const getTasks = async (req, res) => {
  try {
    const { status, page = 1, limit = 10 } = req.query;
    const isAdmin = req.user.role === 'admin';

    // Build filter based on user role and query parameters
    const filterOptions = { status };
    if (!isAdmin) {
      filterOptions.userId = req.user._id;
    }
    const filter = buildTaskFilters(filterOptions);
    if (process.env.NODE_ENV === 'development') {
      console.log('GET /api/tasks filter:', filter);
    }

    // Get paginated tasks using service
    const { tasks, total, totalPages } = await getTasksFromService(filter, { page, limit });
    if (process.env.NODE_ENV === 'development') {
      console.log('GET /api/tasks returned tasks count:', tasks.length);
      // console.log('GET /api/tasks returned tasks:', tasks); // Uncomment for full task details if needed
    }

    // Get task counts by status
    const statusCounts = await getTaskStatusCounts(req.user);

    res.json({
      tasks,
      total,
      page: Number(page),
      limit: Number(limit),
      totalPages,
      statusSummary: statusCounts
    });
  } catch (error) {
    handleServerError(res, error);
  }
};

// @desc    Get task by ID
// @route   GET /api/tasks/:id
// @access  Private
const getTaskById = async (req, res) => {
  try {
    const task = await getTaskByIdFromService(req.params.id);
    console.log('DEBUG getTaskById: task =', JSON.stringify(task, null, 2));
    console.log('DEBUG getTaskById: req.user =', JSON.stringify(req.user, null, 2));

    if (!task) {
      console.log('DEBUG getTaskById: Task not found');
      return res.status(404).json({ message: "Task not found" });
    }

    // Check authorization using service
    const hasAccess = checkTaskAccess(task, req.user);
    console.log('DEBUG getTaskById: hasAccess =', hasAccess);

    if (!hasAccess) {
      console.log('DEBUG getTaskById: Not authorized');
      // Send a specific response for the frontend to handle the redirect
      return res.status(403).json({ 
        unassigned: true, 
        message: "You are no longer assigned to this task and do not have permission to view it."
      });
    }

    res.json({ task });
  } catch (error) {
    console.error(`❌ Error in getTaskById:`, error);
    if (error.message === 'Invalid task ID') {
      return res.status(400).json({ message: error.message });
    }
    handleServerError(res, error);
  }
};

// @desc    Create a new task (Admin only)
// @route   POST /api/tasks/
// @access  Private (Admin)
const createTask = async (req, res) => {
  try {
    const {
      title,
      description,
      priority,
      dueDate,
      assignedTo,
      attachments,
      todoChecklist, // accept camelCase from frontend
      todoCheckList // legacy
    } = req.body;

    // Validate assignedTo is an array (allow null/undefined for tasks without assignments)
    if (assignedTo !== null && assignedTo !== undefined && !Array.isArray(assignedTo)) {
      return res.status(400).json({ message: "assignedTo must be an array of user IDs" });
    }
    
    // Map frontend todoChecklist to backend subTasks schema
    const checklist = req.body.todoChecklist || req.body.todoCheckList || [];
    const formattedSubTasks = checklist.map(item => ({
      title: item.text,
      isCompleted: item.completed === 'true' || item.completed === true,
    }));

    const finalAttachments = [];
    if (req.files && Array.isArray(req.files)) {
      req.files.forEach(file => {
        finalAttachments.push({
          name: file.originalname,
          url: file.filename, // Store only the filename
          originalName: file.originalname,
          size: file.size,
          mimeType: file.mimetype
        });
      });
    }

    const taskData = {
      title,
      description,
      priority,
      deadline: dueDate,
      team: assignedTo || [],
      attachments: finalAttachments,
      subTasks: formattedSubTasks,
      createdBy: req.user._id
    };

    const createdTask = await createTaskFromService(taskData, req.user._id);

    // Fetch the newly created task with populated fields (assigned users, etc.)
    const task = await getTaskByIdFromService(createdTask._id);



    // --- Normalize response for frontend ---
    const normalizedTask = {
      ...task,
      assignedTo: Array.isArray(task.team) ? [...task.team] : [],
      dueDate: task.dueDate || task.deadline || null,
      todoCheckList: task.todoCheckList || task.subTasks || [],
      attachments: task.attachments || [],
    };

    const io = require('../socket/socketInstance').getSocketInstance();
    if (io && assignedTo) {
      assignedTo.forEach(userId => {
        io.to(userId).emit('taskCreated', normalizedTask);
      });
    }

    res.status(201).json({ message: "Task created successfully", task: normalizedTask });
  } catch (error) {
    if (error.message.includes('validation') || error.message.includes('Invalid')) {
      return res.status(400).json({ message: error.message });
    }
    handleServerError(res, error);
  }
};

// @desc    Update task details
// @route   PUT /api/tasks/:id
// @access  Private
const updateTask = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = { ...req.body };
    console.log('updateTask: Received body:', JSON.stringify(req.body, null, 2));
    if (req.files) {
      console.log('updateTask: Received files:', JSON.stringify(req.files, null, 2));
    }

    // --- Simplified Attachment Handling ---
    let finalAttachments = [];

    // Handle existing attachments
    if (req.body.existingAttachments) {
      try {
        const existing = JSON.parse(req.body.existingAttachments);
        if (Array.isArray(existing)) {
          finalAttachments = existing;
        }
      } catch (e) {
        // ignore if parsing fails
      }
    }

    // Handle new file uploads
    if (req.files && Array.isArray(req.files)) {
      const newUploads = req.files.map(file => ({
        name: file.originalname,
        url: file.filename, // Store only the filename
        originalName: file.originalname,
        size: file.size,
        mimeType: file.mimetype,
      }));
      finalAttachments.push(...newUploads);
    }
    
    updateData.attachments = finalAttachments;

    const currentTask = await getTaskByIdFromService(id);
    if (!currentTask) {
      return res.status(404).json({ message: "Task not found" });
    }
    // Always map assignedTo to team for consistency
    if (updateData.assignedTo !== undefined) {
      updateData.team = updateData.assignedTo;
      delete updateData.assignedTo;
    }

    // Handle checklist updates
    if (updateData.todoCheckList) {
      updateData.subTasks = updateData.todoCheckList.map(item => ({
        title: item.text,
        isCompleted: item.completed === 'true' || item.completed === true,
      }));
      delete updateData.todoCheckList;
    }

    // Sanitize status and priority to match schema enums
    if (updateData.status) {
      updateData.status = updateData.status.toLowerCase().replace(/\s+/g, '-');
    }
    if (updateData.priority) {
      updateData.priority = updateData.priority.toLowerCase();
    }

    if (!currentTask) {
      return res.status(404).json({ message: "Task not found" });
    }

    if (!checkTaskAccess(currentTask, req.user)) {
      return res.status(403).json({ message: "Not authorized to update this task" });
    }

    const originalTeam = currentTask.team.map(t => t._id.toString());
    const updatedTeam = (updateData.team || []).map(t => t.toString());

    const removedUsers = originalTeam.filter(userId => !updatedTeam.includes(userId));
    const addedUsers = updatedTeam.filter(userId => !originalTeam.includes(userId));

    const updatedTask = await updateTaskFromService(id, updateData, req.user._id);

    if (!updatedTask) {
      return res.status(404).json({ message: "Task not found after update attempt." });
    }

    const io = require('../socket/socketInstance').getSocketInstance();
    // Notify users who were removed
    if (removedUsers.length > 0) {
      for (const userId of removedUsers) {
        await createTaskUserRemovedNotification(updatedTask, userId, req.user._id);
        io.to(userId).emit('taskDeleted', { taskId: updatedTask._id });
      }
    }



    res.json({ message: "Task updated successfully", task: updatedTask });
  } catch (error) {
    console.error('Error in updateTask controller:', error);
    if (error.name === 'ValidationError') {
      return res.status(400).json({ message: error.message });
    }
    handleServerError(res, error);
  }
};

// @desc    Update task status
// @route   PUT /api/tasks/:id/status
// @access  Private
const updateTaskStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    const updatedTask = await updateTaskStatusFromService(id, status, req.user._id);

    if (!updatedTask) {
      return res.status(404).json({ message: "Task not found or failed to update." });
    }

    res.json({ message: "Task status updated successfully", task: updatedTask });
  } catch (error) {
    handleServerError(res, error);
  }
};

// @desc    Delete a task (Admin only)
// @route   DELETE /api/tasks/:id
// @access  Private (Admin)
const deleteTask = async (req, res) => {
  try {
    const task = await getTaskByIdFromService(req.params.id);

    if (!task) {
      return res.status(404).json({ message: "Task not found" });
    }

    try {
      await createTaskDeletionNotifications(task, req.user._id);
    } catch (notificationError) {
      if (process.env.NODE_ENV === 'development') {
        console.error('Error creating task deletion notifications:', notificationError);
      }
    }

    await deleteTaskFromService(req.params.id);

    const io = require('../socket/socketInstance').getSocketInstance();
    if (io && task.team) {
      task.team.forEach(userId => {
        io.to(userId.toString()).emit('taskDeleted', { taskId: req.params.id });
      });
    }

    res.json({ message: "Task deleted successfully" });
  } catch (error) {
    handleServerError(res, error);
  }
};

// @desc    Update task checklist
// @route   PUT /api/tasks/:id/checklist
// @access  Private
const updateTaskChecklist = async (req, res) => {
  try {
    const { id } = req.params;
    const { subTasks } = req.body; // Renamed from todoChecklist to match frontend

    if (!subTasks || !Array.isArray(subTasks)) {
      return res.status(400).json({ message: "Request body must contain a 'subTasks' array." });
    }

    // The service layer will handle the core logic, including finding the task
    // and updating it. We perform the access check here for security.
    const task = await getTaskByIdFromService(id);
    if (!task) {
      return res.status(404).json({ message: 'Task not found' });
    }

    if (!checkTaskAccess(task, req.user)) {
      return res.status(403).json({ message: 'Not authorized' });
    }

    // Call the service function, which now contains the robust update logic
    const updatedTask = await updateTaskChecklistFromService(id, subTasks, req.user._id);

    if (!updatedTask) {
      return res.status(404).json({ message: 'Task checklist could not be updated' });
    }

    res.json({ message: 'Task checklist updated successfully', task: updatedTask });
  } catch (error) {
    handleServerError(res, error);
  } 
};

// @desc    Bulk delete tasks (Admin only)
// @route   DELETE /api/tasks/bulk
// @access  Private (Admin)
const bulkDeleteTasks = async (req, res) => {
  try {
    const { taskIds } = req.body;

    if (!Array.isArray(taskIds) || taskIds.length === 0) {
      return res.status(400).json({ message: "taskIds must be a non-empty array" });
    }

    const result = await bulkDeleteTasksFromService(taskIds);

    res.json({ 
      message: `Successfully deleted ${result.deletedCount} out of ${result.requestedCount} tasks`,
      ...result 
    });
    
  } catch (error) {
    handleServerError(res, error);
  }
};

// @desc    Get dashboard data (Admin)
// @route   GET /api/tasks/dashboard-data
// @access  Private (Admin)
const getDashboardData = async (req, res) => {
  try {
    const data = await getAdminDashboardData();
    res.json(data);
  } catch (error) {
    handleServerError(res, error);
  }
};

// @desc    Get dashboard data (User)
// @route   GET /api/tasks/user-dashboard-data
// @access  Private
const getUserDashboardData = async (req, res) => {
  try {
    const data = await getUserDashboardDataFromService(req.user._id);
    res.json(data);
  } catch (error) {
    handleServerError(res, error);
  }
};

module.exports = {
  getTasks,
  getTaskById,
  createTask,
  updateTask,
  deleteTask,
  updateTaskStatus,
  updateTaskChecklist,
  bulkDeleteTasks,
  getDashboardData,
  getUserDashboardData,
};
