import React, { useState } from 'react';
import { useNotifications } from '../../contexts/NotificationContext';
import axiosInstance from '../../utils/axiosInstance';
import toast from 'react-hot-toast';

const TestNotifications = () => {
  const { notifications, unreadCount, fetchNotifications } = useNotifications();
  const [loading, setLoading] = useState(false);

  const createTestTask = async () => {
    setLoading(true);
    try {
      const response = await axiosInstance.post('/api/tasks', {
        title: 'Test Notification Task - ' + new Date().toLocaleTimeString(),
        description: 'This is a test task to verify notifications work',
        priority: 'medium',
        dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
        assignedTo: ['68495a4be3fd9de62d8a3ac'] // Replace with actual user ID
      });
      
      if (response.data && response.data.task) {
        toast.success('Test task created! Check for notification.');
        // Refresh notifications after a short delay
        setTimeout(() => {
          fetchNotifications();
        }, 1000);
      }
    } catch (error) {
      toast.error('Failed to create test task: ' + (error.response?.data?.message || error.message));
    } finally {
      setLoading(false);
    }
  };

  const refreshNotifications = () => {
    fetchNotifications();
    toast.success('Notifications refreshed');
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-md max-w-md">
      <h3 className="text-lg font-semibold mb-4">Test Notifications</h3>
      
      <div className="space-y-3 mb-4">
        <div className="text-sm">
          <strong>Current Status:</strong>
          <div>Notifications: {notifications.length}</div>
          <div>Unread: {unreadCount}</div>
        </div>
        
        <button
          onClick={createTestTask}
          disabled={loading}
          className="w-full bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:opacity-50"
        >
          {loading ? 'Creating...' : 'Create Test Task'}
        </button>
        
        <button
          onClick={refreshNotifications}
          className="w-full bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600"
        >
          Refresh Notifications
        </button>
      </div>
      
      {notifications.length > 0 && (
        <div>
          <h4 className="font-medium mb-2">Recent Notifications:</h4>
          <div className="space-y-2 max-h-40 overflow-y-auto">
            {notifications.slice(0, 3).map(notif => (
              <div key={notif._id} className="text-xs p-2 bg-gray-100 rounded">
                <div className="font-medium">{notif.title}</div>
                <div className="text-gray-600">{notif.message}</div>
                <div className="text-gray-500">{notif.type} - {notif.isRead ? 'Read' : 'Unread'}</div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default TestNotifications;
