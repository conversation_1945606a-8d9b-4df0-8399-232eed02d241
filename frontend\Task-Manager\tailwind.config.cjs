/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./index.html",
    "./src/**/*.{js,jsx}",
  ],
  theme: {
    extend: {
      fontFamily: {
        display: ['Poppins', 'sans-serif'],
      },
      colors: {
        primary: '#1368EC',
        'auth-btn': '#3839ec',
        'auth-btn-hover': '#353BCF',
        'auth-btn-focus': '#3c43f4',
      },
      screens: {
        '3xl': '1920px',
      },
      keyframes: {
        'pulse-cta': {
          '0%, 100%': { transform: 'scale(1)' },
          '50%': { transform: 'scale(1.05)' },
        },
        'float-1': {
          '0%': { transform: 'translateY(0) rotate(0deg) scale(1)' },
          '25%': { transform: 'translateY(10px) rotate(2deg) scale(1.02)' },
          '50%': { transform: 'translateY(18px) rotate(0deg) scale(1)' },
          '75%': { transform: 'translateY(8px) rotate(-2deg) scale(0.98)' },
          '100%': { transform: 'translateY(0) rotate(0deg) scale(1)' },
        },
        'float-2': {
          '0%': { transform: 'translate(0, 0) rotate(0deg)' },
          '25%': { transform: 'translate(12px, -10px) rotate(3deg)' },
          '50%': { transform: 'translate(0, 0) rotate(0deg)' },
          '75%': { transform: 'translate(-12px, 10px) rotate(-3deg)' },
          '100%': { transform: 'translate(0, 0) rotate(0deg)' },
        },
        'float-3': {
          '0%': { transform: 'translate(0, 0) scale(1) rotate(0deg)' },
          '20%': { transform: 'translate(12px, 12px) scale(1.03) rotate(2deg)' },
          '40%': { transform: 'translate(5px, 24px) scale(1.05) rotate(4deg)' },
          '60%': { transform: 'translate(-5px, 24px) scale(1.03) rotate(2deg)' },
          '80%': { transform: 'translate(-12px, 12px) scale(1.01) rotate(-2deg)' },
          '100%': { transform: 'translate(0, 0) scale(1) rotate(0deg)' },
        },
        'float-4': {
          '0%': { transform: 'translate(0, 0) rotate(0deg) scale(1)' },
          '20%': { transform: 'translate(8px, -8px) rotate(2deg) scale(1.02)' },
          '40%': { transform: 'translate(16px, -16px) rotate(4deg) scale(1.04)' },
          '60%': { transform: 'translate(12px, -12px) rotate(2deg) scale(1.02)' },
          '80%': { transform: 'translate(4px, -4px) rotate(1deg) scale(1.01)' },
          '100%': { transform: 'translate(0, 0) rotate(0deg) scale(1)' },
        },
        'float-5': {
          '0%': { transform: 'translate(0, 0) rotate(0deg) scale(1)' },
          '10%': { transform: 'translate(2px, -15px) rotate(3deg) scale(1.04)' },
          '20%': { transform: 'translate(-1px, -2px) rotate(-1deg) scale(0.98)' },
          '30%': { transform: 'translate(1px, -8px) rotate(2deg) scale(1.02)' },
          '40%': { transform: 'translate(-2px, -1px) rotate(-1deg) scale(0.99)' },
          '50%': { transform: 'translate(0, -5px) rotate(1deg) scale(1.01)' },
          '60%': { transform: 'translate(2px, -1px) rotate(0deg) scale(1)' },
          '70%': { transform: 'translate(-1px, -3px) rotate(-1deg) scale(1.01)' },
          '80%': { transform: 'translate(1px, -1px) rotate(1deg) scale(1)' },
          '100%': { transform: 'translate(0, 0) rotate(0deg) scale(1)' },
        },
      },
      animation: {
        'pulse-cta': 'pulse-cta 2.5s infinite ease-in-out',
        'float-1': 'float-1 6s infinite cubic-bezier(0.4, 0, 0.6, 1)',
        'float-2': 'float-2 7s infinite cubic-bezier(0.4, 0, 0.6, 1)',
        'float-3': 'float-3 10s infinite cubic-bezier(0.45, 0, 0.55, 1)',
        'float-4': 'float-4 8s infinite cubic-bezier(0.4, 0, 0.6, 1)',
        'float-5': 'float-5 9s infinite cubic-bezier(0.25, 0.1, 0.25, 1)',
      },
    },
  },
  // Enable necessary core plugins
  corePlugins: {
    gradientColorStops: true,
    backgroundImage: true,
    backgroundColor: true,
    textColor: true,
    borderColor: true,
    borderRadius: true,
    padding: true,
    margin: true,
    fontWeight: true,
    fontSize: true,
    boxShadow: true,
    transform: true,
    transitionProperty: true,
    scale: true,
  },
  // Additional plugins
  plugins: [],
}
