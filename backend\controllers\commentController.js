const Comment = require('../models/Comment');
const Task = require('../models/Task');

// @desc    Create a new comment
// @route   POST /api/tasks/:taskId/comments
// @access  Private
const createComment = async (req, res) => {
  try {
    const { content } = req.body;
    const { taskId } = req.params;
    const userId = req.user._id; // Fixed: Use _id instead of id

    const task = await Task.findById(taskId);

    if (!task) {
      return res.status(404).json({ message: 'Task not found' });
    }

    const comment = await Comment.create({
      content,
      task: taskId,
      user: userId,
    });

    task.comments.push(comment._id);
    await task.save();

    const populatedComment = await Comment.findById(comment._id).populate('user', 'name profileImage').lean();

    res.status(201).json(populatedComment);
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// @desc    Get all comments for a task
// @route   GET /api/tasks/:taskId/comments
// @access  Private
const getCommentsForTask = async (req, res) => {
  try {
    const { taskId } = req.params;
    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 10;
    const skip = (page - 1) * limit;

    const total = await Comment.countDocuments({ task: taskId });

    const comments = await Comment.find({ task: taskId })
      .sort({ isPinned: -1, createdAt: -1 })
      .limit(limit)
      .skip(skip)
      .populate('user', 'name profileImage')
      .lean();

    res.status(200).json({
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
      data: comments,
    });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// @desc    Pin a comment
// @route   PUT /api/comments/:commentId/pin
// @access  Private
const pinComment = async (req, res) => {
  try {
    const { commentId } = req.params;
    const comment = await Comment.findById(commentId);

    if (!comment) {
      return res.status(404).json({ message: 'Comment not found' });
    }

    // Optional: Add logic to check if user has permission to pin

    comment.isPinned = !comment.isPinned;
    await comment.save();

    res.status(200).json(comment);
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

module.exports = {
  createComment,
  getCommentsForTask,
  pinComment,
};
