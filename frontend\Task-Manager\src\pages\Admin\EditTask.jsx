import React, { useState, useEffect, useCallback } from "react";
import DashboardLayout from "../../components/layout/DashboardLayout";
import { PRIORITY_DATA } from "../../utils/data";
import axiosInstance from "../../utils/axiosInstance";
import { API_PATHS } from "../../utils/apiPaths";
import SelectDropdown from "../../components/Inputs/SelectDropdown";
import SelectUsers from "../../components/Inputs/SelectUsers";
import TodoListInput from "../../components/Inputs/TodoListInput";
import AddAttachmentsInput from "../../components/Inputs/AddAttachmentsInput";
import toast from "react-hot-toast";
import { useNavigate, useParams } from "react-router-dom";
import { BiLoaderAlt } from "react-icons/bi";
import { normalizeAndValidateChecklist } from '../../utils/todoUtils';
import { mapTaskFromApi } from '../../utils/taskUtils';

const EditTask = () => {
  const { id: taskId } = useParams();
  const navigate = useNavigate();
  const [allUsers, setAllUsers] = useState([]);
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(false);
  const [fetching, setFetching] = useState(true);
  const [taskData, setTaskData] = useState(null);

  const handleValueChange = useCallback((key, value) => {
    setTaskData((prevData) => ({
      ...prevData,
      [key]: value,
    }));
  }, []);

  const handleRenameAttachment = useCallback((id, newName) => {
    setTaskData(prev => {
      const newAttachments = prev.attachments.map(att => {
        if (att._id === id || att.id === id) {
          return { ...att, name: newName };
        }
        return att;
      });
      return { ...prev, attachments: newAttachments };
    });
  }, []);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [taskResponse, usersResponse] = await Promise.all([
          axiosInstance.get(API_PATHS.TASKS.GET_BY_ID(taskId)),
          axiosInstance.get(API_PATHS.USERS.GET_ALL)
        ]);

        const task = taskResponse.data.task || taskResponse.data;
        const normalizedTask = mapTaskFromApi(task);
        setTaskData(normalizedTask);

        const rawUsers = Array.isArray(usersResponse.data)
          ? usersResponse.data
          : usersResponse.data?.data || [];
        const users = rawUsers.map(user => ({ ...user, fullName: user.name || user.fullName }));
        setAllUsers(users);

      } catch (err) {
        console.error("Failed to fetch initial data:", err);
        setError("Failed to load required data. Please try again.");
        toast.error("Failed to load data.");
      } finally {
        setFetching(false);
      }
    };

    if (taskId) {
      fetchData();
    }
  }, [taskId]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (loading) return;
    if (!taskData.title) {
      setError('Title is required');
      return;
    }
    setError("");
    setLoading(true);

    try {
      const formData = new FormData();
      formData.append("title", taskData.title);
      formData.append("description", taskData.description);
      formData.append("priority", taskData.priority);
      if (taskData.dueDate) {
        formData.append("dueDate", taskData.dueDate);
      }

      (taskData.assignedTo || []).forEach(user => formData.append("assignedTo[]", typeof user === 'string' ? user : user._id));

      const validTodos = normalizeAndValidateChecklist(taskData.todoCheckList);
      validTodos.forEach((todo, index) => {
        formData.append(`todoCheckList[${index}][text]`, todo.text);
        formData.append(`todoCheckList[${index}][completed]`, todo.completed);
      });

      const existingAttachments = taskData.attachments.filter(a => a.isExisting).map(a => ({
        _id: a._id,
        name: a.name,
        url: a.url,
      }));
      formData.append("existingAttachments", JSON.stringify(existingAttachments));

      taskData.attachments.forEach(attachment => {
        if (attachment.file) {
          formData.append("attachments", attachment.file, attachment.name);
        }
      });

      await axiosInstance.put(API_PATHS.TASKS.UPDATE(taskId), formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });

      toast.success('Task updated successfully');
      navigate('/admin/tasks');
    } catch (error) {
      toast.error(error?.response?.data?.message || 'Failed to update task');
    } finally {
      setLoading(false);
    }
  };

  if (fetching) {
    return (
      <DashboardLayout>
        <div className="flex justify-center items-center h-64">
          <BiLoaderAlt className="animate-spin text-4xl text-indigo-600" />
        </div>
      </DashboardLayout>
    );
  }

  if (error) {
    return (
      <DashboardLayout>
        <div className="flex flex-col justify-center items-center h-64 bg-red-50 p-4 rounded-lg">
          <p className="text-red-600 text-lg font-semibold">Error</p>
          <p className="text-red-500 mt-2 text-center">{error}</p>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="max-w-3xl mx-auto bg-white p-8 rounded-2xl shadow-lg">
        <h2 className="text-3xl font-bold mb-8 text-gray-800">Edit Task</h2>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <label htmlFor="title" className="block text-sm font-medium text-gray-600 mb-1">Title</label>
            <input
              type="text"
              id="title"
              value={taskData.title}
              onChange={(e) => handleValueChange('title', e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition"
              required
            />
          </div>

          <div>
            <label htmlFor="description" className="block text-sm font-medium text-gray-600 mb-1">Description</label>
            <textarea
              id="description"
              rows="5"
              value={taskData.description}
              onChange={(e) => handleValueChange('description', e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition"
            ></textarea>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label htmlFor="priority" className="block text-sm font-medium text-gray-600 mb-1">Priority</label>
              <SelectDropdown
                options={PRIORITY_DATA}
                selected={taskData.priority}
                onSelect={(priority) => handleValueChange('priority', priority.value)}
              />
            </div>
            <div>
              <label htmlFor="dueDate" className="block text-sm font-medium text-gray-600 mb-1">Due Date</label>
              <input
                type="date"
                id="dueDate"
                value={taskData.dueDate ? new Date(taskData.dueDate).toISOString().split('T')[0] : ''}
                onChange={(e) => handleValueChange('dueDate', e.target.value)}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-600 mb-1">Assign To</label>
            <SelectUsers
              selectedUsers={taskData.assignedTo.map(u => typeof u === 'string' ? u : u._id)}
              allUsers={allUsers}
              onChange={(users) => handleValueChange('assignedTo', users)}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-600 mb-2">Todo Checklist</label>
            <TodoListInput
              todos={taskData.todoCheckList}
              setTodos={(todos) => handleValueChange('todoCheckList', todos)}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-600 mb-2">Attachments</label>
            <AddAttachmentsInput
              attachments={taskData.attachments}
              onAttachmentsChange={(attachments) => handleValueChange('attachments', attachments)}
              onRenameAttachment={handleRenameAttachment}
            />
          </div>

          <div className="flex justify-end items-center pt-6 border-t border-gray-200 mt-8">
            <button
              type="button"
              onClick={() => navigate(-1)}
              className="mr-4 px-6 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-6 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition disabled:bg-blue-300"
              disabled={loading}
            >
              {loading ? 'Updating...' : 'Update Task'}
            </button>
          </div>
        </form>
      </div>
    </DashboardLayout>
  );
};

export default EditTask;
