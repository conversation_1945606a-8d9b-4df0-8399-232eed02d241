import React from 'react';

// Imports for logo files
import googleLogo from '../../../assets/logos/Google.png'; 
import linkedinLogo from '../../../assets/logos/Linkedin.svg';
import twitterLogo from '../../../assets/logos/Twitter.svg';
import metaLogo from '../../../assets/logos/Meta.svg';
import microsoftLogo from '../../../assets/logos/Microsoft.png';

/**
 * Partners section component for the landing page
 * Displays logos of partner companies in their actual colors.
 * @returns {JSX.Element} - Rendered component
 */
const Partners = () => {
  // Logo size configurations for better visual balance
  // Using slightly desaturated logos with transparency to blend better
  const logoSizes = {
    google: "h-8 md:h-9",
    linkedin: "h-7 md:h-8",
    twitter: "h-10 md:h-11", // Increased to match Google/Microsoft
    meta: "h-16 md:h-17", // Increased to match Google/Microsoft
    microsoft: "h-8 md:h-9",
  };

  return (
    <section className="py-12 sm:py-16 bg-white">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8" data-aos="fade-up" data-aos-duration="700">
        <div className="flex flex-wrap justify-center items-center gap-x-12 gap-y-8 sm:gap-x-16 md:gap-x-20 lg:gap-x-24">
          {/* Google */}
          <a href="#" className="flex items-center justify-center" aria-label="Google">
            <img src={googleLogo} alt="Google Logo" className={`${logoSizes.google} w-auto object-contain`} />
          </a>
          
          {/* LinkedIn */}
          <a href="#" className="flex items-center justify-center" aria-label="LinkedIn">
            <img src={linkedinLogo} alt="LinkedIn Logo" className={`${logoSizes.linkedin} w-auto object-contain`} />
          </a>
          
          {/* Twitter */}
          <a href="#" className="flex items-center justify-center" aria-label="Twitter">
            <img src={twitterLogo} alt="Twitter Logo" className={`${logoSizes.twitter} w-auto object-contain`} />
          </a>
          
          {/* Meta */}
          <a href="#" className="flex items-center justify-center" aria-label="Meta">
            <img src={metaLogo} alt="Meta Logo" className={`${logoSizes.meta} w-auto object-contain`} />
          </a>
          
          {/* Microsoft */}
          <a href="#" className="flex items-center justify-center" aria-label="Microsoft">
            <img src={microsoftLogo} alt="Microsoft Logo" className={`${logoSizes.microsoft} w-auto object-contain`} />
          </a>
        </div>
      </div>
    </section>
  );
};

export default Partners;
