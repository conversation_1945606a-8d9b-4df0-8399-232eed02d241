import React, { useState } from 'react';
import { useNotifications } from '../../contexts/NotificationContext';
import { useSocket } from '../../contexts/SocketContext';
import axiosInstance from '../../utils/axiosInstance';

const NotificationDebug = () => {
  const { 
    notifications, 
    unreadCount, 
    loading, 
    fetchNotifications 
  } = useNotifications();
  
  const { connected } = useSocket();
  const [testResult, setTestResult] = useState('');

  const createTestTask = async () => {
    try {
      setTestResult('Creating test task...');
      const response = await axiosInstance.post('/api/tasks', {
        title: 'Test Notification Task - ' + new Date().toLocaleTimeString(),
        description: 'This is a test task to verify notifications',
        priority: 'medium',
        dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
        assignedTo: ['68495a4be3fd9de62d8a3ac'] // Test user ID
      });
      
      if (response.data) {
        setTestResult(`✅ Task created: ${response.data.task._id}`);
        // Refresh notifications
        setTimeout(() => fetchNotifications(), 1000);
      }
    } catch (error) {
      setTestResult(`❌ Error: ${error.response?.data?.message || error.message}`);
    }
  };

  const checkNotifications = async () => {
    try {
      setTestResult('Checking notifications...');
      const response = await axiosInstance.get('/api/notifications');
      setTestResult(`✅ Found ${response.data.notifications?.length || 0} notifications, ${response.data.unreadCount || 0} unread`);
    } catch (error) {
      setTestResult(`❌ Error: ${error.response?.data?.message || error.message}`);
    }
  };

  return (
    <div className="fixed bottom-4 left-4 bg-white border border-gray-300 rounded-lg p-4 shadow-lg max-w-sm z-50">
      <h3 className="font-bold text-sm mb-2">Notification Debug</h3>
      
      <div className="text-xs space-y-1 mb-3">
        <div><strong>Socket Connected:</strong> {connected ? '✅' : '❌'}</div>
        <div><strong>Notifications:</strong> {notifications.length}</div>
        <div><strong>Unread Count:</strong> {unreadCount}</div>
        <div><strong>Loading:</strong> {loading ? 'Yes' : 'No'}</div>
      </div>
      
      <div className="space-y-2 mb-3">
        <button 
          onClick={createTestTask}
          className="w-full text-xs bg-blue-500 text-white px-2 py-1 rounded"
        >
          Create Test Task
        </button>
        <button 
          onClick={checkNotifications}
          className="w-full text-xs bg-green-500 text-white px-2 py-1 rounded"
        >
          Check Notifications
        </button>
        <button 
          onClick={fetchNotifications}
          className="w-full text-xs bg-purple-500 text-white px-2 py-1 rounded"
        >
          Refresh Notifications
        </button>
      </div>
      
      {testResult && (
        <div className="text-xs p-2 bg-gray-100 rounded">
          {testResult}
        </div>
      )}
      
      {notifications.length > 0 && (
        <div className="mt-2">
          <div className="text-xs font-bold">Recent:</div>
          {notifications.slice(0, 2).map(notif => (
            <div key={notif._id} className="text-xs text-gray-600 truncate">
              {notif.type}: {notif.title}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default NotificationDebug;
