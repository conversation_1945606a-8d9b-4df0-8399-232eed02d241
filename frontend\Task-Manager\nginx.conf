server {
    listen 80;
    server_name localhost;

    # Serve the built front-end files
    root /usr/share/nginx/html;
    index index.html;

    # All front-end routes should be handled by the SPA
    location / {
        try_files $uri $uri/ /index.html;
    }

    # Forward all API requests to the backend container
    location /api/ {
        proxy_pass http://backend:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Forward socket.io (WebSocket) traffic to the backend container
    location /socket.io/ {
        proxy_pass http://backend:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
    }
}
