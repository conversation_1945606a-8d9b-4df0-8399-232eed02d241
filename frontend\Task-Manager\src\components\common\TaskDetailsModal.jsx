import React from 'react';
import { LuX } from 'react-icons/lu';
import moment from 'moment';
import CommentSection from '../comments/CommentSection';
import { getStatusBadgeColor, getPriorityBadgeColor } from '../../utils/uiHelpers';

const TaskDetailsModal = ({ isOpen, onClose, task }) => {
  if (!isOpen || !task) {
    return null;
  }

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center p-4"
      onClick={onClose}
    >
      <div
        className="bg-white rounded-lg shadow-xl w-full max-w-3xl max-h-[90vh] flex flex-col"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="flex justify-between items-center p-4 border-b sticky top-0 bg-white z-10">
          <h2 className="text-2xl font-bold text-gray-800 truncate pr-4">{task.title}</h2>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-800">
            <LuX size={24} />
          </button>
        </div>

        <div className="p-6 overflow-y-auto">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <div>
              <p className="text-sm font-medium text-gray-500 mb-1">Status</p>
              <span className={`px-3 py-1 text-sm rounded-full font-medium ${getStatusBadgeColor(task.status)}`}>
                {task.status}
              </span>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500 mb-1">Priority</p>
              <span className={`px-3 py-1 text-sm rounded-full font-medium ${getPriorityBadgeColor(task.priority)}`}>
                {task.priority}
              </span>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500 mb-1">Created At</p>
              <p className="text-gray-800 font-medium">{moment(task.createdAt).format('MMMM Do YYYY')}</p>
            </div>
          </div>

          <CommentSection taskId={task._id} />
        </div>
      </div>
    </div>
  );
};

export default TaskDetailsModal;
