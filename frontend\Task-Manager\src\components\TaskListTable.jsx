import React, { useState, useEffect, useMemo, useCallback } from "react";
import PropTypes from "prop-types";
import { LuTrash2, Lu<PERSON><PERSON>clip } from "react-icons/lu";
import moment from "moment";
import ConfirmationModal from "./common/ConfirmationModal";

import axiosInstance from "../utils/axiosInstance";
import { API_PATHS } from "../utils/apiPaths";
import toast from "react-hot-toast";
import { getStatusBadgeColor, getPriorityBadgeColor } from "../utils/uiHelpers";
import AvatarGroup from "./AvatarGroup";

const TaskListTable = ({ tableData = null, onTaskDeleted, onSelectionChange, onTaskClick, selectedTaskId = null }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [taskToDelete, setTaskToDelete] = useState(null);
  const [selectedTaskIds, setSelectedTaskIds] = useState(new Set());

  const displayData = useMemo(() => {
    if (tableData !== null) return tableData;
    return [];
  }, [tableData]);

  const allSelected = useMemo(() => {
    return displayData.length > 0 && selectedTaskIds.size === displayData.length;
  }, [displayData.length, selectedTaskIds.size]);

  useEffect(() => {
    if (onSelectionChange) {
      onSelectionChange(Array.from(selectedTaskIds));
    }
  }, [selectedTaskIds, onSelectionChange]);

  const handleSelectAll = useCallback((e) => {
    const newSelectedTaskIds = new Set();
    if (e.target.checked) {
      displayData.forEach(task => newSelectedTaskIds.add(task._id));
    }
    setSelectedTaskIds(newSelectedTaskIds);
  }, [displayData]);

  const handleSelectRow = useCallback((taskId) => {
    setSelectedTaskIds(prevSelected => {
      const newSelected = new Set(prevSelected);
      if (newSelected.has(taskId)) {
        newSelected.delete(taskId);
      } else {
        newSelected.add(taskId);
      }
      return newSelected;
    });
  }, []);

  const openDeleteModal = useCallback((task, e) => {
    e.stopPropagation();
    setTaskToDelete(task);
    setIsModalOpen(true);
  }, []);

  const handleRowClick = useCallback((task) => {
    if (onTaskClick) {
      // Normalize task data before passing it to parent
      const normalizedTask = {
        ...task,
        // Combine todos from all sources
        todoChecklist: [
          ...(task.todoChecklist || []),
          ...(task.todoCheckList || []),
          ...(task.subTasks || []).map(st => ({
            text: st.title || st.text || st.name,
            completed: st.completed || st.isCompleted || st.status === 'completed'
          }))
        ],
        // Ensure assignedTo is an array
        assignedTo: Array.isArray(task.assignedTo) 
          ? task.assignedTo 
          : Array.isArray(task.team) 
            ? task.team 
            : [],
        // Ensure consistent status format
        status: task.status?.toLowerCase() === 'todo' 
          ? 'Pending' 
          : task.status?.toLowerCase() === 'in-progress' 
            ? 'In Progress' 
            : task.status
      };
      
      console.log('Debug - Normalized task data:', normalizedTask);
      onTaskClick(normalizedTask);
    }
  }, [onTaskClick]);

  const closeDeleteModal = useCallback(() => {
    setIsModalOpen(false);
    setTaskToDelete(null);
  }, []);

  const handleConfirmDelete = useCallback(async () => {
    if (!taskToDelete) return;
    
    try {
      await axiosInstance.delete(API_PATHS.TASKS.DELETE_TASK(taskToDelete._id));
      toast.success("Task deleted successfully");
      
      if (selectedTaskIds.has(taskToDelete._id)) {
        setSelectedTaskIds(prev => {
          const newSelected = new Set(prev);
          newSelected.delete(taskToDelete._id);
          return newSelected;
        });
      }
      
      if (onTaskDeleted) onTaskDeleted(taskToDelete._id);
    } catch (error) {
      toast.error("Failed to delete task");
      console.error("Delete error:", error);
    }
    
    closeDeleteModal();
  }, [taskToDelete, onTaskDeleted, selectedTaskIds, closeDeleteModal]);

  if (Array.isArray(displayData) && displayData.length === 0) {
    return (
      <div className="text-center py-8 bg-gray-50 rounded-lg">
        <p className="text-gray-500">No tasks available</p>
      </div>
    );
  }

  return (
    <>
      <div className="space-y-6">
        <div className="overflow-x-auto rounded-lg shadow">
          <table className="min-w-full bg-white" role="table">
            <thead>
              <tr className="bg-gray-50 border-b">
                <th scope="col" className="py-3 px-6 text-left">
                  <input 
                    type="checkbox" 
                    onChange={handleSelectAll} 
                    checked={allSelected} 
                    className="form-checkbox h-4 w-4 text-blue-600 transition duration-150 ease-in-out" 
                  />
                </th>
                <th scope="col" className="py-3 px-6 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Name</th>
                <th scope="col" className="py-3 px-6 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Status</th>
                <th scope="col" className="py-3 px-6 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Priority</th>
                <th scope="col" className="py-3 px-6 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Assigned To</th>
                <th scope="col" className="py-3 px-6 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Created On</th>
                <th scope="col" className="py-3 px-6 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Attachment</th>
                <th scope="col" className="py-3 px-6 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {displayData.map((task) => (
                <React.Fragment key={task._id}>
                  <tr 
                    onClick={() => handleRowClick(task)} 
                    className={`hover:bg-gray-50 transition-colors cursor-pointer ${
                      selectedTaskId === task._id ? 'bg-blue-50 ring-2 ring-blue-400' : ''
                    }`}
                  >
                    <td className="py-4 px-6 text-left">
                      <input 
                        type="checkbox" 
                        checked={selectedTaskIds.has(task._id)} 
                        onChange={() => handleSelectRow(task._id)} 
                        className="form-checkbox h-4 w-4 text-blue-600 transition duration-150 ease-in-out"
                        onClick={e => e.stopPropagation()} 
                      />
                    </td>
                    <td className="py-4 px-6">{task.title}</td>
                    <td className="py-4 px-6">
                      <span className={`px-3 py-1 text-xs rounded-full font-medium ${getStatusBadgeColor(task.status)}`}>
                        {task.status}
                      </span>
                    </td>
                    <td className="py-4 px-6">
                      <span className={`px-3 py-1 text-xs rounded-full font-medium ${getPriorityBadgeColor(task.priority)}`}>
                        {task.priority}
                      </span>
                    </td>
                    <td className="py-4 px-6">
                      {Array.isArray(task.assignedTo || task.team) && (task.assignedTo || task.team).length > 0 ? (
                        <AvatarGroup avatars={task.assignedTo || task.team} size="sm" />
                      ) : (
                        <span className="text-gray-400 text-xs">Unassigned</span>
                      )}
                    </td>
                    <td className="py-4 px-6">
                      {task.createdAt ? moment(task.createdAt).format("Do MMM YYYY") : "N/A"}
                    </td>
                    <td className="py-4 px-6">
                      <span className="flex items-center gap-1 text-gray-700">
                        <LuPaperclip className="inline-block h-5 w-5 text-blue-500" />
                        {Array.isArray(task.attachments) ? task.attachments.length : 0}
                      </span>
                    </td>
                    <td className="py-4 px-6">
                      <button
                        className="text-red-500 hover:text-red-700"
                        onClick={(e) => openDeleteModal(task, e)}
                        aria-label="Delete Task"
                      >
                        <LuTrash2 size={16} />
                      </button>
                    </td>
                  </tr>
                </React.Fragment>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      <ConfirmationModal
        isOpen={isModalOpen}
        onClose={closeDeleteModal}
        onConfirm={handleConfirmDelete}
        title="Confirm Deletion"
        message={`Are you sure you want to delete the task "${taskToDelete?.title}"? This action cannot be undone.`}
      />
    </>
  );
};

TaskListTable.propTypes = {
  tableData: PropTypes.arrayOf(
    PropTypes.shape({
      _id: PropTypes.string.isRequired,
      title: PropTypes.string.isRequired,
      status: PropTypes.oneOf(["Completed", "Pending", "In Progress"]).isRequired,
      priority: PropTypes.oneOf(["high", "medium", "low"]).isRequired,
      createdAt: PropTypes.string
    })
  ),
  onTaskDeleted: PropTypes.func,
  onSelectionChange: PropTypes.func,
  onTaskClick: PropTypes.func,
  selectedTaskId: PropTypes.string
};

export default TaskListTable;
