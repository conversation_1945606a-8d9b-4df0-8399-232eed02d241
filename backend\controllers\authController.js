const User = require('../models/User');
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const tokenService = require('../services/tokenService');

// Generate JWT tokens (access and refresh)
const generateTokens = async (userId) => {
  return tokenService.generateTokens(userId);
};

// @desc    Register a new user
// @route   POST /api/auth/register
// @access  Public
const register = async (req, res) => {
  try {
    const { name, email, password, profileImageUrl, adminInviteToken } = req.body;

    // Normalize profileImageUrl to a relative path so that it stays valid
    // even if the backend host/port changes in different environments.
    let normalizedProfileImageUrl = null;
    if (profileImageUrl) {
      try {
        // Extract filename from any absolute/relative URL that contains "/uploads/"
        const match = profileImageUrl.match(/\/uploads\/([^?#]+)/);
        if (match && match[1]) {
          normalizedProfileImageUrl = `/uploads/${match[1]}`;
        } else {
          // If uploads segment not found, keep the original string
          normalizedProfileImageUrl = profileImageUrl;
        }
      } catch (e) {
        // Fallback to original if regex fails
        normalizedProfileImageUrl = profileImageUrl;
      }
    }
    
    // Add strong password validation
    const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]{8,}$/;
    if (!passwordRegex.test(password)) {
      return res.status(400).json({
        message: 'Password is not strong enough. It must be at least 8 characters long and contain at least one uppercase letter, one lowercase letter, and one number.',
      });
    }

    // Check if user already exists
    const userExists = await User.findOne({ email }).lean();
    
    if (userExists) {
      return res.status(400).json({ message: "User already exists" });
    }
    
    // Determine user role: admin if correct token is provided, otherwise member
    let role;
    if (adminInviteToken) {
      const adminInviteTokenEnv = process.env.ADMIN_INVITE_TOKEN;
      role = adminInviteToken === adminInviteTokenEnv ? "admin" : "member";
    } else {
      role = "member";
    }
    
    // Hash password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);

    // Create new user
    const user = await User.create({
      name,
      email,
      password: hashedPassword,
      profileImageUrl: normalizedProfileImageUrl,
      role,
      refreshTokens: []
    });

    // Generate tokens
    const { accessToken, refreshToken } = await generateTokens(user._id);

    // Add refresh token to user's refreshTokens array
    user.refreshTokens.push(refreshToken);
    await user.save();

    // Set tokens as httpOnly cookies
    res.cookie('refreshToken', refreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'strict',
      maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
    });
    res.cookie('accessToken', accessToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
      maxAge: 15 * 60 * 1000, // 15 minutes
    });

    // Return user data and access token (for SPA localStorage)
    res.status(201).json({
      id: user._id,
      name: user.name,
      email: user.email,
      role: user.role,
      profileImageUrl: user.profileImageUrl,
      token: accessToken,
    });
  } catch (error) {
    
    res.status(500).json({ message: "Server error", error: error.message });
  }
};

// @desc    Login user
// @route   POST /api/auth/login
// @access  Public
const login = async (req, res) => {
  try {
    const { email, password } = req.body;
    
    const user = await User.findOne({ email });
    if (!user) {
      return res.status(401).json({ message: "Invalid email or password" });
    }

    // Compare password
    const isMatch = await bcrypt.compare(password, user.password);
    if (!isMatch) {
      return res.status(401).json({ message: "Invalid email or password" });
    }

    // Generate tokens
    const { accessToken, refreshToken } = await generateTokens(user._id);

    // Add refresh token to user's refreshTokens array
    user.refreshTokens.push(refreshToken);
    await user.save();

    // Set tokens as httpOnly cookies
    res.cookie('refreshToken', refreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'strict',
      maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
    });
    res.cookie('accessToken', accessToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
      maxAge: 15 * 60 * 1000, // 15 minutes
    });

    // Return user data and access token (for SPA localStorage)
    res.json({
      id: user._id,
      name: user.name,
      email: user.email,
      role: user.role,
      profileImageUrl: user.profileImageUrl,
      token: accessToken,
    });
  } catch (error) {
    
    return res.status(500).json({ message: "Server error", error: error.message });
  }
};

// @desc    Get user profile
// @route   GET /api/auth/profile
// @access  Private (Requires JWT)
const getUserProfile = async (req, res) => {
  try {
    // Handle both id and _id formats from different middleware
    const userId = req.user._id || req.user.id;
    const user = await User.findById(userId).select('-password').lean();
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }
    
    // The 'user' object is already a plain JavaScript object because of .lean()

    // Process profile image URL to ensure it's a full URL if it's a relative path
    if (user.profileImageUrl && user.profileImageUrl.startsWith('/uploads/')) {
      // Construct an absolute URL using the server's own URL
      const baseUrl = `${req.protocol}://${req.get('host')}`;
      user.profileImageUrl = `${baseUrl}${user.profileImageUrl}`;
    }

    res.json(user);
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// @desc    Update user profile
// @route   PUT /api/auth/profile
// @access  Private (Requires JWT)
const updateUserProfile = async (req, res) => {
  try {
    // Handle both id and _id formats from different middleware
    const userId = req.user._id || req.user.id;
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }
    
    // Update basic fields
    user.name = req.body.name || user.name;
    user.email = req.body.email || user.email;
    
    // Handle profile image URL updates with proper formatting
    // Check if profileImageUrl is provided and is a string
    if (typeof req.body.profileImageUrl === 'string') {
      const fs = require('fs');
      const path = require('path');
      const oldProfileImageUrl = user.profileImageUrl;
      
      // If it's a URL that includes our uploads path
      if (typeof req.body.profileImageUrl === 'string' && req.body.profileImageUrl.includes('/uploads/')) {
        // Extract just the filename to store a cleaner URL
        const uploadMatch = req.body.profileImageUrl.match(/\/uploads\/([^?#]+)/);
        if (uploadMatch && uploadMatch[1]) {
          const newFilename = uploadMatch[1];
          
          // Store as a relative path to prevent issues with domain changes
          user.profileImageUrl = `/uploads/${newFilename}`;
          
          // If old image exists and is different from new one, delete it
          if (oldProfileImageUrl) {
            try {
              // Extract old filename
              const oldImageMatch = oldProfileImageUrl.match(/\/uploads\/([^?#]+)/);
              if (oldImageMatch && oldImageMatch[1]) {
                const oldFilename = decodeURIComponent(oldImageMatch[1]);
                
                // Only delete if the filenames are different
                if (oldFilename !== newFilename) {
                  const oldFilePath = path.join(__dirname, '..', 'uploads', oldFilename);

                  // Check if file exists and delete it
                  if (fs.existsSync(oldFilePath)) {
                    fs.unlinkSync(oldFilePath);
                  }
                }
              }
            } catch (deleteError) {
              // Continue with update even if deletion fails
            }
          }
        } else {
          user.profileImageUrl = req.body.profileImageUrl;
        }
    } else if (req.body.profileImageUrl === '' || req.body.profileImageUrl === null) { // This handles explicit removal or null value
        // If profile image is being removed, delete the old file
        if (oldProfileImageUrl) {
          try {
            // Extract old filename
            const oldImageMatch = oldProfileImageUrl.match(/\/uploads\/([^?#]+)/);
            if (oldImageMatch && oldImageMatch[1]) {
              const oldFilename = decodeURIComponent(oldImageMatch[1]);
              const oldFilePath = path.join(__dirname, '..', 'uploads', oldFilename);
              
              // Check if file exists and delete it
              if (fs.existsSync(oldFilePath)) {
                fs.unlinkSync(oldFilePath);
              }
            }
          } catch (deleteError) {
            // Continue with update even if deletion fails
          }
        }
        // Set to empty string
        user.profileImageUrl = '';
      } else {
        // For other URL types, store as-is
        user.profileImageUrl = req.body.profileImageUrl;
      }
    }
    
    let newAccessToken;

    // Handle password updates
    if (req.body.password) {
      const salt = await bcrypt.genSalt(10);
      user.password = await bcrypt.hash(req.body.password, salt);

      // Invalidate all old refresh tokens
      user.refreshTokens = [];

      // Issue a new set of tokens for the current session
      const { accessToken, refreshToken } = await generateTokens(user._id);
      user.refreshTokens.push(refreshToken);
      newAccessToken = accessToken;

      // Set the new refresh token in the cookie
      res.cookie('refreshToken', refreshToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'strict',
        maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
      });

      // Set the new access token in the cookie
      res.cookie('accessToken', newAccessToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
        maxAge: 15 * 60 * 1000, // 15 minutes
      });
    }
    
    // Save the updated user
    const updatedUser = await user.save();
    
    // Construct full image URL for the response if it's a relative path
    let profileImageUrl = updatedUser.profileImageUrl;
    if (profileImageUrl && profileImageUrl.startsWith('/uploads/')) {
      // Construct an absolute URL using the server's own URL
      const baseUrl = `${req.protocol}://${req.get('host')}`;
      profileImageUrl = `${baseUrl}${profileImageUrl}`;
    }
    
    // Send the response with complete user data
    const responsePayload = {
      _id: updatedUser._id,
      name: updatedUser.name,
      email: updatedUser.email,
      role: updatedUser.role,
      profileImageUrl, // Use the processed URL
    };

    // If a new access token was generated, include it in the response
    if (newAccessToken) {
      responsePayload.accessToken = newAccessToken;
    }

    res.json(responsePayload);
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// @desc    Logout user
// @route   POST /api/auth/logout
// @access  Public
const logout = async (req, res) => {
  const { refreshToken } = req.cookies;

  // Define cookie options based on environment
  const cookieOptions = {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
  };

  // Clear both access and refresh tokens
  res.clearCookie('refreshToken', {
    ...cookieOptions,
    sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'strict',
  });
  res.clearCookie('accessToken', {
    ...cookieOptions,
    sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
  });

  if (!refreshToken) {
    // If there's no token, we can't do anything on the backend,
    // but we've cleared the cookies, so the client is logged out.
    return res.sendStatus(204); // No content
  }

  try {
    // Find user by refresh token and remove it from the database to invalidate it
    await User.findOneAndUpdate(
      { refreshTokens: refreshToken },
      { $pull: { refreshTokens: refreshToken } }
    );

    return res.status(200).json({ message: 'Logged out successfully' });
  } catch (error) {
    res.status(500).json({ message: 'Server error during logout', error: error.message });
  }
};

module.exports = {
  getUserProfile,
  register,
  login,
  updateUserProfile,
  logout
}