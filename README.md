Task Manager fullstack project (frontend + backend)

# Project Manager

A full-stack project management application for teams and individuals. This project includes a robust backend (Node.js/Express/MongoDB) and a modern frontend (React).

## Features
- User authentication (JWT, refresh tokens)
- Task management (CRUD, priorities, status, attachments)
- Real-time notifications and chat
- User roles and permissions (admin, user)
- Dashboard with charts and statistics
- Bulk actions (delete, assign, etc.)
- File uploads and secure handling
- Security best practices (rate limiting, input validation, CSRF protection)
- Responsive UI with modern design

## Tech Stack
- **Frontend:** React, Tailwind CSS, React Router, Axios
- **Backend:** Node.js, Express, MongoDB, Mongoose
- **Real-time:** Socket.io
- **Testing:** Jest, Supertest

## Getting Started

### Prerequisites
- Node.js (v16+ recommended)
- MongoDB

### Backend Setup
```bash
cd backend
npm install
cp .env.example .env # Edit .env with your MongoDB URI and secrets
npm start
```

### Frontend Setup
```bash
cd frontend/Task-Manager
npm install
cp .env.example .env # Edit .env for API base URL if needed
npm run dev
```

### Running with Docker
To run the entire application with Docker, ensure you have Docker and Docker Compose installed. Then, from the root of the project, run:
```bash
docker-compose up --build
```
This command handles both the frontend and backend:
-   **Backend**: The `backend` service is built and run in a dedicated container.
-   **Frontend**: The `nginx` service now builds the frontend and serves its static files. It also acts as a reverse proxy for the backend. This is an optimized setup that reduces the number of running containers.

The application will be available at `http://localhost`.

To stop the application, press `Ctrl+C` in the terminal, and then run:
```bash
docker-compose down
```

### Running Tests
- Backend: `npm test` (from backend directory)
- Frontend: `npm test` (from frontend/Task-Manager directory)

## Folder Structure
- `backend/` - Node.js/Express API
- `frontend/Task-Manager/` - React frontend

## Contributing
Pull requests are welcome! For major changes, please open an issue first to discuss what you would like to change.

## License
This project is licensed under the MIT License.

---

**Developed by AJAY VISHNU.M**
