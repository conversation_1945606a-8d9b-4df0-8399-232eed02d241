import React from 'react';
import { Box, Tooltip } from '@mui/material';
import { 
  Check as CheckIcon, 
  DoneAll as DoneAllIcon,
  Schedule as ScheduleIcon,
  Error as ErrorIcon 
} from '@mui/icons-material';

/**
 * MessageStatus Component
 * 
 * Displays visual indicators for message delivery and read status:
 * - Sending: Clock icon (gray)
 * - Sent: Single checkmark (gray) 
 * - Delivered: Double checkmark (gray)
 * - Read: Double checkmark (blue/green)
 * - Failed: Error icon (red)
 */
const MessageStatus = ({ message, conversation, isOwnMessage }) => {
  // Don't show status for messages from others
  if (!isOwnMessage) {
    return null;
  }

  // Determine message status
  const getMessageStatus = () => {
    // Check if message failed to send
    if (message.failed) {
      return 'failed';
    }

    // Check if message is still sending
    if (message.sending || message.pending) {
      return 'sending';
    }

    // Check if message has been read by others
    if (message.readBy && message.readBy.length > 1) {
      // For individual chats, check if the other participant has read it
      if (conversation?.type === 'individual') {
        const otherParticipant = conversation.participants?.find(
          p => p._id !== message.sender._id && p._id !== message.sender
        );

        if (otherParticipant) {
          const hasRead = message.readBy.some(
            readEntry => readEntry.user === otherParticipant._id ||
                        readEntry.user?._id === otherParticipant._id
          );
          return hasRead ? 'read' : 'delivered';
        }
      }

      // For group chats, if anyone else has read it
      const othersWhoRead = message.readBy.filter(
        readEntry => readEntry.user !== message.sender._id &&
                    readEntry.user !== message.sender &&
                    readEntry.user?._id !== message.sender._id &&
                    readEntry.user?._id !== message.sender
      );

      return othersWhoRead.length > 0 ? 'read' : 'delivered';
    }

    // Check if message has been delivered
    if (message.deliveredTo && message.deliveredTo.length > 0) {
      return 'delivered';
    }

    // Message has been sent but not delivered
    return message._id && !message._id.startsWith('temp-') ? 'sent' : 'sending';
  };

  const status = getMessageStatus();

  // Get status icon and color
  const getStatusDisplay = () => {
    const baseStyle = { fontSize: 14 };
    
    switch (status) {
      case 'sending':
        return {
          icon: <ScheduleIcon sx={{ ...baseStyle, color: 'rgba(255, 255, 255, 0.5)' }} />,
          tooltip: 'Sending...',
          color: 'rgba(255, 255, 255, 0.5)'
        };
        
      case 'sent':
        return {
          icon: <CheckIcon sx={{ ...baseStyle, color: 'rgba(255, 255, 255, 0.7)' }} />,
          tooltip: 'Sent',
          color: 'rgba(255, 255, 255, 0.7)'
        };
        
      case 'delivered':
        return {
          icon: <DoneAllIcon sx={{ ...baseStyle, color: 'rgba(255, 255, 255, 0.7)' }} />,
          tooltip: 'Delivered',
          color: 'rgba(255, 255, 255, 0.7)'
        };
        
      case 'read':
        return {
          icon: <DoneAllIcon sx={{ ...baseStyle, color: '#4FC3F7' }} />,
          tooltip: 'Read',
          color: '#4FC3F7'
        };
        
      case 'failed':
        return {
          icon: <ErrorIcon sx={{ ...baseStyle, color: '#f44336' }} />,
          tooltip: 'Failed to send',
          color: '#f44336'
        };
        
      default:
        return {
          icon: <CheckIcon sx={{ ...baseStyle, color: 'rgba(255, 255, 255, 0.7)' }} />,
          tooltip: 'Sent',
          color: 'rgba(255, 255, 255, 0.7)'
        };
    }
  };

  const statusDisplay = getStatusDisplay();

  return (
    <Tooltip title={statusDisplay.tooltip} arrow placement="top">
      <Box 
        sx={{ 
          display: 'flex', 
          alignItems: 'center',
          ml: 0.5,
          cursor: 'default'
        }}
      >
        {statusDisplay.icon}
      </Box>
    </Tooltip>
  );
};

export default MessageStatus;
