<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Authentication Flow Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🔐 Authentication Persistence Test</h1>
    <p>This test simulates the authentication flow that happens on page refresh.</p>
    
    <div class="test-section">
        <h3>Step 1: Login</h3>
        <button onclick="testLogin()">Test Login</button>
        <div id="login-result"></div>
    </div>
    
    <div class="test-section">
        <h3>Step 2: Get Profile (Page Refresh Simulation)</h3>
        <button onclick="testProfile()">Test Profile Fetch</button>
        <div id="profile-result"></div>
    </div>
    
    <div class="test-section">
        <h3>Step 3: Token Storage</h3>
        <button onclick="checkTokenStorage()">Check Token Storage</button>
        <div id="storage-result"></div>
    </div>
    
    <div class="test-section">
        <h3>Step 4: Clear Storage (Logout Simulation)</h3>
        <button onclick="clearStorage()">Clear Storage</button>
        <div id="clear-result"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3000';
        let currentToken = null;

        async function testLogin() {
            const resultDiv = document.getElementById('login-result');
            resultDiv.innerHTML = '<p>🔄 Testing login...</p>';
            
            try {
                const response = await fetch(`${API_BASE}/api/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'testpassword123'
                    })
                });
                
                const data = await response.json();
                
                if (response.ok && data.token) {
                    currentToken = data.token;
                    localStorage.setItem('token', data.token);
                    localStorage.setItem('user', JSON.stringify({
                        _id: data.id,
                        id: data.id,
                        name: data.name,
                        email: data.email,
                        role: data.role
                    }));
                    
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ Login Successful</h4>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    throw new Error(data.message || 'Login failed');
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Login Failed</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        async function testProfile() {
            const resultDiv = document.getElementById('profile-result');
            const token = localStorage.getItem('token') || currentToken;
            
            if (!token) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ No Token Found</h4>
                        <p>Please login first</p>
                    </div>
                `;
                return;
            }
            
            resultDiv.innerHTML = '<p>🔄 Testing profile fetch...</p>';
            
            try {
                const response = await fetch(`${API_BASE}/api/auth/profile`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ Profile Fetch Successful</h4>
                            <p>This simulates what happens on page refresh</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    throw new Error(data.message || 'Profile fetch failed');
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Profile Fetch Failed</h4>
                        <p>${error.message}</p>
                        <p>This would cause redirect to login page</p>
                    </div>
                `;
            }
        }

        function checkTokenStorage() {
            const resultDiv = document.getElementById('storage-result');
            const token = localStorage.getItem('token');
            const user = localStorage.getItem('user');
            
            if (token && user) {
                resultDiv.innerHTML = `
                    <div class="success">
                        <h4>✅ Token and User Data Found</h4>
                        <p><strong>Token:</strong> ${token.substring(0, 20)}...</p>
                        <p><strong>User:</strong></p>
                        <pre>${JSON.stringify(JSON.parse(user), null, 2)}</pre>
                    </div>
                `;
            } else {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Missing Storage Data</h4>
                        <p>Token: ${token ? 'Found' : 'Missing'}</p>
                        <p>User: ${user ? 'Found' : 'Missing'}</p>
                    </div>
                `;
            }
        }

        function clearStorage() {
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            currentToken = null;
            
            document.getElementById('clear-result').innerHTML = `
                <div class="info">
                    <h4>🧹 Storage Cleared</h4>
                    <p>This simulates logout</p>
                </div>
            `;
        }

        // Auto-check storage on page load
        window.onload = function() {
            checkTokenStorage();
        };
    </script>
</body>
</html>
