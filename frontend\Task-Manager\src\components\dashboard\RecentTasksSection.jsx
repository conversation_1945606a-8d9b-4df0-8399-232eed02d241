/**
 * Recent Tasks Section Component
 * Displays recent tasks in a table format
 * Extracted from Dashboard.jsx for better maintainability
 */

import React from 'react';
import TaskListTable from '../TaskListTable';

const RecentTasksSection = ({ 
  dashboardData, 
  loading = false, 
  onTaskDeleted,
  onSelectionChange 
}) => {
  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="h-6 bg-gray-200 rounded w-1/4 mb-4 animate-pulse" />
        <div className="space-y-3">
          {Array.from({ length: 5 }).map((_, index) => (
            <div key={index} className="animate-pulse">
              <div className="flex space-x-4">
                <div className="h-4 bg-gray-200 rounded flex-1" />
                <div className="h-4 bg-gray-200 rounded w-20" />
                <div className="h-4 bg-gray-200 rounded w-16" />
                <div className="h-4 bg-gray-200 rounded w-24" />
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  const recentTasks = dashboardData?.recentTasks || [];

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-800">
            Recent Tasks
          </h3>
          <span className="text-sm text-gray-500">
            {recentTasks.length} task{recentTasks.length !== 1 ? 's' : ''}
          </span>
        </div>
      </div>
      
      <div className="p-6">
        {recentTasks.length > 0 ? (
          <TaskListTable
            tableData={recentTasks}
            onTaskDeleted={onTaskDeleted}
            onSelectionChange={onSelectionChange}
            showPagination={false}
            maxRows={10}
          />
        ) : (
          <div className="text-center py-12">
            <div className="text-4xl mb-4">📝</div>
            <h4 className="text-lg font-medium text-gray-700 mb-2">
              No Recent Tasks
            </h4>
            <p className="text-gray-500">
              Recent tasks will appear here once they are created.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default RecentTasksSection;
