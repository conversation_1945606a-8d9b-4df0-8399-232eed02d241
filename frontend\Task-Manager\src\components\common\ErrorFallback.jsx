import React from 'react';
import { LuInfo, LuRefreshCw, LuHouse, LuArrowLeft } from 'react-icons/lu';

/**
 * Reusable Error Fallback Component
 * Eliminates duplicate error handling UI across components
 */

/**
 * Generic error display component
 */
const ErrorFallback = ({ 
  error,
  resetError,
  title = "Something went wrong",
  message = "An unexpected error occurred. Please try again.",
  showDetails = false,
  showRetry = true,
  showHome = false,
  showBack = false,
  onHome,
  onBack,
  className = "",
  variant = "default" // default, minimal, card, page
}) => {
  const getVariantClasses = () => {
    switch (variant) {
      case "minimal":
        return "text-center py-8";
      case "card":
        return "bg-white p-6 rounded-lg shadow-sm border border-red-200";
      case "page":
        return "min-h-screen flex items-center justify-center bg-gray-50";
      default:
        return "bg-red-50 border border-red-200 rounded-lg p-6";
    }
  };

  const getIconClasses = () => {
    switch (variant) {
      case "minimal":
        return "w-8 h-8 text-red-500 mx-auto mb-3";
      case "page":
        return "w-16 h-16 text-red-500 mx-auto mb-6";
      default:
        return "w-12 h-12 text-red-500 mx-auto mb-4";
    }
  };

  const getTitleClasses = () => {
    switch (variant) {
      case "minimal":
        return "text-lg font-medium text-gray-900 mb-2";
      case "page":
        return "text-3xl font-bold text-gray-900 mb-4";
      default:
        return "text-xl font-semibold text-red-800 mb-3";
    }
  };

  const getMessageClasses = () => {
    switch (variant) {
      case "minimal":
        return "text-sm text-gray-600 mb-4";
      case "page":
        return "text-lg text-gray-600 mb-8 max-w-md mx-auto";
      default:
        return "text-red-700 mb-4";
    }
  };

  return (
    <div className={`text-center ${getVariantClasses()} ${className}`}>
      <LuInfo className={getIconClasses()} />
      
      <h3 className={getTitleClasses()}>
        {title}
      </h3>
      
      <p className={getMessageClasses()}>
        {message}
      </p>

      {showDetails && error && (
        <details className="mb-4 text-left">
          <summary className="cursor-pointer text-sm text-gray-600 hover:text-gray-800">
            Show error details
          </summary>
          <pre className="mt-2 p-3 bg-gray-100 rounded text-xs text-gray-800 overflow-auto">
            {error.message || error.toString()}
          </pre>
        </details>
      )}

      <div className="flex flex-wrap gap-3 justify-center">
        {showRetry && resetError && (
          <button
            onClick={resetError}
            className="inline-flex items-center px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
          >
            <LuRefreshCw className="w-4 h-4 mr-2" />
            Try Again
          </button>
        )}

        {showHome && onHome && (
          <button
            onClick={onHome}
            className="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
          >
            <LuHouse className="w-4 h-4 mr-2" />
            Go Home
          </button>
        )}

        {showBack && onBack && (
          <button
            onClick={onBack}
            className="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
          >
            <LuArrowLeft className="w-4 h-4 mr-2" />
            Go Back
          </button>
        )}
      </div>
    </div>
  );
};

/**
 * Specific error fallback variants for common use cases
 */

// Network error fallback
export const NetworkErrorFallback = ({ resetError, className = "" }) => (
  <ErrorFallback
    title="Connection Error"
    message="Unable to connect to the server. Please check your internet connection and try again."
    resetError={resetError}
    className={className}
    variant="card"
  />
);

// Not found error fallback
export const NotFoundErrorFallback = ({ onHome, onBack, className = "" }) => (
  <ErrorFallback
    title="Page Not Found"
    message="The page you're looking for doesn't exist or has been moved."
    showRetry={false}
    showHome={true}
    showBack={true}
    onHome={onHome}
    onBack={onBack}
    className={className}
    variant="page"
  />
);

// Permission error fallback
export const PermissionErrorFallback = ({ onHome, className = "" }) => (
  <ErrorFallback
    title="Access Denied"
    message="You don't have permission to access this resource."
    showRetry={false}
    showHome={true}
    onHome={onHome}
    className={className}
    variant="card"
  />
);

// Loading error fallback (for failed data fetching)
export const LoadingErrorFallback = ({ resetError, message, className = "" }) => (
  <ErrorFallback
    title="Failed to Load Data"
    message={message || "Unable to load the requested data. Please try again."}
    resetError={resetError}
    className={className}
    variant="minimal"
  />
);

// Form error fallback
export const FormErrorFallback = ({ resetError, message, className = "" }) => (
  <ErrorFallback
    title="Form Error"
    message={message || "There was an error processing your request. Please try again."}
    resetError={resetError}
    className={className}
    variant="card"
  />
);

// Component error fallback (for React Error Boundaries)
export const ComponentErrorFallback = ({ error, resetError, className = "" }) => (
  <ErrorFallback
    error={error}
    title="Component Error"
    message="This component encountered an error and couldn't render properly."
    resetError={resetError}
    showDetails={process.env.NODE_ENV === 'development'}
    className={className}
    variant="card"
  />
);

export default ErrorFallback;
