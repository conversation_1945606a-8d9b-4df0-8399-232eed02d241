import { useContext, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { UserContext } from "../contexts/userContext";

export const useUserAuth = () => {
  const { user, loading, clearUser } = useContext(UserContext);
  const navigate = useNavigate();
  
  useEffect(() => {
    if (!loading) {
      if (!user) {
        clearUser();
        navigate("/login");
      }
    }
  }, [user, loading, clearUser, navigate]);
  
  return { user, loading, clearUser, navigate };
};

export default useUserAuth;
