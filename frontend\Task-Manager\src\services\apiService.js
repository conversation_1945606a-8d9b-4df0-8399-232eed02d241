import axiosInstance from '../utils/axiosInstance';
import { toast } from 'react-hot-toast';

/**
 * Make API request with error handling
 * @param {Object} config - Axios request config
 * @param {Object} options - Error handling options
 * @returns {Promise} - API response
 */
const makeRequest = async (config, options = {}) => {
  try {
    const response = await axiosInstance(config);
    return response.data;
  } catch (error) {
    // Handle error with toast notification
    const errorMessage = error.response?.data?.message || error.message || 'An error occurred';

    if (options.showToast !== false) {
      toast.error(errorMessage);
    }

    // Handle specific redirects
    if (options.redirectTo && error.response?.status === 401) {
      window.location.href = options.redirectTo;
    }

    throw error;
  }
};

// API methods
const apiService = {
  // Auth endpoints
  auth: {
    login: (credentials) => 
      makeRequest({
        method: 'POST',
        url: '/auth/login',
        data: credentials
      }, {
        redirectTo: '/login'
      }),
    
    register: (userData) => 
      makeRequest({
        method: 'POST',
        url: '/auth/register',
        data: userData
      }),
    
    logout: () => 
      makeRequest({
        method: 'POST',
        url: '/auth/logout'
      })
  },

  // Task endpoints
  tasks: {
    getAll: () => 
      makeRequest({
        method: 'GET',
        url: '/tasks'
      }),
    
    getById: (id) => 
      makeRequest({
        method: 'GET',
        url: `/tasks/${id}`
      }),
    
    create: (taskData) => 
      makeRequest({
        method: 'POST',
        url: '/tasks',
        data: taskData
      }),
    
    update: (id, taskData) => 
      makeRequest({
        method: 'PUT',
        url: `/tasks/${id}`,
        data: taskData
      }),
    
    delete: (id) => 
      makeRequest({
        method: 'DELETE',
        url: `/tasks/${id}`
      })
  },

  // User endpoints
  users: {
    getProfile: () => 
      makeRequest({
        method: 'GET',
        url: '/users/profile'
      }),
    
    updateProfile: (userData) => 
      makeRequest({
        method: 'PUT',
        url: '/users/profile',
        data: userData
      })
  }
};

export default apiService; 