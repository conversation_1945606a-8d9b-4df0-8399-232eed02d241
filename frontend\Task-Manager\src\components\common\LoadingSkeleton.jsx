import React from 'react';

/**
 * Reusable Loading Skeleton Component
 * Eliminates duplicate loading skeleton code across components
 */

/**
 * Basic skeleton item
 */
const SkeletonItem = ({ className = "", width = "100%", height = "1rem" }) => (
  <div 
    className={`bg-gray-200 rounded animate-pulse ${className}`}
    style={{ width, height }}
  />
);

/**
 * Card skeleton for task cards, user cards, etc.
 */
const CardSkeleton = ({ showAvatar = false, lines = 3 }) => (
  <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200 animate-pulse">
    {showAvatar && (
      <div className="flex items-center mb-3">
        <div className="w-8 h-8 bg-gray-200 rounded-full mr-3" />
        <div className="h-4 bg-gray-200 rounded w-1/3" />
      </div>
    )}
    
    <div className="space-y-2">
      <div className="h-5 bg-gray-200 rounded w-3/4" />
      {Array.from({ length: lines }).map((_, index) => (
        <div 
          key={index}
          className="h-4 bg-gray-200 rounded"
          style={{ width: `${Math.random() * 40 + 60}%` }}
        />
      ))}
    </div>
    
    <div className="flex justify-between items-center mt-4">
      <div className="h-6 bg-gray-200 rounded w-20" />
      <div className="h-6 bg-gray-200 rounded w-16" />
    </div>
  </div>
);

/**
 * Table row skeleton
 */
const TableRowSkeleton = ({ columns = 4 }) => (
  <tr className="animate-pulse">
    {Array.from({ length: columns }).map((_, index) => (
      <td key={index} className="px-6 py-4">
        <div className="h-4 bg-gray-200 rounded" />
      </td>
    ))}
  </tr>
);

/**
 * Dashboard stats skeleton
 */
const StatsSkeleton = ({ count = 4 }) => (
  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
    {Array.from({ length: count }).map((_, index) => (
      <div key={index} className="bg-white p-6 rounded-lg shadow-sm border border-gray-200 animate-pulse">
        <div className="flex items-center justify-between">
          <div>
            <div className="h-4 bg-gray-200 rounded w-20 mb-2" />
            <div className="h-8 bg-gray-200 rounded w-16" />
          </div>
          <div className="w-12 h-12 bg-gray-200 rounded-lg" />
        </div>
      </div>
    ))}
  </div>
);

/**
 * Chart skeleton
 */
const ChartSkeleton = ({ height = "300px" }) => (
  <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200 animate-pulse">
    <div className="h-6 bg-gray-200 rounded w-1/3 mb-4" />
    <div 
      className="bg-gray-200 rounded"
      style={{ height }}
    />
  </div>
);

/**
 * List skeleton for tasks, users, etc.
 */
const ListSkeleton = ({ items = 5, showAvatar = false }) => (
  <div className="space-y-4">
    {Array.from({ length: items }).map((_, index) => (
      <div key={index} className="bg-white p-4 rounded-lg shadow-sm border border-gray-200 animate-pulse">
        <div className="flex items-center space-x-4">
          {showAvatar && <div className="w-10 h-10 bg-gray-200 rounded-full" />}
          <div className="flex-1 space-y-2">
            <div className="h-4 bg-gray-200 rounded w-3/4" />
            <div className="h-3 bg-gray-200 rounded w-1/2" />
          </div>
          <div className="h-8 bg-gray-200 rounded w-20" />
        </div>
      </div>
    ))}
  </div>
);

/**
 * Form skeleton
 */
const FormSkeleton = ({ fields = 4 }) => (
  <div className="space-y-6">
    {Array.from({ length: fields }).map((_, index) => (
      <div key={index} className="animate-pulse">
        <div className="h-4 bg-gray-200 rounded w-1/4 mb-2" />
        <div className="h-10 bg-gray-200 rounded w-full" />
      </div>
    ))}
    <div className="flex space-x-4 pt-4">
      <div className="h-10 bg-gray-200 rounded w-24" />
      <div className="h-10 bg-gray-200 rounded w-24" />
    </div>
  </div>
);

/**
 * Main LoadingSkeleton component with different variants
 */
const LoadingSkeleton = ({ 
  variant = "card", 
  count = 1, 
  showAvatar = false,
  lines = 3,
  columns = 4,
  height = "300px",
  className = ""
}) => {
  const renderSkeleton = () => {
    switch (variant) {
      case "card":
        return Array.from({ length: count }).map((_, index) => (
          <CardSkeleton key={index} showAvatar={showAvatar} lines={lines} />
        ));
      
      case "table":
        return Array.from({ length: count }).map((_, index) => (
          <TableRowSkeleton key={index} columns={columns} />
        ));
      
      case "stats":
        return <StatsSkeleton count={count} />;
      
      case "chart":
        return <ChartSkeleton height={height} />;
      
      case "list":
        return <ListSkeleton items={count} showAvatar={showAvatar} />;
      
      case "form":
        return <FormSkeleton fields={count} />;
      
      case "item":
        return Array.from({ length: count }).map((_, index) => (
          <SkeletonItem key={index} />
        ));
      
      default:
        return <CardSkeleton showAvatar={showAvatar} lines={lines} />;
    }
  };

  return (
    <div className={className}>
      {renderSkeleton()}
    </div>
  );
};

// Export individual components for specific use cases
export {
  SkeletonItem,
  CardSkeleton,
  TableRowSkeleton,
  StatsSkeleton,
  ChartSkeleton,
  ListSkeleton,
  FormSkeleton
};

export default LoadingSkeleton;
