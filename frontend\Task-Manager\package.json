{"name": "task-manager", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.1.1", "@mui/material": "^7.1.1", "aos": "^2.3.4", "axios": "^1.9.0", "date-fns": "^4.1.0", "framer-motion": "^12.18.1", "moment": "^2.30.1", "react": "^19.1.0", "react-countup": "^6.5.3", "react-dom": "^19.1.0", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-intersection-observer": "^9.16.0", "react-router-dom": "^7.6.0", "recharts": "^2.15.3", "socket.io-client": "^4.8.1"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.14", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.4.24", "tailwindcss": "^3.3.2", "vite": "^6.3.5", "vitest": "^3.2.4", "babel-plugin-transform-remove-console": "^6.9.4"}}