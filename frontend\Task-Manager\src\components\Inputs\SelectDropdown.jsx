import React, { useState, useRef, useEffect } from "react";
import { createPortal } from "react-dom";
import { LuChevronDown } from "react-icons/lu";

const SelectDropdown = ({ options, selected, onSelect, placeholder, disabled = false }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [position, setPosition] = useState({ top: 0, left: 0, width: 0 });
  const dropdownRef = useRef(null);

  const updatePosition = () => {
    if (dropdownRef.current && isOpen) {
      const rect = dropdownRef.current.getBoundingClientRect();
      setPosition({
        top: rect.bottom + window.scrollY,
        left: rect.left + window.scrollX,
        width: rect.width
      });
    }
  };

  useEffect(() => {
    if (isOpen) {
      updatePosition();
      window.addEventListener('scroll', updatePosition, true);
      window.addEventListener('resize', updatePosition);
    }
    return () => {
      window.removeEventListener('scroll', updatePosition, true);
      window.removeEventListener('resize', updatePosition);
    };
  }, [isOpen]);

  useEffect(() => {
    const handleClickOutside = () => {
      if (isOpen) {
        setIsOpen(false);
      }
    };

    // Use window to catch all clicks
    window.addEventListener('click', handleClickOutside);
    return () => window.removeEventListener('click', handleClickOutside);
  }, [isOpen]);

  const handleButtonClick = (e) => {
    e.stopPropagation(); // Prevent the window click handler from immediately closing the dropdown
    if (!disabled) {
      setIsOpen(prev => !prev);
    }
  };

  const handleSelect = (option) => {
    onSelect(option);
    setIsOpen(false);
  };

  const selectedOption = typeof selected === 'object' ? selected : 
    options.find(opt => opt.value?.toLowerCase() === selected?.toLowerCase());
  const selectedLabel = selectedOption?.label || placeholder || "Select an option";

  return (
    <div className="relative w-full z-10" ref={dropdownRef}>
      <button
        type="button"
        className={`form-input flex items-center justify-between w-full h-12 ${disabled ? 'bg-gray-100 cursor-not-allowed' : 'hover:border-blue-500'}`}
        onClick={handleButtonClick}
        disabled={disabled}
      >
        <span className={selectedOption ? 'text-black' : 'text-gray-500'}>{selectedLabel}</span>
        <LuChevronDown className={`transition-transform duration-200 ${isOpen ? "rotate-180" : ""}`} />
      </button>

      {isOpen && !disabled && createPortal(
        <div 
          style={{
            position: 'fixed',
            width: position.width,
            top: position.top,
            left: position.left
          }}
          className="bg-white border border-gray-200 rounded-md shadow-lg z-[9999] max-h-60 overflow-y-auto"
        >
          {options.map((option, index) => (
            <div
              key={index}
              onClick={() => handleSelect(option)}
              className={`px-4 py-3 cursor-pointer hover:bg-gray-100 text-sm text-gray-700
                ${selectedOption?.value === option.value ? 'bg-indigo-50 text-indigo-700' : ''}`}
            >
              {option.label}
            </div>
          ))}
        </div>,
        document.body
      )}
    </div>
  );
};

export default SelectDropdown;
