/**
 * Validation Service
 * Centralized validation utilities to eliminate duplicate validation logic
 */

const mongoose = require('mongoose');

/**
 * Validate email format
 * @param {String} email - Email to validate
 * @returns {Boolean} True if valid email format
 */
const isValidEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * Validate password strength
 * @param {String} password - Password to validate
 * @param {Object} options - Validation options
 * @returns {Object} Validation result with isValid and message
 */
const validatePassword = (password, options = {}) => {
  const {
    minLength = 8,
    requireUppercase = true,
    requireLowercase = true,
    requireNumbers = true,
    requireSpecialChars = false
  } = options;

  if (!password) {
    return { isValid: false, message: "Password is required" };
  }

  if (password.length < minLength) {
    return { 
      isValid: false, 
      message: `Password must be at least ${minLength} characters long` 
    };
  }

  if (requireUppercase && !/[A-Z]/.test(password)) {
    return { 
      isValid: false, 
      message: "Password must contain at least one uppercase letter" 
    };
  }

  if (requireLowercase && !/[a-z]/.test(password)) {
    return { 
      isValid: false, 
      message: "Password must contain at least one lowercase letter" 
    };
  }

  if (requireNumbers && !/\d/.test(password)) {
    return { 
      isValid: false, 
      message: "Password must contain at least one number" 
    };
  }

  if (requireSpecialChars && !/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    return { 
      isValid: false, 
      message: "Password must contain at least one special character" 
    };
  }

  return { isValid: true, message: "Password is valid" };
};

/**
 * Validate MongoDB ObjectId
 * @param {String} id - ID to validate
 * @returns {Boolean} True if valid ObjectId
 */
const isValidObjectId = (id) => {
  return mongoose.isValidObjectId(id);
};

/**
 * Validate task data
 * @param {Object} taskData - Task data to validate
 * @param {Boolean} isUpdate - Whether this is an update operation
 * @returns {Object} Validation result
 */
const validateTaskData = (taskData, isUpdate = false) => {
  const errors = [];

  // For updates, only validate fields that are present
  if (!isUpdate || taskData.title !== undefined) {
    if (!taskData.title || taskData.title.trim().length === 0) {
      errors.push("Task title is required");
    }
  }

  if (taskData.title && taskData.title.length > 200) {
    errors.push("Task title must be less than 200 characters");
  }

  if (taskData.description && taskData.description.length > 2000) {
    errors.push("Task description must be less than 2000 characters");
  }

  if (taskData.priority && !['low', 'medium', 'high', 'Low', 'Medium', 'High'].includes(taskData.priority)) {
    errors.push("Priority must be low, medium, or high");
  }

  if (taskData.status && !['todo', 'in-progress', 'completed', 'Pending', 'In Progress', 'Completed'].includes(taskData.status)) {
    errors.push("Status must be todo, in-progress, or completed");
  }

  // For updates, be more lenient with due date validation
  if (taskData.dueDate && !isUpdate && new Date(taskData.dueDate) < new Date()) {
    errors.push("Due date cannot be in the past");
  }

  // Validate assignedTo field (legacy support)
  if (taskData.assignedTo && !Array.isArray(taskData.assignedTo)) {
    errors.push("assignedTo must be an array of user IDs");
  }

  // Validate team field (current field name)
  if (taskData.team && !Array.isArray(taskData.team)) {
    errors.push("team must be an array of user IDs");
  }

  // Allow empty arrays - tasks can be created without assigned users
  // The requirement for assigned users can be enforced at the business logic level if needed

  if (taskData.assignedTo && taskData.assignedTo.length > 0) {
    const invalidIds = taskData.assignedTo.filter(id => !isValidObjectId(id));
    if (invalidIds.length > 0) {
      errors.push("Invalid user IDs in assignedTo array");
    }
  }

  if (taskData.team && taskData.team.length > 0) {
    const invalidIds = taskData.team.filter(id => !isValidObjectId(id));
    if (invalidIds.length > 0) {
      errors.push("Invalid user IDs in team array");
    }
  }

  // Validate attachments if present
  if (taskData.attachments && !Array.isArray(taskData.attachments)) {
    errors.push("Attachments must be an array");
  }

  // Validate todoChecklist (or subTasks) for non-empty text
  const todoList = taskData.todoChecklist || taskData.subTasks || [];
  if (Array.isArray(todoList)) {
    const hasEmpty = todoList.some(item => !item.text || item.text.trim() === '');
    if (hasEmpty) {
      errors.push('All todo items must have non-empty text');
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Validate user data
 * @param {Object} userData - User data to validate
 * @param {Boolean} isUpdate - Whether this is an update operation
 * @returns {Object} Validation result
 */
const validateUserData = (userData, isUpdate = false) => {
  const errors = [];

  if (!isUpdate && (!userData.name || userData.name.trim().length === 0)) {
    errors.push("Name is required");
  }

  if (userData.name && userData.name.length > 100) {
    errors.push("Name must be less than 100 characters");
  }

  if (!isUpdate && (!userData.email || !isValidEmail(userData.email))) {
    errors.push("Valid email is required");
  }

  if (userData.email && !isValidEmail(userData.email)) {
    errors.push("Invalid email format");
  }

  if (!isUpdate && !userData.password) {
    errors.push("Password is required");
  }

  if (userData.password) {
    const passwordValidation = validatePassword(userData.password);
    if (!passwordValidation.isValid) {
      errors.push(passwordValidation.message);
    }
  }

  if (userData.role && !['admin', 'member', 'user'].includes(userData.role)) {
    errors.push("Role must be admin, member, or user");
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Validate file upload data
 * @param {Object} file - File object from multer
 * @param {Object} options - Upload options
 * @returns {Object} Validation result
 */
const validateFileUpload = (file, options = {}) => {
  const {
    maxSize = 5 * 1024 * 1024, // 5MB default
    allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'],
    allowedExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.pdf']
  } = options;

  const errors = [];

  if (!file) {
    errors.push("No file provided");
    return { isValid: false, errors };
  }

  if (file.size > maxSize) {
    errors.push(`File size must be less than ${Math.round(maxSize / (1024 * 1024))}MB`);
  }

  if (!allowedTypes.includes(file.mimetype)) {
    errors.push(`File type ${file.mimetype} is not allowed`);
  }

  const fileExtension = file.originalname.toLowerCase().substring(file.originalname.lastIndexOf('.'));
  if (!allowedExtensions.includes(fileExtension)) {
    errors.push(`File extension ${fileExtension} is not allowed`);
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Sanitize user input to prevent XSS
 * @param {String} input - Input to sanitize
 * @returns {String} Sanitized input
 */
const sanitizeInput = (input) => {
  if (typeof input !== 'string') return input;
  
  return input
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#x27;')
    .replace(/\//g, '&#x2F;');
};

/**
 * Validate pagination parameters
 * @param {Object} query - Query parameters
 * @returns {Object} Validated pagination parameters
 */
const validatePagination = (query) => {
  const page = Math.max(1, parseInt(query.page) || 1);
  const limit = Math.min(100, Math.max(1, parseInt(query.limit) || 10));
  const skip = (page - 1) * limit;

  return { page, limit, skip };
};

module.exports = {
  isValidEmail,
  validatePassword,
  isValidObjectId,
  validateTaskData,
  validateUserData,
  validateFileUpload,
  sanitizeInput,
  validatePagination
};
