// Use backend API base URL if available, otherwise fall back to previously used variable, then to localhost:5000
export const BASE_URL =
  import.meta.env.VITE_API_BASE_URL || // preferred env variable used elsewhere in codebase
  import.meta.env.VITE_API_URL;       // legacy env variable

// Separate URL for accessing uploaded files from the backend server
export const UPLOADS_URL = `${BASE_URL}/uploads`;

// Centralized API Path Definitions
export const API_PATHS = {
  // Authentication
  AUTH: {
    LOGIN: "/api/auth/login",
    REGISTER: "/api/auth/register",
    GET_ME: "/api/auth/me",
    GET_PROFILE: "/api/auth/profile",
    UPDATE_PROFILE: "/api/auth/profile", // Add this line
    LOGOUT: "/api/auth/logout",
  },

  // User Management
  USERS: {
    GET_ALL: "/api/users",
    GET_ALL_USERS: "/api/users", // Legacy alias
    GET_BY_ID: (userId) => `/api/users/${userId}`,
    DELETE_USER: (userId) => `/api/users/${userId}`,
  },

  // Task Management
  TASKS: {
    GET_ALL: "/api/tasks",
    GET_BY_ID: (taskId) => `/api/tasks/${taskId}`,
    CREATE: "/api/tasks",
    UPDATE: (taskId) => `/api/tasks/${taskId}`,
    DELETE_TASK: (taskId) => `/api/tasks/${taskId}`,
    DELETE: (taskId) => `/api/tasks/${taskId}`, // Legacy alias
    BULK_DELETE: "/api/tasks/bulk",
    UPDATE_STATUS: (taskId) => `/api/tasks/${taskId}/status`,
    UPDATE_CHECKLIST: (taskId) => `/api/tasks/${taskId}/checklist`,
    GET_USER_DASHBOARD_DATA: "/api/tasks/user-dashboard-data",
    GET_DASHBOARD_DATA: "/api/tasks/dashboard-data",
  },

  // Comments
  COMMENTS: {
    GET_ALL: (taskId) => `/api/tasks/${taskId}/comments`,
    CREATE: (taskId) => `/api/tasks/${taskId}/comments`,
    PIN: (taskId, commentId) => `/api/tasks/${taskId}/comments/${commentId}/pin`,
  },

  // File Uploads
  UPLOADS: {
    PROFILE_IMAGE: "/api/upload/profile-image",
    SINGLE_UPLOAD: "/api/uploads/single",
  },

  // Notifications
  NOTIFICATIONS: {
    GET_NOTIFICATIONS: "/api/notifications",
    GET_UNREAD_COUNT: "/api/notifications/unread-count",
    MARK_AS_READ: (id) => `/api/notifications/${id}/read`,
    MARK_ALL_AS_READ: "/api/notifications/mark-all-read",
    DELETE_NOTIFICATION: (id) => `/api/notifications/${id}`,
    CLEAR_ALL: "/api/notifications/clear-all",
  },

  // Reports
  REPORTS: {
    EXPORT_USERS: "/api/reports/export/users",
    EXPORT_TASKS: "/api/reports/export/tasks",
  },

  // Image Uploads
  IMAGE: {
    UPLOAD_IMAGE: "/api/uploads/single",
    UPLOAD_SIGNUP_IMAGE: "/api/uploads/signup-image",
  }
};
