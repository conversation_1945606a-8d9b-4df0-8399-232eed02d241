const mongoose = require('mongoose');
const User = require('../models/User');
require('dotenv').config({ path: require('path').resolve(__dirname, '../.env') });

const MONGO_URI = process.env.MONGO_URI || 'mongodb://localhost:27017/taskmanager';

async function cleanup() {
  await mongoose.connect(MONGO_URI, { useNewUrlParser: true, useUnifiedTopology: true });

  const result = await User.deleteMany({
    $or: [
      { email: { $regex: /notification(admin|recipient)[0-9]+@example\.com/i } },
      { name: { $regex: /^Notification (Admin|Recipient)/i } },
      { email: { $regex: /@example\.com$/i } }
    ]
  });

  console.log(`Deleted ${result.deletedCount} test/demo users.`);
  await mongoose.disconnect();
}

cleanup().catch(err => {
  console.error('Cleanup failed:', err);
  process.exit(1);
});
