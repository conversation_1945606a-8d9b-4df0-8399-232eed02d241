const mongoose = require('mongoose');
const { getSocketInstance } = require('../socket/socketInstance');
const Task = require('../models/Task');
const {
  createTaskAssignmentNotifications,
  createTaskStatusUpdateNotifications,
} = require('./taskNotificationService');

const getTasks = async (filter = {}, options = {}) => {
  const page = parseInt(options.page, 10) > 0 ? parseInt(options.page, 10) : 1;
  const limit = parseInt(options.limit, 10) > 0 ? parseInt(options.limit, 10) : 10;
  const skip = (page - 1) * limit;

  const [tasks, total] = await Promise.all([
    Task.find(filter)
      .populate({
        path: 'team',
        select: 'name email profileImageUrl role',
        model: 'User'
      })
      .populate({
        path: 'createdBy',
        select: 'name email profileImageUrl',
        model: 'User'
      })
      .sort({ createdAt: -1, _id: -1 })
      .skip(skip)
      .limit(limit)
      .lean(),
    Task.countDocuments(filter)
  ]);

  const tasksWithAssigned = tasks.map(task => ({
    ...task,
    assignedTo: Array.isArray(task.team) ? [...task.team] : [],
    dueDate: task.dueDate || task.deadline || null,
  }));

  return {
    tasks: tasksWithAssigned,
    total,
    page,
    limit,
    totalPages: Math.ceil(total / limit)
  };
};

const getTaskById = async (taskId, options = {}) => {
  if (!mongoose.Types.ObjectId.isValid(taskId)) {
    return null;
  }
  let task = await Task.findById(taskId)
    .populate({ path: 'team', select: 'name email profileImageUrl role' })
    .populate({ path: 'createdBy', select: 'name email profileImageUrl role' })
    .lean();

  if (!task) return null;

  // Defensive: If team contains any non-object (e.g. string/ID), re-populate from User model
  if (Array.isArray(task.team) && task.team.length > 0 && typeof task.team[0] !== 'object') {
    const User = require('../models/User');
    const populatedTeam = await User.find({ _id: { $in: task.team } }, 'name email profileImageUrl role').lean();
    task.team = populatedTeam;
  }

  return {
    ...task,
    assignedTo: Array.isArray(task.team) ? [...task.team] : [],
    dueDate: task.dueDate || task.deadline || null,
    todoCheckList: task.todoCheckList || task.subTasks || [],
    attachments: task.attachments || [],
  };
};

const createTask = async (taskData, createdByUserId) => {
  // Explicitly map fields to ensure only schema-defined data is used
  const dataToSave = {
    title: taskData.title,
    description: taskData.description,
    priority: taskData.priority,
    status: taskData.status || 'todo',
    deadline: taskData.deadline, // Mapped from dueDate in controller
    team: taskData.team || [], // Mapped from assignedTo in frontend
    subTasks: taskData.subTasks || [], // Mapped from todoCheckList in frontend
    attachments: taskData.attachments || [],
    createdBy: createdByUserId,
  };

  const newTask = await Task.create(dataToSave);
  // Notification: create assignment notifications for all assigned users except creator
  if (Array.isArray(newTask.team) && newTask.team.length > 0 && createdByUserId) {
    const assignedUserIds = (newTask.team || []).filter(
      (id) => id && id.toString() !== createdByUserId.toString()
    );
    if (assignedUserIds.length > 0) {
      try {
        await createTaskAssignmentNotifications(newTask, assignedUserIds, createdByUserId);
      } catch (e) {
        console.error('Failed to create assignment notification on createTask:', e);
      }
    }
  }
  return getTaskById(newTask._id);
};

const updateTask = async (taskId, updateData, updatedByUserId) => {
  if (!mongoose.Types.ObjectId.isValid(taskId)) {
    return null;
  }

  const task = await Task.findById(taskId);
  if (!task) {
    return null;
  }

  const oldStatus = task.status;
  const oldTeam = (task.team || []).map(id => id.toString());

  // Handle specific fields that require special logic or marking as modified
  if (updateData.attachments !== undefined) {
    task.attachments = updateData.attachments;
    task.markModified('attachments');
  }
  
  if (updateData.subTasks !== undefined) {
    task.subTasks = updateData.subTasks;
    task.markModified('subTasks');
  }

  if (updateData.team !== undefined || updateData.assignedTo !== undefined) {
    const newTeamData = updateData.team || updateData.assignedTo || [];
    task.team = newTeamData.map(u => (typeof u === 'object' && u !== null ? u._id : u)).filter(Boolean);
  }

  // These fields are handled above, so we exclude them from generic assignment
  const managedFields = ['attachments', 'team', 'assignedTo', 'subTasks'];
  for (const key in updateData) {
    if (!managedFields.includes(key) && Object.prototype.hasOwnProperty.call(updateData, key)) {
      task[key] = updateData[key];
    }
  }

  try {
    const updatedTask = await task.save();

    const newTeam = (updatedTask.team || []).map(id => id.toString());
    const addedMembers = newTeam.filter(id => !oldTeam.includes(id));
    if (addedMembers.length > 0 && updatedByUserId) {
      createTaskAssignmentNotifications(updatedTask, addedMembers, updatedByUserId)
        .catch(e => console.error('Failed to create assignment notification:', e));
    }

    const populatedTask = await getTaskById(updatedTask._id);

    // Emit a socket event to notify clients of the update
    const io = getSocketInstance();
    if (io && populatedTask) {
      const roomName = `task-${populatedTask._id.toString()}`;
      io.to(roomName).emit('taskUpdated', populatedTask);
      // Also emit to a general admin room for dashboard-level updates
      io.to('admin-room').emit('taskUpdated', populatedTask);
    }

    return populatedTask;
  } catch (error) {
    console.error('Error saving task in service:', error);
    throw error;
  }
};

const deleteTask = async (taskId) => {
  if (!mongoose.Types.ObjectId.isValid(taskId)) {
    return null;
  }
  return await Task.findByIdAndDelete(taskId);
};

const updateTaskStatus = async (taskId, status, updatedByUserId) => {
  // Fetch the task first to get old status
  const task = await Task.findById(taskId);
  if (!task) return null;
  const oldStatus = task.status;

  // Directly update the task and get the populated result
  const updatedTask = await Task.findByIdAndUpdate(
    taskId,
    { status },
    { new: true, runValidators: true }
  );

  if (!updatedTask) {
    throw new Error('Task not found or update failed');
  }

  // Now that we have the updated task, we can proceed with notifications and socket events
  const populatedTask = await getTaskById(updatedTask._id); // Ensure it's fully populated

  // Only notify if status actually changed
  if (populatedTask && oldStatus !== status) {
    try {
      await createTaskStatusUpdateNotifications(populatedTask, oldStatus, status, updatedByUserId);
    } catch (e) {
      console.error('Failed to create status update notification in updateTaskStatus:', e);
    }
  }
  // Emit socket events to all relevant parties
  const io = getSocketInstance();
  if (io && populatedTask) {
    // Emit to the specific task room for detailed views
    io.to(`task-${populatedTask._id.toString()}`).emit('taskUpdated', populatedTask);

    // Emit to each assigned user's personal room for general updates
    if (Array.isArray(populatedTask.team)) {
      populatedTask.team.forEach(user => {
        if (user && user._id) {
          io.to(user._id.toString()).emit('taskUpdated', populatedTask);
        }
      });
    }
    // Also emit to a general admin room for dashboard-level updates
    io.to('admin-room').emit('taskUpdated', populatedTask);
  }

  return populatedTask;
};

const updateTaskChecklist = async (taskId, checklistItems, updatedByUserId) => {
  const subTasks = checklistItems.map(item => ({
    _id: item._id, // Preserve _id to allow Mongoose to update existing items
    title: item.title || item.text,
    // Handle both `isCompleted` from the new frontend and legacy `completed` field
    isCompleted: typeof item.isCompleted !== 'undefined' ? item.isCompleted : (item.completed === 'true' || item.completed === true),
  }));
  return updateTask(taskId, { subTasks }, updatedByUserId);
};

const bulkDeleteTasks = async (taskIds) => {
  const validTaskIds = taskIds.filter(id => mongoose.Types.ObjectId.isValid(id));
  if (validTaskIds.length !== taskIds.length) {
    throw new Error('Invalid task IDs provided.');
  }
  const result = await Task.deleteMany({ _id: { $in: validTaskIds } });
  return {
    deletedCount: result.deletedCount,
    requestedCount: taskIds.length,
  };
};

const checkTaskAccess = (task, user) => {
  if (!task || !user) return false;
  if (user.role === 'admin') return true;

  const userIdStr = user._id.toString();
  const isCreator = task.createdBy && task.createdBy._id.toString() === userIdStr;
  const isAssigned = task.team && task.team.some(member => member._id.toString() === userIdStr);

  return isCreator || isAssigned;
};

module.exports = {
  getTasks,
  getTaskById,
  createTask,
  updateTask,
  deleteTask,
  updateTaskStatus,
  updateTaskChecklist,
  bulkDeleteTasks,
  checkTaskAccess,
};
