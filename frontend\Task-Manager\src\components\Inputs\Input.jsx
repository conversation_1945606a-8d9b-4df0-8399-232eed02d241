import React, { useState } from "react";
import { Fa<PERSON><PERSON><PERSON>ye, FaRegEyeSlash } from "react-icons/fa";

const Input = ({ value, onChange, label, placeholder, type, name }) => {
  const [showPassword, setShowPassword] = useState(false);

  const toggleShowPassword = () => {
    setShowPassword(!showPassword);
  };

  // Generate an ID from the name prop or label for accessibility
  const inputId = name || (label ? label.toLowerCase().replace(/\s+/g, '-') : '');

  return (
    <div className="mb-4 w-full">
      {label && (
        <label htmlFor={inputId} className="block text-sm font-medium text-slate-700 mb-1">
          {label}
        </label>
      )}
      <div className="input-box relative"> {/* Ensure input-box class or add styling for border, etc. Added relative for icon positioning */}
        <input
          id={inputId}
          type={
            type === "password" ? (showPassword ? "text" : "password") : type
          }
          placeholder={placeholder}
          className="w-full bg-transparent outline-none" // Reverted to rely on input-box for styling
          value={value}
          onChange={onChange} // Simplified onChange
          name={name || (label ? label.toLowerCase().replace(/\s+/g, '-') : '')} // Added name attribute
        />

        {type === "password" && (
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
            {showPassword ? (
              <FaRegEye
                size={20}
                className="text-primary cursor-pointer"
                onClick={toggleShowPassword}
              />
            ) : (
              <FaRegEyeSlash
                size={20}
                className="text-slate-400 cursor-pointer"
                onClick={toggleShowPassword}
              />
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default Input;
