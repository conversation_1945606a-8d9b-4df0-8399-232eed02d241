const Task = require("../models/Task");
const User = require("../models/User");
const mongoose = require("mongoose");
const { getUserTaskCounts, getMultipleUserTaskCounts } = require('../services/taskStatisticsService');

// @desc    Get all users
// @route   GET /api/users/
// @access  Private
const getUsers = async (req, res) => {
  try {
    // Find all users, excluding their password, and send as a simple array
    const users = await User.find({}).select("-password").lean();
    res.json(users);
  } catch (error) {
    res.status(500).json({ message: "Server error", error: error.message });
  }
};

// @desc    Get a user by ID
// @route   GET /api/users/:id
// @access  Private
const getUserById = async (req, res) => {
  try {
    if (!mongoose.isValidObjectId(req.params.id)) {
      return res.status(400).json({ message: "Invalid user ID" });
    }
    const user = await User.findById(req.params.id).select('-password').lean();
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }
    const taskCounts = await getUserTaskCounts(user._id);
    res.json({
      ...user._doc,
      ...taskCounts
    });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};


// @desc    Delete a user (Admin only)
// @route   DELETE /api/users/:id
// @access  Private (Admin)
const deleteUser = async (req, res) => {
  try {
    if (!mongoose.isValidObjectId(req.params.id)) {
      return res.status(400).json({ message: "Invalid user ID" });
    }
    const user = await User.findByIdAndDelete(req.params.id);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }
    res.json({ message: 'User deleted successfully' });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

module.exports = { getUsers, getUserById, deleteUser };
