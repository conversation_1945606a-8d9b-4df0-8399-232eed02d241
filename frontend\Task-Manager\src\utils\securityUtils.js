/**
 * Frontend Security Utilities
 * Client-side security measures and utilities
 */

/**
 * XSS Protection - Sanitize user input
 */
export const sanitizeInput = (input) => {
  if (typeof input !== 'string') return input;
  
  return input
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#x27;')
    .replace(/\//g, '&#x2F;');
};

/**
 * Validate and sanitize HTML content
 */
export const sanitizeHtml = (html) => {
  if (typeof html !== 'string') return html;
  
  // Remove script tags and event handlers
  return html
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    .replace(/on\w+\s*=\s*"[^"]*"/gi, '')
    .replace(/on\w+\s*=\s*'[^']*'/gi, '')
    .replace(/javascript:/gi, '')
    .replace(/vbscript:/gi, '')
    .replace(/data:text\/html/gi, '');
};

/**
 * Secure token storage
 */
export const tokenStorage = {
  set: (token) => {
    try {
      // Use sessionStorage for better security (cleared when tab closes)
      sessionStorage.setItem('authToken', token);
      
      // Set expiration time
      const expirationTime = Date.now() + (24 * 60 * 60 * 1000); // 24 hours
      sessionStorage.setItem('tokenExpiration', expirationTime.toString());
      
      return true;
    } catch (error) {
      console.error('Failed to store token:', error);
      return false;
    }
  },
  
  get: () => {
    try {
      const token = sessionStorage.getItem('authToken');
      const expiration = sessionStorage.getItem('tokenExpiration');
      
      if (!token || !expiration) {
        return null;
      }
      
      // Check if token is expired
      if (Date.now() > parseInt(expiration)) {
        tokenStorage.clear();
        return null;
      }
      
      return token;
    } catch (error) {
      console.error('Failed to retrieve token:', error);
      return null;
    }
  },
  
  clear: () => {
    try {
      sessionStorage.removeItem('authToken');
      sessionStorage.removeItem('tokenExpiration');
      localStorage.removeItem('token'); // Clear any old localStorage tokens
      return true;
    } catch (error) {
      console.error('Failed to clear token:', error);
      return false;
    }
  },
  
  isValid: () => {
    const token = tokenStorage.get();
    return token !== null;
  }
};

/**
 * CSRF Token management
 */
export const csrfToken = {
  generate: () => {
    const array = new Uint8Array(32);
    crypto.getRandomValues(array);
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
  },
  
  set: (token) => {
    sessionStorage.setItem('csrfToken', token);
  },
  
  get: () => {
    return sessionStorage.getItem('csrfToken');
  },
  
  clear: () => {
    sessionStorage.removeItem('csrfToken');
  }
};

/**
 * Input validation utilities
 */
export const validateInput = {
  email: (email) => {
    if (!email || typeof email !== 'string') {
      return { isValid: false, message: 'Email is required' };
    }
    
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return { isValid: false, message: 'Invalid email format' };
    }
    
    if (email.length > 254) {
      return { isValid: false, message: 'Email too long' };
    }
    
    return { isValid: true };
  },
  
  password: (password) => {
    if (!password || typeof password !== 'string') {
      return { isValid: false, message: 'Password is required' };
    }
    
    if (password.length < 8) {
      return { isValid: false, message: 'Password must be at least 8 characters long' };
    }
    
    if (password.length > 128) {
      return { isValid: false, message: 'Password too long' };
    }
    
    const hasUpperCase = /[A-Z]/.test(password);
    const hasLowerCase = /[a-z]/.test(password);
    const hasNumbers = /\d/.test(password);
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);
    
    if (!hasUpperCase || !hasLowerCase || !hasNumbers || !hasSpecialChar) {
      return { 
        isValid: false, 
        message: 'Password must contain uppercase, lowercase, numbers, and special characters' 
      };
    }
    
    return { isValid: true };
  },
  
  text: (text, options = {}) => {
    const { minLength = 0, maxLength = 1000, fieldName = 'Text' } = options;
    
    if (text === null || text === undefined) {
      if (minLength > 0) {
        return { isValid: false, message: `${fieldName} is required` };
      }
      return { isValid: true };
    }
    
    if (typeof text !== 'string') {
      return { isValid: false, message: `${fieldName} must be text` };
    }
    
    if (text.length < minLength) {
      return { isValid: false, message: `${fieldName} must be at least ${minLength} characters` };
    }
    
    if (text.length > maxLength) {
      return { isValid: false, message: `${fieldName} must be less than ${maxLength} characters` };
    }
    
    // Check for suspicious patterns
    const suspiciousPatterns = [
      /<script/i,
      /javascript:/i,
      /vbscript:/i,
      /on\w+\s*=/i
    ];
    
    for (const pattern of suspiciousPatterns) {
      if (pattern.test(text)) {
        return { isValid: false, message: `${fieldName} contains invalid content` };
      }
    }
    
    return { isValid: true };
  },
  
  url: (url) => {
    if (!url || typeof url !== 'string') {
      return { isValid: false, message: 'URL is required' };
    }
    
    try {
      const urlObj = new URL(url);
      
      // Only allow http and https
      if (!['http:', 'https:'].includes(urlObj.protocol)) {
        return { isValid: false, message: 'Only HTTP and HTTPS URLs are allowed' };
      }
      
      // Block localhost and private IPs
      const hostname = urlObj.hostname.toLowerCase();
      if (hostname === 'localhost' || 
          hostname === '127.0.0.1' || 
          hostname.startsWith('192.168.') ||
          hostname.startsWith('10.') ||
          /^172\.(1[6-9]|2[0-9]|3[0-1])\./.test(hostname)) {
        return { isValid: false, message: 'Local URLs are not allowed' };
      }
      
      return { isValid: true };
    } catch (error) {
      return { isValid: false, message: 'Invalid URL format' };
    }
  }
};

/**
 * File upload security
 */
export const fileUploadSecurity = {
  validateFile: (file) => {
    if (!file) {
      return { isValid: false, message: 'No file selected' };
    }
    
    // Check file size (5MB limit)
    const maxSize = 5 * 1024 * 1024;
    if (file.size > maxSize) {
      return { isValid: false, message: 'File size must be less than 5MB' };
    }
    
    // Check file type
    const allowedTypes = [
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp',
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ];
    
    if (!allowedTypes.includes(file.type)) {
      return { isValid: false, message: 'File type not allowed' };
    }
    
    // Check filename for suspicious patterns
    const filename = file.name.toLowerCase();
    const suspiciousExtensions = ['.exe', '.bat', '.cmd', '.scr', '.pif', '.com', '.js', '.vbs'];
    
    for (const ext of suspiciousExtensions) {
      if (filename.endsWith(ext)) {
        return { isValid: false, message: 'File type not allowed' };
      }
    }
    
    // Check for double extensions
    if ((filename.match(/\./g) || []).length > 1) {
      const parts = filename.split('.');
      if (parts.length > 2) {
        return { isValid: false, message: 'Invalid filename' };
      }
    }
    
    return { isValid: true };
  },
  
  sanitizeFilename: (filename) => {
    return filename
      .replace(/[^a-zA-Z0-9.-]/g, '_')
      .replace(/_{2,}/g, '_')
      .substring(0, 100);
  }
};

/**
 * Content Security Policy utilities
 */
export const csp = {
  generateNonce: () => {
    const array = new Uint8Array(16);
    crypto.getRandomValues(array);
    return btoa(String.fromCharCode(...array));
  },
  
  reportViolation: (violation) => {
    // Log CSP violations for monitoring
    console.warn('CSP Violation:', violation);
    
    // In production, send to monitoring service
    if (process.env.NODE_ENV === 'production') {
      // Send to your monitoring service
      fetch('/api/security/csp-violation', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(violation)
      }).catch(console.error);
    }
  }
};

/**
 * Session security
 */
export const sessionSecurity = {
  isSessionValid: () => {
    const lastActivity = sessionStorage.getItem('lastActivity');
    if (!lastActivity) return false;
    
    const now = Date.now();
    const sessionTimeout = 30 * 60 * 1000; // 30 minutes
    
    return (now - parseInt(lastActivity)) < sessionTimeout;
  },
  
  updateActivity: () => {
    sessionStorage.setItem('lastActivity', Date.now().toString());
  },
  
  clearSession: () => {
    sessionStorage.clear();
    localStorage.removeItem('token'); // Clear any old tokens
  }
};

/**
 * Secure random string generation
 */
export const generateSecureRandom = (length = 32) => {
  const array = new Uint8Array(length);
  crypto.getRandomValues(array);
  return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
};

/**
 * Environment detection
 */
export const environment = {
  isDevelopment: () => process.env.NODE_ENV === 'development',
  isProduction: () => process.env.NODE_ENV === 'production',
  isSecureContext: () => window.isSecureContext || location.protocol === 'https:'
};

/**
 * Security headers validation
 */
export const validateSecurityHeaders = (response) => {
  const headers = response.headers;
  const warnings = [];
  
  if (!headers.get('x-content-type-options')) {
    warnings.push('Missing X-Content-Type-Options header');
  }
  
  if (!headers.get('x-frame-options')) {
    warnings.push('Missing X-Frame-Options header');
  }
  
  if (!headers.get('x-xss-protection')) {
    warnings.push('Missing X-XSS-Protection header');
  }
  
  if (warnings.length > 0 && environment.isDevelopment()) {
    console.warn('Security headers missing:', warnings);
  }
  
  return warnings;
};

export default {
  sanitizeInput,
  sanitizeHtml,
  tokenStorage,
  csrfToken,
  validateInput,
  fileUploadSecurity,
  csp,
  sessionSecurity,
  generateSecureRandom,
  environment,
  validateSecurityHeaders
};
