import React, { useState, useEffect } from "react";
import axiosInstance from "../../utils/axiosInstance";
import { API_PATHS } from "../../utils/apiPaths";
import toast from "react-hot-toast";
import Modal from "./Modal";
import { formatDate } from "../../utils/dateUtils";
import AvatarGroup from "../AvatarGroup";
import ProxyImage from "../common/ProxyImage";
import { getAttachmentPreviewUrl } from "../../utils/attachmentUtils";
import { FaFileAlt } from "react-icons/fa";
import LoadingSkeleton from "../common/LoadingSkeleton";

import { useUser } from '../../contexts/userContext';
import { useSocket } from '../../contexts/SocketContext';

const TaskDetailModal = ({ isOpen, onClose, task, onEdit, isLoading = false, onTaskUpdate, isEditing = false }) => {
  useSocket();
  useUser();
  const [selectedAttachment, setSelectedAttachment] = useState(null);
  // derive checklist from task no matter the field name
  const getChecklist = (t) => {
    if (!t) return [];
    if (Array.isArray(t.subTasks) && t.subTasks.length) return t.subTasks;
    if (Array.isArray(t.todoCheckList) && t.todoCheckList.length) return t.todoCheckList;
    if (Array.isArray(t.todoChecklist) && t.todoChecklist.length) return t.todoChecklist;
    return [];
  };

  // maintain a local copy of the checklist so the modal always reflects the latest state
  const [subTasks, setSubTasks] = useState(getChecklist(task));

  // keep local checklist in sync whenever the parent passes a new task prop (open modal or external update)
  useEffect(() => {
    setSubTasks(getChecklist(task));
  }, [task]);

  const handleChecklistChange = async (index) => {
    const currentSubTasks = subTasks; // always derived local copy
    const newSubTasks = currentSubTasks.map((subtask, i) => {
      const toggled = i === index ? !(subtask.isCompleted ?? subtask.completed) : (subtask.isCompleted ?? subtask.completed);
      return {
        ...subtask,
        isCompleted: toggled,
        completed: toggled, // keep both keys for compatibility
      };
    });

    try {
      const response = await axiosInstance.put(
        API_PATHS.TASKS.UPDATE_CHECKLIST(task._id),
        { subTasks: newSubTasks }
      );
      toast.success("Checklist updated!");
      // update local state immediately for optimistic UI
      setSubTasks(newSubTasks);

      if (onTaskUpdate) {
        const updatedTask = response.data && response.data.task ? response.data.task : { ...task, subTasks: newSubTasks };
        onTaskUpdate(updatedTask);
      }
    } catch (error) {
      console.error("Failed to update checklist", error);
      toast.error("Failed to update checklist. Please try again.");
    }
  };



  if (!task) return null;

  const renderPriorityBadge = (priority) => {
    const colors = {
      high: "bg-red-100 text-red-800",
      medium: "bg-yellow-100 text-yellow-800",
      low: "bg-green-100 text-green-800",
    };
    return (
      <span className={`px-2 py-1 rounded-full text-xs font-medium ${colors[priority.toLowerCase()]}`}>
        {priority.charAt(0).toUpperCase() + priority.slice(1)}
      </span>
    );
  };

  const renderProgressBar = (todoList) => {
    if (!todoList || todoList.length === 0) return null;
    const completedTasks = todoList.filter(todo => todo.isCompleted ?? todo.completed).length;
    const totalTasks = todoList.length;
    const percentage = Math.round((completedTasks / totalTasks) * 100);
    return (
      <div>
        <div className="flex justify-between mb-1">
          <span className="text-sm font-medium text-gray-700">Progress</span>
          <span className="text-sm font-medium text-gray-700">{percentage}%</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2.5">
          <div className="bg-blue-600 h-2.5 rounded-full" style={{ width: `${percentage}%` }}></div>
        </div>
      </div>
    );
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="3xl">
      <div className="p-4">
        {
          isLoading ? (
            <div className="p-4">
              <LoadingSkeleton variant="details" />
            </div>
          ) : (
            <React.Fragment>
              <div className="flex justify-between items-start mb-4">
                <div>
                  <h3 className="text-2xl font-bold text-gray-800">{task.title}</h3>
                  {task.dueDate && (
                    <p className="text-sm text-gray-500 mt-1">Due: {formatDate(task.dueDate)}</p>
                  )}
                </div>
                {renderPriorityBadge(task.priority)}
              </div>

              <div className="space-y-6">
                <div>
                  <h4 className="font-semibold text-gray-700 mb-2">Description</h4>
                  <p className="text-gray-600 whitespace-pre-wrap">{task.description || "No description provided."}</p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    {task.assignedTo && task.assignedTo.length > 0 && (
                      <div>
                        <h4 className="font-semibold text-gray-700 mb-2">Assigned To</h4>
                        <AvatarGroup avatars={task.assignedTo} size="md" />
                      </div>
                    )}
                  </div>
                  <div>
                    {renderProgressBar(subTasks)}
                  </div>
                </div>

                {subTasks && subTasks.length > 0 && (
                  <div>
                    <h4 className="font-semibold text-gray-700 mb-3">Checklist</h4>
                    <div className="space-y-3">
                      {subTasks.map((todo, index) => (
                        <div key={todo._id || index} className="flex items-center bg-gray-50 p-2 rounded-md">
                          <input 
                            type="checkbox" 
                            checked={todo.isCompleted ?? todo.completed ?? false} 
                            onChange={() => handleChecklistChange(index)}
                            disabled={!isEditing}
                            className={`h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 ${!isEditing ? 'cursor-not-allowed' : 'cursor-pointer'}`}
                          />
                          <label htmlFor={`todo-${index}`} className={`ml-3 text-sm text-gray-700 ${
                            (todo.isCompleted ?? todo.completed) ? "line-through text-gray-500" : ""
                          }`}
                          >
                            {todo.title}
                          </label>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {task.attachments && task.attachments.length > 0 && (
                  <div>
                    <h4 className="font-semibold text-gray-700 mb-3">Attachments</h4>
                    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
                      {task.attachments.map((attachment, index) => {
                        const previewUrl = getAttachmentPreviewUrl(attachment);
                        const isImage = (attachment.mimeType && attachment.mimeType.startsWith('image/')) || /\.(jpg|jpeg|png|gif|webp)$/i.test(attachment.url || '');
                        const isPdf = (attachment.mimeType === 'application/pdf') || /\.pdf$/i.test(attachment.url || '');

                        return (
                          <button
                            key={index}
                            type="button"
                            onClick={() => setSelectedAttachment({ ...attachment, previewUrl, isImage, isPdf })}
                            className="group relative aspect-square bg-gray-100 dark:bg-gray-700 rounded-lg overflow-hidden transition-transform transform hover:scale-105 focus:outline-none"
                            title={attachment.name}
                          >
                            {isImage ? (
                              <ProxyImage
                                src={previewUrl}
                                alt={attachment.name}
                                className="w-full h-full object-cover"
                                containerClassName="w-full h-full"
                              />
                            ) : isPdf ? (
                              <iframe
                                src={previewUrl}
                                title={attachment.name}
                                className="w-full h-full object-cover bg-white"
                                style={{ minHeight: 0, minWidth: 0, border: 'none' }}
                              />
                            ) : (
                              <div className="w-full h-full flex flex-col items-center justify-center p-2">
                                <FaFileAlt className="text-4xl text-gray-400 dark:text-gray-500 mb-2" />
                                <span className="text-xs text-center text-gray-600 dark:text-gray-300 truncate">
                                  {attachment.name}
                                </span>
                              </div>
                            )}
                            <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-colors" />
                          </button>
                        );
                      })}
                    </div>
                    {/* Large Preview Modal */}
                    {selectedAttachment && (
                      <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-60">
                        <div className="bg-white dark:bg-gray-900 rounded-lg shadow-lg max-w-full max-h-full p-4 relative flex flex-col items-center">
                          <button
                            className="absolute top-2 right-2 text-gray-700 dark:text-gray-200 bg-gray-200 dark:bg-gray-700 rounded-full p-1 hover:bg-gray-300 dark:hover:bg-gray-600 transition"
                            onClick={() => setSelectedAttachment(null)}
                            aria-label="Close"
                          >
                            ✕
                          </button>
                          <div className="max-w-[80vw] max-h-[80vh] flex flex-col items-center justify-center">
                            {selectedAttachment.isImage ? (
                              <img
                                src={selectedAttachment.previewUrl}
                                alt={selectedAttachment.name}
                                className="max-w-full max-h-[70vh] rounded"
                              />
                            ) : selectedAttachment.isPdf ? (
                              <iframe
                                src={selectedAttachment.previewUrl}
                                title={selectedAttachment.name}
                                className="w-[70vw] h-[70vh] rounded border bg-white"
                                style={{ background: '#fff' }}
                              />
                            ) : (
                              <div className="flex flex-col items-center">
                                <FaFileAlt className="text-6xl text-gray-400 dark:text-gray-500 mb-4" />
                                <span className="text-lg font-semibold mb-2">{selectedAttachment.name}</span>
                                <a
                                  href={selectedAttachment.previewUrl}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="text-blue-600 underline"
                                >
                                  Open or Download
                                </a>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>

              <div className="mt-8 pt-4 border-t flex justify-between items-center">
                <div className="text-xs text-gray-500">
                  <p>Created: {formatDate(task.createdAt)}</p>
                  <p>Last updated: {formatDate(task.updatedAt)}</p>
                </div>
                <button
                  onClick={onEdit}
                  className="px-5 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-semibold"
                >
                  Edit Task
                </button>
              </div>
            </React.Fragment>
          )
        }
      </div>
    </Modal>
  );
};

export default TaskDetailModal;
