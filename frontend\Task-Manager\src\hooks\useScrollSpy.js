import { useState, useEffect } from 'react';

/**
 * Custom hook for tracking active section based on scroll position
 * @param {Object} options - Hook options
 * @param {Object} options.sectionRefs - Object containing refs for sections to track
 * @param {number} options.offset - Offset from the top of the viewport to consider a section active
 * @returns {string} - ID of the currently active section
 */
const useScrollSpy = ({ sectionRefs, offset = 100 }) => {
  const [activeSection, setActiveSection] = useState('');

  useEffect(() => {
    const handleScroll = () => {
      // Get current scroll position
      const scrollPosition = window.scrollY + offset;
      
      // Find the current active section
      let currentSection = '';
      
      Object.entries(sectionRefs).forEach(([id, ref]) => {
        if (ref.current && ref.current.offsetTop <= scrollPosition) {
          currentSection = id;
        }
      });
      
      // If no section is found and we're at the top, default to the first section
      if (!currentSection && sectionRefs.hero?.current && window.scrollY < sectionRefs.hero.current.offsetTop) {
        currentSection = 'hero';
      }
      
      // Update active section if it changed
      if (currentSection !== activeSection) {
        setActiveSection(currentSection);
      }
    };

    // Add scroll event listener
    window.addEventListener('scroll', handleScroll);
    
    // Initial call to set active section on mount
    handleScroll();
    
    // Clean up event listener on unmount
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [sectionRefs, activeSection, offset]);

  return activeSection;
};

export default useScrollSpy;
