/**
 * Email validation function
 * @param {string} email - The email to validate
 * @returns {boolean} - True if email is valid, false otherwise
 */
export const validateEmail = (email) => {
  if (!email) return false;
  
  // Regular expression for email validation
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * Password validation function with detailed validation
 * @param {string} password - The password to validate
 * @param {number} minLength - Minimum length required (default: 8)
 * @returns {object} - { isValid: boolean, message: string }
 */
export const validatePassword = (password, minLength = 8) => {
  // Check if password exists
  if (!password) {
    return { isValid: false, message: "Password is required" };
  }
  
  // Check minimum length
  if (password.length < minLength) {
    return { 
      isValid: false, 
      message: `Password must be at least ${minLength} characters long` 
    };
  }
  
  // Check for at least one uppercase letter
  if (!/[A-Z]/.test(password)) {
    return { 
      isValid: false, 
      message: "Password must contain at least one uppercase letter" 
    };
  }
  
  // Check for at least one lowercase letter
  if (!/[a-z]/.test(password)) {
    return { 
      isValid: false, 
      message: "Password must contain at least one lowercase letter" 
    };
  }
  
  // Check for at least one number
  if (!/\d/.test(password)) {
    return { 
      isValid: false, 
      message: "Password must contain at least one number" 
    };
  }
  
  // Check for at least one special character
  if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
    return { 
      isValid: false, 
      message: "Password must contain at least one special character" 
    };
  }
  
  // If all validations pass
  return { 
    isValid: true, 
    message: "Password is valid" 
  };
};







export const addThousandsSeparator = (num) => {
  if (num === null || isNaN(num)) return "";

  const [integerPart, fractionalPart] = num.toString().split(".");
  const formattedInteger = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ",");

  return fractionalPart
    ? `${formattedInteger}.${fractionalPart}`
    : formattedInteger;
};