import { toast } from 'react-toastify';

/**
 * Error types for frontend application
 */
export const ErrorTypes = {
  NETWORK_ERROR: 'NETWORK_ERROR',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  AUTHENTICATION_ERROR: 'AUTHENTICATION_ERROR',
  AUTHORIZATION_ERROR: 'AUTHORIZATION_ERROR',
  NOT_FOUND_ERROR: 'NOT_FOUND_ERROR',
  API_ERROR: 'API_ERROR',
  UNKNOWN_ERROR: 'UNKNOWN_ERROR'
};

/**
 * Error handler class for frontend application
 */
class ErrorService {
  /**
   * Handle API errors
   * @param {Error} error - The error object
   * @param {Object} options - Additional options
   * @returns {void}
   */
  static handleApiError(error, options = {}) {
    const {
      showToast = true,
      redirectTo = null,
      customMessage = null
    } = options;

    // Get error details
    const errorMessage = this.getErrorMessage(error);
    const errorType = this.getErrorType(error);

    // Log error
    console.error(`[${errorType}] ${errorMessage}`, error);

    // Show toast if enabled
    if (showToast) {
      toast.error(customMessage || errorMessage);
    }

    // Handle specific error types
    switch (errorType) {
      case ErrorTypes.AUTHENTICATION_ERROR:
        this.handleAuthError(redirectTo);
        break;
      case ErrorTypes.AUTHORIZATION_ERROR:
        this.handleAuthError(redirectTo);
        break;
      case ErrorTypes.NETWORK_ERROR:
        this.handleNetworkError();
        break;
      default:
        // Handle unknown errors
        break;
    }
  }

  /**
   * Get error message from error object
   * @param {Error} error - The error object
   * @returns {string} - Error message
   */
  static getErrorMessage(error) {
    if (error.response?.data?.message) {
      return error.response.data.message;
    }
    if (error.message) {
      return error.message;
    }
    return 'An unexpected error occurred';
  }

  /**
   * Get error type from error object
   * @param {Error} error - The error object
   * @returns {string} - Error type
   */
  static getErrorType(error) {
    if (error.response) {
      const status = error.response.status;
      if (status === 401) return ErrorTypes.AUTHENTICATION_ERROR;
      if (status === 403) return ErrorTypes.AUTHORIZATION_ERROR;
      if (status === 404) return ErrorTypes.NOT_FOUND_ERROR;
      if (status === 422) return ErrorTypes.VALIDATION_ERROR;
      return ErrorTypes.API_ERROR;
    }
    if (error.message?.includes('Network Error')) {
      return ErrorTypes.NETWORK_ERROR;
    }
    return ErrorTypes.UNKNOWN_ERROR;
  }

  /**
   * Handle authentication errors
   * @param {string} redirectTo - Redirect path
   */
  static handleAuthError(redirectTo) {
    // Clear auth data
    localStorage.removeItem('token');
    localStorage.removeItem('user');

    // Redirect if path provided
    if (redirectTo) {
      window.location.href = redirectTo;
    }
  }

  /**
   * Handle network errors
   */
  static handleNetworkError() {
    toast.error('Network error. Please check your internet connection.');
  }

  /**
   * Create a custom error
   * @param {string} message - Error message
   * @param {string} type - Error type
   * @returns {Error} - Custom error object
   */
  static createError(message, type = ErrorTypes.UNKNOWN_ERROR) {
    const error = new Error(message);
    error.type = type;
    return error;
  }
}

export default ErrorService; 