import React, { useEffect, useState, useCallback } from 'react';
import { useSocket } from '../../contexts/SocketContext';
import { useNavigate } from 'react-router-dom';
import moment from 'moment';
import { LuArrowRight, LuCalendar, LuClock, LuFlag } from 'react-icons/lu';

// Hooks
import { useUser } from '../../contexts/userContext';
import axiosInstance from '../../utils/axiosInstance';
import toast from 'react-hot-toast';

// Components
import DashboardLayout from '../../components/layout/DashboardLayout';
import InfoCard from '../../components/cards/InfoCard';
import TaskListTable from '../../components/TaskListTable';

// Utils
import { API_PATHS } from '../../utils/apiPaths';
import { formatDate, truncateString, getStatusBadgeColor, getPriorityBadgeColor } from '../../utils/uiHelpers';
import { mapTaskFromApi, getTaskStatistics } from '../../utils/taskUtils';

/**
 * User Dashboard Component
 * Displays personalized dashboard for regular users
 */
const UserDashboard = () => {
  const { socket, isConnected } = useSocket();
  const [selectedTaskId, setSelectedTaskId] = useState(null);
  const [detailModalOpen, setDetailModalOpen] = useState(false);
  const [detailTask, setDetailTask] = useState(null);
  const { user } = useUser();
  const navigate = useNavigate();

  const [dashboardData, setDashboardData] = useState({
    statistics: {
      totalTasks: 0,
      pendingTasks: 0,
      inProgressTasks: 0,
      completedTasks: 0,
      overdueTasks: 0
    },
    charts: {
      taskStatusDistribution: { Pending: 0, InProgress: 0, Completed: 0 },
      taskPriorityLevels: { Low: 0, Medium: 0, High: 0 }
    },
    recentTasks: []
  });

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Extract user ID to prevent complex expressions in dependency array
  const userId = user?._id || user?.id;

  // Fetch dashboard data - memoized to prevent unnecessary re-creation
  const fetchDashboardData = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      if (import.meta.env.DEV) {

      }

      const [summaryRes, allTasksRes] = await Promise.all([
        axiosInstance.get(API_PATHS.TASKS.GET_USER_DASHBOARD_DATA),
        axiosInstance.get(API_PATHS.TASKS.GET_ALL)
      ]);

      const response = summaryRes;

      if (import.meta.env.DEV) {

      }

      if (response?.data) {
        // Normalise tasks and recompute statistics so counts reflect real status
        // Use all tasks and keep only those assigned to the current user
        const allTasks = Array.isArray(allTasksRes.data.tasks) ? allTasksRes.data.tasks : [];
        const visibleTasks = allTasks.filter(t => t.team?.some(member => member._id === userId));
        const normalisedTasks = visibleTasks.map(mapTaskFromApi);
        const stats = getTaskStatistics(normalisedTasks);
        const statusDistribution = {
          Pending: stats.pending,
          InProgress: stats.inProgress,
          Completed: stats.completed
        };
        setDashboardData({
          ...response.data,
          statistics: {
            totalTasks: stats.total,
            pendingTasks: stats.pending,
            inProgressTasks: stats.inProgress,
            completedTasks: stats.completed,
            overdueTasks: stats.overdue
          },
          charts: {
            ...response.data.charts,
            taskStatusDistribution: statusDistribution
          },
          recentTasks: normalisedTasks.slice(0,5)
        });
      }
    } catch (error) {
      if (import.meta.env.DEV) {
        console.error('UserDashboard: Error fetching dashboard data:', error);
      }

      let errorMessage = 'Failed to load dashboard data';

      if (error.response?.status === 403) {
        errorMessage = 'You do not have permission to access dashboard data';
      } else if (error.response?.status === 404) {
        errorMessage = 'Dashboard endpoint not found';
      } else if (error.response?.status === 401) {
        errorMessage = 'Please log in again to access your dashboard';
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  // Fixed: Use extracted user ID to prevent duplicate API calls
  useEffect(() => {
    if (userId) {
      fetchDashboardData();
    }
  }, [userId, fetchDashboardData]);

  // Best Practice: Listen for real-time task update events and refetch dashboard data
  useEffect(() => {
    if (!socket || !isConnected) return;

    // Handler to refetch dashboard data
    const handleTaskEvent = () => {
      fetchDashboardData();
    };

    socket.on('taskUpdated', handleTaskEvent);
    socket.on('taskAssigned', handleTaskEvent);
    socket.on('taskDeleted', handleTaskEvent);

    // Cleanup listeners on unmount
    return () => {
      socket.off('taskUpdated', handleTaskEvent);
      socket.off('taskAssigned', handleTaskEvent);
      socket.off('taskDeleted', handleTaskEvent);
    };
  }, [socket, isConnected, fetchDashboardData]);

  // Navigation handlers
  const handleViewAllTasks = useCallback(() => {
    navigate('/user/tasks');
  }, [navigate]);

  const handleViewTask = useCallback((taskId) => {
    const task = dashboardData.recentTasks.find(t => t._id === taskId);
    setSelectedTaskId(taskId);
    setDetailTask(task);
    setDetailModalOpen(true);
  }, [dashboardData.recentTasks]);

  const handleTaskDeleted = useCallback((deletedTaskId) => {
    setDashboardData(prevData => ({
      ...prevData,
      recentTasks: prevData.recentTasks.filter(task => task._id !== deletedTaskId),
      statistics: {
        ...prevData.statistics,
        totalTasks: prevData.statistics.totalTasks - 1,
      }
    }));
    toast.success('Task removed from recent list.');
  }, []);

  // Show loading state
  if (loading) {
    return (
      <DashboardLayout>
        <div className="p-6 max-w-7xl mx-auto">
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-16 w-16 border-t-4 border-blue-500"></div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  // Show error state
  if (error) {
    return (
      <DashboardLayout>
        <div className="p-6 max-w-7xl mx-auto">
          <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
            <h3 className="text-lg font-medium text-red-800 mb-2">Error Loading Dashboard</h3>
            <p className="text-red-600 mb-4">{error}</p>
            <button
              onClick={fetchDashboardData}
              className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
            >
              Try Again
            </button>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  // Simple render without complex animations for now
  return (
    <DashboardLayout>
      <div className="p-6 max-w-7xl mx-auto">
        {/* Welcome Section */}
        <div className="mb-8 bg-gradient-to-r from-blue-600 to-indigo-700 rounded-2xl shadow-lg overflow-hidden">
          <div className="p-6 md:p-8">
            <h2 className="text-2xl md:text-3xl font-bold text-white">
              Welcome back, {user?.name || 'User'}!
            </h2>
            <p className="text-blue-100 mt-2">
              {moment().format('dddd, MMMM Do YYYY')}
            </p>

            <div className="mt-6">
              <button
                onClick={handleViewAllTasks}
                className="px-5 py-2.5 bg-white text-blue-700 rounded-lg shadow hover:bg-blue-50 transition-colors"
              >
                View My Tasks
              </button>
            </div>
          </div>
        </div>

        {/* Task Statistics Cards */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <InfoCard
            icon={<div className="bg-blue-100 p-2 rounded-lg"><LuFlag className="h-5 w-5 text-blue-600" /></div>}
            label="Total Tasks"
            value={dashboardData?.statistics?.totalTasks || 0}
            color="bg-blue-500"
          />
          <InfoCard
            icon={<div className="bg-amber-100 p-2 rounded-lg"><LuClock className="h-5 w-5 text-amber-600" /></div>}
            label="Pending Tasks"
            value={dashboardData?.statistics?.pendingTasks || 0}
            color="bg-amber-500"
          />
          <InfoCard
            icon={<div className="bg-indigo-100 p-2 rounded-lg"><LuClock className="h-5 w-5 text-indigo-600" /></div>}
            label="In Progress"
            value={dashboardData?.statistics?.inProgressTasks || 0}
            color="bg-indigo-500"
          />
          <InfoCard
            icon={<div className="bg-emerald-100 p-2 rounded-lg"><LuFlag className="h-5 w-5 text-emerald-600" /></div>}
            label="Completed"
            value={dashboardData?.statistics?.completedTasks || 0}
            color="bg-emerald-500"
          />
        </div>

        {/* Recent Tasks Section */}
        <div className="bg-white rounded-xl shadow-md overflow-hidden">
          <div className="flex items-center justify-between p-6 border-b border-gray-100">
            <h3 className="text-lg font-semibold text-gray-800">My Recent Tasks</h3>

            <button
              onClick={handleViewAllTasks}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors inline-flex items-center"
            >
              View All Tasks
              <LuArrowRight className="ml-1 h-4 w-4" />
            </button>
          </div>
          <div className="p-4">
            <TaskListTable 
              tableData={dashboardData.recentTasks}
              onTaskDeleted={handleTaskDeleted}
              onTaskClick={handleViewTask}
              selectedTaskId={selectedTaskId}
            />
          </div>
        </div>
      </div>
      {/* Task Detail Modal */}
      {detailModalOpen && detailTask && (
        <TaskDetailModal 
          isOpen={detailModalOpen}
          onClose={() => { setDetailModalOpen(false); setSelectedTaskId(null); setDetailTask(null); }}
          task={detailTask}
          onEdit={() => {}}
        />
      )}
    </DashboardLayout>
  );
};

export default React.memo(UserDashboard);