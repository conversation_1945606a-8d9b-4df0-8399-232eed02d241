import { useEffect } from "react";
import { BrowserRouter as Router, Routes, Route, Navigate } from "react-router-dom";
import AOS from "aos";
import "aos/dist/aos.css";
import { Toaster } from "react-hot-toast";

// Context provider
import UserProvider, { useUser } from "./contexts/userContext";
import { NotificationProvider } from "./contexts/NotificationContext";
import { SocketProvider } from "./contexts/SocketContext";
import AuthenticatedProviders from "./components/providers/AuthenticatedProviders";
import ErrorBoundary from "./components/common/ErrorBoundary";

// Route guards
import PrivateRoute from "./routes/PrivateRoute";
import PublicRoute from "./routes/PublicRoute";

// Public pages
import LandingPage from "./pages/LandingPage";
import Login from "./pages/Auth/Login";
import SignUp from "./pages/Auth/Signup";

// Admin pages
import Dashboard from "./pages/Admin/Dashboard";
import ManageTasks from "./pages/Admin/ManageTasks";
import CreateTask from "./pages/Admin/CreateTask";
import ManageUsers from "./pages/Admin/ManageUsers";

// User pages
import UserDashboard from "./pages/User/UserDashboard";
import MyTasks from "./pages/User/MyTasks";
import ViewTaskDetails from "./pages/User/ViewTaskDetails";
import UserEditTask from "./pages/User/EditTask";
import EditTask from "./pages/Admin/EditTask";

// Shared pages
import ProfileSettings from "./pages/ProfileSettings";
import Chat from "./pages/Chat";

// Toast configuration
const toastConfig = {
  duration: 4000,
  style: {
    background: "#fff",
    color: "#333",
  },
  success: {
    duration: 3000,
    iconTheme: {
      primary: "#10B981",
      secondary: "#fff",
    },
  },
  error: {
    duration: 4000,
    iconTheme: {
      primary: "#EF4444",
      secondary: "#fff",
    },
  },
};

const App = () => {
  // Initialize AOS once when app starts
  useEffect(() => {
    AOS.init({
      duration: 800,
      easing: "ease-in-out",
      once: true,
      mirror: false,
    });
  }, []);

  return (
    <ErrorBoundary>
      <Router>
        <UserProvider>
          <SocketProvider>
            <NotificationProvider>
              <Toaster toastOptions={toastConfig} />
              <AppRoutes />
            </NotificationProvider>
          </SocketProvider>
        </UserProvider>
      </Router>
    </ErrorBoundary>
  );
};

// This component ensures that the rest of the app (including SocketProvider)
// only renders AFTER the user's authentication status has been determined.
const AppRoutes = () => {
  const { user, loading } = useUser();

  // Check if token exists in localStorage
  const token = localStorage.getItem('token');

  // Show a loading screen while user is being authenticated.
  if (loading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin rounded-full h-16 w-16 border-t-4 border-blue-500"></div>
      </div>
    );
  }

  // If user is authenticated but token is missing or invalid, show an error
  if (user && !token) {
    return (
      <div className="flex flex-col justify-center items-center h-screen bg-red-50">
        <div className="text-red-500 text-xl mb-4">Authentication Error</div>
        <p className="text-gray-700 mb-4">Your session token is missing or invalid.</p>
        <button
          onClick={() => window.location.href = '/login'}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Return to Login
        </button>
      </div>
    );
  }

  return (
    <Routes>
      {/* Public Routes */}
      <Route path="/" element={<PublicRoute component={LandingPage} />} />
      <Route path="/login" element={<PublicRoute component={Login} />} />
      <Route path="/signup" element={<PublicRoute component={SignUp} />} />

      {/* Admin Routes */}
      <Route element={<PrivateRoute allowedRoles={["admin"]} />}>
        <Route path="/admin/dashboard" element={
          <AuthenticatedProviders>
            <Dashboard />
          </AuthenticatedProviders>
        } />
        <Route path="/admin/tasks" element={
          <AuthenticatedProviders>
            <ManageTasks />
          </AuthenticatedProviders>
        } />
        <Route path="/admin/create-task" element={
          <AuthenticatedProviders>
            <CreateTask />
          </AuthenticatedProviders>
        } />
        <Route path="/admin/task/:id" element={
          <AuthenticatedProviders>
            <EditTask />
          </AuthenticatedProviders>
        } />
        <Route path="/admin/edit-task/:id" element={
          <AuthenticatedProviders>
            <EditTask />
          </AuthenticatedProviders>
        } />
        <Route path="/admin/users" element={
          <AuthenticatedProviders>
            <ManageUsers />
          </AuthenticatedProviders>
        } />
      </Route>

      {/* User Routes */}
      <Route element={<PrivateRoute allowedRoles={["user"]} />}>
        <Route path="/user/dashboard" element={
          <AuthenticatedProviders>
            <UserDashboard />
          </AuthenticatedProviders>
        } />
        <Route path="/user/tasks" element={
          <AuthenticatedProviders>
            <MyTasks />
          </AuthenticatedProviders>
        } />
        <Route path="/user/view-task-details/:id" element={
          <AuthenticatedProviders>
            <ViewTaskDetails />
          </AuthenticatedProviders>
        } />
        <Route path="/user/edit-task/:id" element={
          <AuthenticatedProviders>
            <UserEditTask />
          </AuthenticatedProviders>
        } />
      </Route>

      {/* Shared Routes */}
      <Route element={<PrivateRoute allowedRoles={["admin", "user"]} />}>
        <Route path="/profile-settings" element={
          <AuthenticatedProviders>
            <ProfileSettings />
          </AuthenticatedProviders>
        } />
        <Route path="/chat" element={
          <AuthenticatedProviders>
            <Chat />
          </AuthenticatedProviders>
        } />
        <Route path="/chat-ui" element={<Navigate to="/chat" replace />} />
      </Route>

      {/* Fallback Route */}
      <Route path="*" element={<Navigate to={user ? (user.role === 'admin' ? '/admin/dashboard' : '/user/dashboard') : '/login'} replace />} />
    </Routes>
  );
};

export default App;
