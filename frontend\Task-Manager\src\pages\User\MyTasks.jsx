import React, { useState, useEffect, useCallback, useMemo } from 'react';

import DashboardLayout from '../../components/layout/DashboardLayout';
import axiosInstance from '../../utils/axiosInstance';
import { API_PATHS } from '../../utils/apiPaths';
import { LuFileSpreadsheet } from 'react-icons/lu';
import { FiAlertTriangle, FiServer, FiWifiOff, FiRefreshCw, FiLock } from 'react-icons/fi';
import TaskStatusTabs from '../../components/TaskStatusTabs';
import TaskCard from '../../components/cards/TaskCard';
import TaskDetailModal from '../../components/modals/TaskDetailModal';
import ErrorBoundary from '../../components/common/ErrorBoundary';
import LoadingSkeleton from '../../components/common/LoadingSkeleton';
import toast from 'react-hot-toast';
import { createStatusTabs, createDummyTabs, filterTasksByStatus, mapTaskFromApi } from '../../utils/taskUtils';
import { useUser } from '../../contexts/userContext';
import { useSocket } from '../../contexts/SocketContext';

const EnhancedErrorDisplay = ({ error, resetError }) => {
  const isNetworkError = error?.toLowerCase().includes('network') || error?.toLowerCase().includes('connection');
  const isAuthError = error?.toLowerCase().includes('auth') || error?.toLowerCase().includes('permission');
  const isServerError = error?.toLowerCase().includes('server');

  return (
    <div className="bg-white border border-gray-200 rounded-lg shadow-sm p-6 col-span-full">
      <div className="flex items-start space-x-4">
        <div className="flex-shrink-0 bg-red-50 p-3 rounded-full">
          {isNetworkError ? <FiWifiOff className="h-6 w-6 text-red-500" /> : isAuthError ? <FiLock className="h-6 w-6 text-red-500" /> : isServerError ? <FiServer className="h-6 w-6 text-red-500" /> : <FiAlertTriangle className="h-6 w-6 text-red-500" />}
        </div>
        <div className="flex-1">
          <h3 className="text-lg font-medium text-gray-900 mb-2">Failed to Load Tasks</h3>
          <p className="text-sm text-gray-600 mb-4">{error}</p>
          <div className="flex space-x-3">
            <button onClick={resetError} className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
              <FiRefreshCw className="mr-2 h-4 w-4" />
              Try Again
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

const MyTasks = () => {
  const [allTasks, setAllTasks] = useState([]);
  const [tabs, setTabs] = useState([]);
  const [filterStatus, setFilterStatus] = useState("All");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [selectedTask, setSelectedTask] = useState(null);
  const [isTaskDetailModalOpen, setIsTaskDetailModalOpen] = useState(false);
  const [isFetchingDetails, setIsFetchingDetails] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const { user } = useUser();

  const getAllTasks = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await axiosInstance.get(API_PATHS.TASKS.GET_ALL);
      const tasks = Array.isArray(response.data.tasks)
        ? response.data.tasks.map(mapTaskFromApi)
        : [];
      setAllTasks(tasks);

      // Generate status tabs based on the tasks that are actually visible to the current
      // user. Using the backend's global summary caused the counts to be incorrect when
      // the user could only see a subset of tasks.
      const statusTabs = createStatusTabs(tasks);
      setTabs(statusTabs);
    } catch (err) {
      const errorMessage = err.response?.data?.message || err.message || "An unknown error occurred";
      setError(errorMessage);
      setTabs(createDummyTabs());
    } finally {
      setIsLoading(false);
    }
  }, [user]);

  useEffect(() => {
    if (user?._id) {
        getAllTasks();
    }
  }, [getAllTasks, user]);

  const handleStatusChange = (status) => {
    setFilterStatus(status);
  };

  const handleClick = async (task) => {
    setIsTaskDetailModalOpen(true);
    setSelectedTask(task);
    setIsEditing(false);
    setIsFetchingDetails(true);
    try {
      const response = await axiosInstance.get(API_PATHS.TASKS.GET_BY_ID(task._id));
      setSelectedTask(response.data.task);
    } catch (error) {
      console.error("Failed to load task details:", error);
      toast.error("Failed to load task details.");
      setIsTaskDetailModalOpen(false);
    } finally {
      setIsFetchingDetails(false);
    }
  };

  const handleTaskUpdate = useCallback((updatedTaskRaw) => {
    // Normalize the task from API/socket to match frontend expectations
    const updatedTask = updatedTaskRaw ? mapTaskFromApi(updatedTaskRaw) : null;
    if (updatedTask && updatedTask._id) {
      setAllTasks((prevTasks) =>
        prevTasks.map((task) =>
          task && task._id === updatedTask._id ? updatedTask : task
        )
      );
      // Also update the selected task if it's the one being updated
      setSelectedTask((prevSelectedTask) => {
        if (prevSelectedTask && prevSelectedTask._id === updatedTask._id) {
          return updatedTask;
        }
        return prevSelectedTask;
      });
    } else {
      // Fallback to refetching all tasks if no specific task is provided
      getAllTasks();
    }
  }, [getAllTasks]);

  // Effect to listen for real-time task updates
  const handleTaskAssigned = useCallback((newTask) => {
    const normalizedTask = mapTaskFromApi(newTask);
    setAllTasks((prevTasks) => {
      if (prevTasks.find((task) => task._id === normalizedTask._id)) {
        return prevTasks;
      }
      return [normalizedTask, ...prevTasks];
    });
  }, []);

  const handleTaskDeleted = useCallback(({ taskId }) => {
    setAllTasks((prevTasks) => prevTasks.filter((task) => task._id !== taskId));
  }, []);

  // Effect to listen for real-time task updates via the global event system
  const { onTaskAssigned, onTaskUpdated, onTaskDeleted } = useSocket();

  useEffect(() => {
    const unSubAssigned = onTaskAssigned(handleTaskAssigned);
    const unSubUpdated = onTaskUpdated(handleTaskUpdate);
    const unSubDeleted = onTaskDeleted(handleTaskDeleted);

    return () => {
      unSubAssigned();
      unSubUpdated();
      unSubDeleted();
    };
  }, [onTaskAssigned, onTaskUpdated, onTaskDeleted, handleTaskAssigned, handleTaskUpdate, handleTaskDeleted]);

  const handleCloseTaskDetailModal = useCallback(() => {
    setIsTaskDetailModalOpen(false);
    // Keep selectedTask available for a moment for smoother transitions
    // setSelectedTask(null);
    setIsEditing(false);
  }, []);

  const handleEnterEditMode = () => {
    setIsEditing(true);
  };

  const filteredTasks = useMemo(() => {
    return filterTasksByStatus(allTasks, filterStatus);
  }, [allTasks, filterStatus]);

  return (
    <DashboardLayout activeMenu="My Tasks">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold">My Tasks</h1>
        <button onClick={getAllTasks} className="flex items-center px-4 py-2 bg-white border border-gray-300 rounded-lg shadow-sm hover:bg-gray-50 text-sm font-medium text-gray-700">
          <FiRefreshCw className="mr-2 h-4 w-4" />
          Refresh Tasks
        </button>
      </div>

      <div className="mb-6">
        <TaskStatusTabs tabs={tabs} activeTab={filterStatus} onTabChange={handleStatusChange} />
      </div>

      <ErrorBoundary fallback={(error, reset) => <EnhancedErrorDisplay error={error} resetError={reset} />}>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {isLoading ? (
            <LoadingSkeleton count={8} />
          ) : error ? (
            <EnhancedErrorDisplay error={error} resetError={getAllTasks} />
          ) : filteredTasks.length > 0 ? (
            filteredTasks.map((task) => (
              <TaskCard key={task._id} task={task} onClick={() => handleClick(task)} />
            ))
          ) : (
            <div className="col-span-full text-center py-12 bg-white rounded-lg shadow-sm">
              <LuFileSpreadsheet className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No Tasks Found</h3>
              <p className="mt-1 text-sm text-gray-500">No tasks match the current filter.</p>
            </div>
          )}
        </div>
      </ErrorBoundary>

      {isTaskDetailModalOpen && (
        <TaskDetailModal
          isOpen={isTaskDetailModalOpen}
          onClose={handleCloseTaskDetailModal}
          task={selectedTask}
          onEdit={handleEnterEditMode}
          isLoading={isFetchingDetails}
          onTaskUpdate={handleTaskUpdate}
          isEditing={isEditing}
        />
      )}
    </DashboardLayout>
  );
};

export default MyTasks;
