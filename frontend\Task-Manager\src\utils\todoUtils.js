// Utility function to normalize and validate todo checklist
export const normalizeAndValidateChecklist = (checklist) => {
  if (!Array.isArray(checklist)) return [];
  const validTodos = checklist.filter(item => item.text && item.text.trim() !== '');
  const normalizedTodos = validTodos.map((item, idx) => {
    return {
      id: item.id || `todo-${Date.now()}-${idx}`,
      text: item.text || '',
      completed: !!item.completed,
    };
  });

  return normalizedTodos;
};
