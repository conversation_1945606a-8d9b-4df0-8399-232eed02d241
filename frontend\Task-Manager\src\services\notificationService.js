import axiosInstance from '../utils/axiosInstance';

const notificationService = {
  // Get all notifications for the current user
  getNotifications: async (params = {}) => {
    try {
      const { page = 1, limit = 20, unreadOnly = false } = params;
      const response = await axiosInstance.get('/api/notifications', {
        params: { page, limit, unreadOnly }
      });
      
      if (response.data.success) {
        return {
          success: true,
          notifications: response.data.notifications || [],
          pagination: response.data.pagination || {},
          unreadCount: response.data.unreadCount || 0
        };
      }
      
      return { success: false, error: 'Failed to fetch notifications' };
    } catch (error) {
      return { 
        success: false, 
        error: error.response?.data?.message || error.message || 'Failed to fetch notifications' 
      };
    }
  },

  // Get unread notification count
  getUnreadCount: async () => {
    try {
      const response = await axiosInstance.get('/api/notifications/unread-count');
      
      if (response.data.success) {
        return {
          success: true,
          unreadCount: response.data.unreadCount || 0
        };
      }
      
      return { success: false, error: 'Failed to fetch unread count' };
    } catch (error) {
      return { 
        success: false, 
        error: error.response?.data?.message || error.message || 'Failed to fetch unread count' 
      };
    }
  },

  // Mark notification as read
  markAsRead: async (notificationId) => {
    try {
      const response = await axiosInstance.put(`/api/notifications/${notificationId}/read`);
      
      if (response.data.success) {
        return {
          success: true,
          notification: response.data.notification
        };
      }
      
      return { success: false, error: 'Failed to mark notification as read' };
    } catch (error) {
      return { 
        success: false, 
        error: error.response?.data?.message || error.message || 'Failed to mark notification as read' 
      };
    }
  },

  // Mark all notifications as read
  markAllAsRead: async () => {
    try {
      const response = await axiosInstance.put('/api/notifications/mark-all-read');
      
      if (response.data.success) {
        return { success: true };
      }
      
      return { success: false, error: 'Failed to mark all notifications as read' };
    } catch (error) {
      return { 
        success: false, 
        error: error.response?.data?.message || error.message || 'Failed to mark all notifications as read' 
      };
    }
  },

  // Delete notification
  deleteNotification: async (notificationId) => {
    try {
      const response = await axiosInstance.delete(`/api/notifications/${notificationId}`);
      
      if (response.data.success) {
        return { success: true };
      }
      
      return { success: false, error: 'Failed to delete notification' };
    } catch (error) {
      return { 
        success: false, 
        error: error.response?.data?.message || error.message || 'Failed to delete notification' 
      };
    }
  },

  // Clear all notifications
  clearAllNotifications: async () => {
    try {
      const response = await axiosInstance.delete('/api/notifications/clear-all');
      
      if (response.data.success) {
        return { 
          success: true,
          message: response.data.message
        };
      }
      
      return { success: false, error: 'Failed to clear all notifications' };
    } catch (error) {
      return { 
        success: false, 
        error: error.response?.data?.message || error.message || 'Failed to clear all notifications' 
      };
    }
  }
};

export default notificationService;
