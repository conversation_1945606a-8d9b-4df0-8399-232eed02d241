// Automated script to test users pagination (ESM)
import axios from 'axios';
import { CookieJar } from 'tough-cookie';
import { wrapper } from 'axios-cookiejar-support';
import { BASE_URL, ADMIN_INVITE_TOKEN } from './test-helpers.mjs';
const ADMIN_USER = {
  email: `paginationadmin${Date.now()}@example.com`,
  password: 'TestAdminUser123',
  name: `Pagination Admin User ${Date.now()}`
};
const NUM_TEST_USERS = 5;

async function main() {
  const jar = new CookieJar();
  const client = wrapper(axios.create({ baseURL: BASE_URL, jar, withCredentials: true }));
  let createdUserIds = [];
  let accessToken = null; // Declare accessToken in the outer scope

  try {
    // 1. Register and login as an admin user
    let res = await client.get('/auth/csrf-token');
    let csrfToken = res.data.csrfToken;
    await client.post('/auth/register', { ...ADMIN_USER, adminInviteToken: ADMIN_INVITE_TOKEN }, { headers: { 'x-csrf-token': csrfToken } });
    console.log('Registered new admin test user.');

    res = await client.get('/auth/csrf-token');
    csrfToken = res.data.csrfToken;
    const loginRes = await client.post('/auth/login', { email: ADMIN_USER.email, password: ADMIN_USER.password }, { headers: { 'x-csrf-token': csrfToken } });
    console.log('Logged in as admin user.');

    const cookiesAfterLogin = await jar.getCookies(BASE_URL);
    const accessTokenCookie = cookiesAfterLogin.find(c => c.key === 'accessToken');
    if (!accessTokenCookie) throw new Error('accessToken cookie missing after login');
    accessToken = accessTokenCookie.value; // Assign to the outer scope variable

    // 2. Create additional users to test pagination
    for (let i = 1; i <= NUM_TEST_USERS; i++) {
      res = await client.get('/auth/csrf-token');
      csrfToken = res.data.csrfToken;
      const newUser = {
        email: `testuser${Date.now()}${i}@example.com`,
        password: 'TestUser123',
        name: `Test User ${i}`
      };
      const registerRes = await client.post('/auth/register', newUser, { headers: { 'x-csrf-token': csrfToken } });
      createdUserIds.push(registerRes.data.id); // Use 'id' instead of '_id'
    }
    console.log(`Created ${NUM_TEST_USERS} additional test users.`);

    // 3. Run Pagination Tests
    // Total users should be at least NUM_TEST_USERS + 1 (the admin)

    // Page 1, limit 2
    res = await client.get('/users?page=1&limit=2', { headers: { 'Authorization': `Bearer ${accessToken}` } });
    if (res.data.data.length !== 2) throw new Error(`Page 1: Expected 2 users, got ${res.data.data.length}`);
    if (res.data.total < NUM_TEST_USERS + 1) throw new Error(`Page 1: Total is less than expected.`);
    console.log('Page 1 OK:', res.data.data.map(u => u.name));

    // Page 2, limit 2
    res = await client.get('/users?page=2&limit=2', { headers: { 'Authorization': `Bearer ${accessToken}` } });
    if (res.data.data.length !== 2) throw new Error(`Page 2: Expected 2 users, got ${res.data.data.length}`);
    console.log('Page 2 OK:', res.data.data.map(u => u.name));

    // Page 3, limit 2 (should have 1 or 2 users depending on total)
    res = await client.get('/users?page=3&limit=2', { headers: { 'Authorization': `Bearer ${accessToken}` } });
    const expectedCount = (NUM_TEST_USERS + 1) - 4;
    if (res.data.data.length !== expectedCount) throw new Error(`Page 3: Expected ${expectedCount} users, got ${res.data.data.length}`);
    console.log('Page 3 OK:', res.data.data.map(u => u.name));

    // Last page (should be empty)
    const lastPage = res.data.totalPages + 1;
    res = await client.get(`/users?page=${lastPage}&limit=2`, { headers: { 'Authorization': `Bearer ${accessToken}` } });
    if (res.data.data.length !== 0) throw new Error(`Page ${lastPage}: Expected 0 users, got ${res.data.data.length}`);
    console.log(`Page ${lastPage} OK (empty as expected)`);

    console.log('All user pagination tests passed!');

  } catch (e) {
    if (e.response) {
      console.error('Test failed with response:', {
        status: e.response.status,
        data: e.response.data,
        config: { url: e.response.config.url, method: e.response.config.method }
      });
    } else {
      console.error('Test failed:', e.message);
    }
    process.exit(1);
  } finally {
    // 4. Cleanup: Delete all created users
    if (createdUserIds.length > 0) {
      console.log('Cleaning up created users...');
      for (const userId of createdUserIds) {
        try {
          const res = await client.get('/auth/csrf-token');
          const csrfToken = res.data.csrfToken;
          await client.delete(`/users/${userId}`, { headers: { 'Authorization': `Bearer ${accessToken}`, 'x-csrf-token': csrfToken } });
        } catch (e) {
          console.warn(`Failed to delete user ${userId}:`, e.message);
        }
      }
      console.log('Cleanup complete.');
    }
  }
}

main();
