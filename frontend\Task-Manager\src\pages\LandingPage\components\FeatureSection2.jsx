import React from 'react';

/**
 * Second feature section component for the landing page
 * @returns {JSX.Element} - Rendered component
 */
const FeatureSection2 = () => {
  const features = [
    {
      title: "Unlimited Components",
      description: "Clarity gives you the blocks & components you need to create a website",
      delay: "100"
    },
    {
      title: "Build Website",
      description: "Amet minim mollit non deserunt ullamco est sit aliqua dolor do amet sint.",
      delay: "200"
    },
    {
      title: "Easy Analytics",
      description: "Amet minim mollit non deserunt ullamco est sit aliqua dolor do amet sint.",
      delay: "300"
    },
    {
      title: "Release Fast",
      description: "Amet minim mollit non deserunt ullamco est sit aliqua dolor do amet sint.",
      delay: "400"
    }
  ];

  return (
    <section className="relative py-16 md:py-24 px-4 sm:px-6 md:px-8 bg-gradient-to-b from-white to-teal-50/10 overflow-hidden">
      <div className="relative container mx-auto text-center max-w-4xl" data-aos="fade-up" data-aos-duration="800">
        {/* Extremely subtle background gradient */}
        <div aria-hidden="true" className="absolute inset-x-0 bottom-0 h-1/3 -z-10 flex justify-center items-end pointer-events-none">
          <div className="w-full max-w-3xl h-[100%] bg-teal-100/20 rounded-t-full opacity-15 blur-3xl filter"></div>
        </div>

        <span className="text-blue-600 font-semibold text-xs tracking-wider uppercase">— Features —</span>
        <h2 className="text-3xl sm:text-4xl font-bold text-slate-900 mt-2 mb-12 sm:mb-16 leading-tight">
          Unlimited options give you <br className="hidden sm:block" /> the ultimate flexibility
        </h2>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-5 sm:gap-6">
          {features.map((feature, index) => (
            <article 
              key={index}
              className="content-card bg-white rounded-lg shadow-sm text-left p-4 sm:p-5" 
              data-aos="fade-up" 
              data-aos-delay={feature.delay}
            >
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center mt-0.5">
                  <svg className="w-3.5 h-3.5 text-white" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-slate-800 mb-1">{feature.title}</h3>
                  <p className="text-slate-500 text-xs leading-relaxed">{feature.description}</p>
                </div>
              </div>
            </article>
          ))}
        </div>
      </div>
    </section>
  );
};

export default FeatureSection2;
