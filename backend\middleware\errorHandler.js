const errorHandler = (err, req, res, next) => {
  let error = { ...err };
  error.message = err.message;

  // Contextual error logging
  const logDetails = {
    timestamp: new Date().toISOString(),
    user: req.user ? {
      id: req.user._id,
      email: req.user.email,
      role: req.user.role
    } : null,
    endpoint: req.originalUrl,
    method: req.method,
    body: req.body,
    query: req.query,
    error: {
      message: err.message,
      stack: err.stack
    }
  };
  console.error('[ERROR]', JSON.stringify(logDetails, null, 2));

  // Mongoose bad ObjectId
  if (err.name === 'CastError') {
    const message = 'Resource not found';
    error = { message, statusCode: 404 };
  }

  // Mongoose duplicate key
  if (err.code === 11000) {
    const message = 'Duplicate field value entered';
    error = { message, statusCode: 400 };
  }

  // Mongoose validation error
  if (err.name === 'ValidationError') {
    const message = Object.values(err.errors).map(val => val.message);
    error = { message, statusCode: 400 };
  }

  res.status(error.statusCode || 500).json({
    success: false,
    error:
      process.env.NODE_ENV === 'production'
        ? 'An unexpected error occurred'
        : error.message || 'Server Error'
  });
};

module.exports = errorHandler;
