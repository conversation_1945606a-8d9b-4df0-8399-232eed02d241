import React from 'react';

/**
 * Footer component for the landing page
 * @returns {JSX.Element} - Rendered component
 */
const Footer = () => {
  const currentYear = new Date().getFullYear();
  
  return (
    <footer id="about" className="bg-black text-gray-400 py-16 sm:py-20 px-6 sm:px-8 md:px-16">
      <div className="container mx-auto">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-10 sm:gap-12 mb-12 sm:mb-16" data-aos="fade-up" data-aos-duration="800">
          {/* Newsletter Signup */}
          <div className="lg:col-span-1">
            <h3 className="text-xl font-semibold text-white mb-6">Get The Latest Updates</h3>
            <form className="flex shadow-md rounded-md">
              <input 
                type="email" 
                placeholder="Enter your email" 
                required 
                className="w-full px-4 py-3 rounded-l-md text-gray-900 focus:outline-none focus:ring-1 focus:ring-cyan-500 border-0 placeholder-gray-400 text-sm"
              />
              <button 
                type="submit" 
                className="bg-cyan-500 hover:bg-cyan-600 text-white px-5 py-3 rounded-r-md font-medium transition-colors text-sm whitespace-nowrap"
              >
                Subscribe
              </button>
            </form>
          </div>
          
          {/* Services Links */}
          <div>
            <h4 className="text-base font-semibold text-white mb-4">Services</h4>
            <ul className="space-y-2 text-sm">
              <li><a href="#" className="hover:text-white transition-colors">Task Management</a></li>
              <li><a href="#" className="hover:text-white transition-colors">Team Collaboration</a></li>
              <li><a href="#" className="hover:text-white transition-colors">Project Planning</a></li>
              <li><a href="#" className="hover:text-white transition-colors">Time Tracking</a></li>
              <li><a href="#" className="hover:text-white transition-colors">Reporting & Analytics</a></li>
            </ul>
          </div>
          
          {/* About Us Links */}
          <div>
            <h4 className="text-base font-semibold text-white mb-4">About Us</h4>
            <ul className="space-y-2 text-sm">
              <li><a href="#hero" className="hover:text-white transition-colors">Home</a></li>
              <li><a href="#" className="hover:text-white transition-colors">Projects</a></li>
              <li><a href="#" className="hover:text-white transition-colors">Features</a></li>
              <li><a href="#" className="hover:text-white transition-colors">About Us</a></li>
              <li><a href="#" className="hover:text-white transition-colors">Blog</a></li>
            </ul>
          </div>
          
          {/* Resource Links */}
          <div>
            <h4 className="text-base font-semibold text-white mb-4">Resources</h4>
            <ul className="space-y-2 text-sm">
              <li><a href="#" className="hover:text-white transition-colors">Contact Us</a></li>
              <li><a href="#" className="hover:text-white transition-colors">Terms</a></li>
              <li><a href="#" className="hover:text-white transition-colors">Policy</a></li>
              <li><a href="#" className="hover:text-white transition-colors">Payments</a></li>
            </ul>
          </div>
        </div>
        
        {/* Logo and Social Links */}
        <div className="border-t border-gray-800 pt-8 pb-4" data-aos="fade-up" data-aos-duration="800" data-aos-delay="100">
          <div className="flex flex-col md:flex-row justify-between items-center gap-6">
            {/* Logo */}
            <div className="text-center md:text-left">
              <div className="text-3xl font-bold text-white tracking-tight">xerox</div>
            </div>
            
            {/* Phone */}
            <div className="text-center">
              <p className="text-base font-medium text-white">(000) 000 - 0000</p>
            </div>
            
            {/* Social Media Links */}
            <div className="flex space-x-3">
              <a href="#" aria-label="Twitter" className="w-8 h-8 bg-gray-800 rounded-full flex items-center justify-center hover:bg-gray-700 transition-colors text-white">
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"/>
                </svg>
              </a>
              <a href="#" aria-label="LinkedIn" className="w-8 h-8 bg-gray-800 rounded-full flex items-center justify-center hover:bg-gray-700 transition-colors text-white">
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z"/>
                </svg>
              </a>
              <a href="#" aria-label="Facebook" className="w-8 h-8 bg-gray-800 rounded-full flex items-center justify-center hover:bg-gray-700 transition-colors text-white">
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M9 8h-3v4h3v12h5v-12h3.642l.358-4h-4v-1.667c0-.955.192-1.333 1.115-1.333h2.885v-5h-3.808c-3.596 0-5.192 1.583-5.192 4.615v3.385z"/>
                </svg>
              </a>
            </div>
          </div>
          
          {/* Copyright and Legal Links */}
          <div className="mt-6 text-center md:text-left text-xs text-gray-500">
            <p>  Copyright @ <span id="currentYear">{currentYear}</span> xerox.com All rights reserved</p>
            <div className="mt-2 space-x-4">
              <a href="#" className="hover:text-white transition-colors">Privacy Policy</a>
              <a href="#" className="hover:text-white transition-colors">Terms & Conditions</a>
              <a href="#" className="hover:text-white transition-colors">Cookie</a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
