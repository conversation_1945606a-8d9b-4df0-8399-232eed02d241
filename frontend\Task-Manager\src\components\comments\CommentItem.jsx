import React from 'react';
import moment from 'moment';
import { LuPin, LuPinOff } from 'react-icons/lu';
import { useUserAuth } from '../../hooks/useUserAuth';

const CommentItem = ({ comment, onPinToggle, currentUserId }) => {
  const { user: commenter, content, createdAt, isPinned } = comment;
  const { user: loggedInUser } = useUserAuth();

  // Determine if the logged-in user can pin/unpin.
  // For now, let's assume only the task creator or admin can pin.
  // This logic can be adjusted based on your app's permission rules.
  const canPin = loggedInUser?.isAdmin; // Example logic

  return (
    <div className={`flex items-start space-x-3 p-3 rounded-lg ${isPinned ? 'bg-yellow-50 border border-yellow-200' : 'bg-gray-50'}`}>
      <img 
        src={commenter?.profileImageUrl || commenter?.profileImage || commenter?.avatar || '/uploads/default-avatar.png'} 
        alt={`${commenter?.name || commenter?.fullName || 'User'}'s avatar`} 
        className="w-10 h-10 rounded-full object-cover"
      />
      <div className="flex-1">
        <div className="flex items-center justify-between">
          <div>
            <span className="font-semibold text-gray-900">{commenter?.name || 'Unknown User'}</span>
            <span className="text-xs text-gray-500 ml-2">{moment(createdAt).fromNow()}</span>
          </div>
          {canPin && (
            <button onClick={() => onPinToggle(comment._id)} className="text-gray-400 hover:text-gray-600">
              {isPinned ? <LuPinOff size={16} className="text-yellow-600" /> : <LuPin size={16} />}
            </button>
          )}
        </div>
        <p className="text-gray-700 mt-1 whitespace-pre-wrap">{content}</p>
      </div>
    </div>
  );
};

export default CommentItem;
