import React from "react";
import PropTypes from "prop-types";
import { LuTrendingUp } from "react-icons/lu";

/**
 * InfoCard component for displaying statistics with customizable styling
 * 
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.icon - Icon component to display
 * @param {string} props.label - Card label/title
 * @param {string|number} props.value - Card value/statistic
 * @param {string} props.color - Color theme (Tailwind CSS class)
 * @param {string} props.trend - Optional trend indicator (up, down, neutral)
 * @param {string} props.trendValue - Optional trend value (e.g., "+15%")
 * @param {string} props.className - Additional CSS classes
 * @returns {JSX.Element} Rendered component
 */
const InfoCard = ({ 
  icon, 
  label, 
  value, 
  color = "from-blue-500 to-blue-600", 
  trend,
  trendValue,
  className = "" 
}) => {
  // Determine trend styling
  let trendIcon = null;
  let trendColorClass = "";
  
  if (trend === "up") {
    trendIcon = "↑";
    trendColorClass = "text-green-300";
  } else if (trend === "down") {
    trendIcon = "↓";
    trendColorClass = "text-red-300";
  } else if (trend === "neutral") {
    trendIcon = "→";
    trendColorClass = "text-gray-300";
  }

  return (
    <div className={`relative p-5 rounded-2xl overflow-hidden bg-gradient-to-br ${color} text-white shadow-lg transform hover:-translate-y-1 transition-transform duration-300 ease-in-out ${className}`}>
      <div className="absolute top-0 right-0 -m-4 opacity-20">
        {icon}
      </div>
      <div className="relative z-10">
        <div className="flex justify-between items-start mb-2">
          <p className="text-sm font-medium opacity-80">{label}</p>
          {trend && trendValue && (
            <p className={`text-xs font-semibold ${trendColorClass} flex items-center`}>
              {trendIcon} {trendValue}
            </p>
          )}
        </div>
        <p className="text-4xl font-bold">{value}</p>
      </div>
    </div>
  );
};

InfoCard.propTypes = {
  icon: PropTypes.node,
  label: PropTypes.string.isRequired,
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
  color: PropTypes.string,
  trend: PropTypes.oneOf(['up', 'down', 'neutral']),
  trendValue: PropTypes.string,
  className: PropTypes.string
};

export default InfoCard;
