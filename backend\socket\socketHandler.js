const { Server } = require('socket.io');
const { setSocketInstance } = require('./socketInstance');
const jwt = require('jsonwebtoken');
const User = require('../models/User');
const Conversation = require('../models/Conversation');
const Message = require('../models/Message');
const Notification = require('../models/Notification');

// Store online users with their socket IDs
const onlineUsers = new Map();
const userSockets = new Map();

const initializeSocket = (server) => {
  // Use environment variable for allowed origins
  const allowedOrigins = process.env.CLIENT_URL
    ? process.env.CLIENT_URL.split(',')
    : ['http://localhost:5174', 'http://localhost:5175', 'http://localhost:5173', 'http://localhost:3000', 'http://localhost:3001'];

  const io = new Server(server, {
    // Add a ping interval and timeout to keep connections alive
    pingInterval: 10000, // 10 seconds
    pingTimeout: 5000,   // 5 seconds
    cors: {
      origin: allowedOrigins,
      methods: ["GET", "POST"],
      credentials: true
    },
    transports: ['websocket', 'polling'],
    allowEIO3: true
  });

  // Middleware for authentication
  io.use(async (socket, next) => {
    const token = socket.handshake.auth.token;

    if (!token) {
      console.log('❌ Socket auth failed: No token provided');
      return next(new Error('Authentication error: No token provided'));
    }

    try {
      const tokenValue = token.startsWith('Bearer ') ? token.split(' ')[1] : token;
      const decoded = jwt.verify(tokenValue, process.env.JWT_SECRET);
      const userId = decoded.userId || decoded.id || decoded._id;

      if (!userId) {
        console.log('❌ Socket auth failed: Invalid token payload');
        return next(new Error('Authentication error: Invalid token payload'));
      }

      const user = await User.findById(userId).select('-password');
      if (!user) {
        console.log('❌ Socket auth failed: User not found');
        return next(new Error('Authentication error: User not found'));
      }

      // Only log successful auth in development mode
      if (process.env.NODE_ENV === 'development') {
        console.log('✅ Socket auth successful for user:', user.name);
      }

      socket.user = user;
      next();
    } catch (error) {
      console.log('❌ Socket auth failed:', error.message);
      next(new Error('Authentication error: Invalid token'));
    }
  });

  // Handle new connections
  // Store the io instance for global access
  setSocketInstance(io);

  io.on('connection', (socket) => {
    const userId = socket.user._id.toString();
    onlineUsers.set(userId, socket.id);
    io.emit('onlineUsers', Array.from(onlineUsers.keys()));

    // Join a room for the user
    socket.join(userId);

    // Handle joining a conversation
    socket.on('joinConversation', ({ conversationId }) => {
      if (conversationId) {
        socket.join(conversationId);
      }
    });

    // Handle leaving a conversation
    socket.on('leaveConversation', ({ conversationId }) => {
      if (conversationId) {
        socket.leave(conversationId);
      }
    });

    // Generic room joining logic
    socket.on('join', ({ room }) => {
      if (room) {
        socket.join(room);
      }
    });

    // --- Task Room Events ---
    socket.on('joinTaskRoom', ({ taskId }) => {
      if (taskId) {
        socket.join(`task-${taskId}`);
      }
    });

    socket.on('leaveTaskRoom', ({ taskId }) => {
      if (taskId) {
        socket.leave(`task-${taskId}`);
      }
    });

    // Handle typing events
    socket.on('typing', ({ conversationId, isTyping }) => {
      socket.to(conversationId).emit('typing', {
        userId: socket.user._id,
        isTyping,
      });
    });

    // Handle sending messages via socket
    socket.on('sendMessage', async ({ conversationId, text, attachments = [], taskReference = null, tempId = null }) => {
      try {
        // Check if conversation exists and user is a participant
        const conversation = await Conversation.findOne({
          _id: conversationId,
          participants: socket.user._id
        });

        if (!conversation) {
          socket.emit('messageError', { error: 'Conversation not found or you are not a participant' });
          return;
        }

        if (!text && (!attachments || attachments.length === 0)) {
          socket.emit('messageError', { error: 'Message must contain text or attachments' });
          return;
        }

        // Create new message
        const newMessage = new Message({
          conversationId,
          sender: socket.user._id,
          text,
          attachments,
          taskReference,
          readBy: [{ user: socket.user._id }]
        });

        await newMessage.save();

        // Update conversation's last message
        conversation.lastMessage = newMessage._id;
        await conversation.save();

        // Populate sender info
        await newMessage.populate({
          path: 'sender',
          select: 'name profileImageUrl'
        });

        // Include the tempId in the response if it was provided
        const messageResponse = newMessage.toObject();
        if (tempId) {
          messageResponse.tempId = tempId;
        }

        // Populate conversation info for the response
        await conversation.populate({
          path: 'participants',
          select: 'name profileImageUrl'
        });

        // Create notifications for other participants
        const otherParticipants = conversation.participants.filter(
          participant => participant._id.toString() !== socket.user._id.toString()
        );

        // Extract just the IDs for notification creation
        const otherParticipantIds = otherParticipants.map(p => p._id);

        if (otherParticipantIds.length > 0) {
          try {
            const notifications = await Notification.createChatMessageNotification(
              newMessage._id,
              conversationId,
              socket.user._id,
              otherParticipantIds
            );

            // Emit notification events to online users
            if (notifications && notifications.length > 0) {
              // Populate the triggeredBy field for real-time notifications
              await Promise.all(notifications.map(notification =>
                notification.populate('triggeredBy', 'name profileImageUrl')
              ));

              notifications.forEach(notification => {
                const recipientId = notification.recipient.toString();
                const recipientSocketId = onlineUsers.get(recipientId);

                if (recipientSocketId) {
                  io.to(recipientSocketId).emit('newNotification', notification);
                }
              });
            }
          } catch (notificationError) {
            console.error('❌ Error creating chat message notifications:', notificationError);
          }
        }

        // Broadcast the message to all participants in the conversation
        io.to(conversationId).emit('newMessage', {
          message: messageResponse,
          conversation: conversation.toObject()
        });

      } catch (error) {
        // Log error only in development
        if (process.env.NODE_ENV === 'development') {
          console.error('Socket sendMessage error:', error);
        }
        socket.emit('messageError', { error: 'Failed to send message' });
      }
    });

    // Handle message read events
    socket.on('messageRead', ({ messageId, conversationId }) => {
      socket.to(conversationId).emit('messageRead', {
        messageId,
        userId: socket.user._id
      });
    });

    // Handle message delivered events
    socket.on('messageDelivered', ({ messageId, conversationId }) => {
      socket.to(conversationId).emit('messageDelivered', {
        messageId,
        userId: socket.user._id
      });
    });

    // Handle notification read events
    socket.on('notificationRead', ({ notificationId, notificationIds }) => {
      // Emit to all connected clients that notification(s) were read
      if (notificationIds && Array.isArray(notificationIds)) {
        // Batch update
        socket.broadcast.emit('notificationRead', { notificationIds });
      } else if (notificationId) {
        // Single update
        socket.broadcast.emit('notificationRead', { notificationId });
      }
    });

    // Handle disconnection
    socket.on('disconnect', () => {
      onlineUsers.delete(userId);
      io.emit('onlineUsers', Array.from(onlineUsers.keys()));
    });

    // Task events
    socket.on('taskAssigned', (data) => {
      io.to(data.userId).emit('taskAssigned', data.task);
    });

    socket.on('taskDeleted', (data) => {
      io.to(data.userId).emit('taskDeleted', data.taskId);
    });
  });

  return io;
};

module.exports = {
  initializeSocket,
  getOnlineUsers: () => onlineUsers
};
