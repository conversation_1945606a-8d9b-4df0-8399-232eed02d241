import React from "react";

const Progress = ({ progress, status }) => {
  const getColor = () => {
    switch (status) {
      case "In Progress":
        return "bg-blue-500";
      case "Completed":
        return "bg-green-500";
      default:
        return "bg-violet-500";
    }
  };

  return (
    <div className="w-full">
      <div className="bg-gray-200 rounded-full h-2">
        <div 
          className={`${getColor()} h-2 rounded-full`} 
          style={{ width: `${progress}%` }}
        ></div>
      </div>
    </div>
  );
};

export default Progress;
