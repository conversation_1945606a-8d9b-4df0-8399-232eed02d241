const mongoose = require('mongoose');

const taskSchema = new mongoose.Schema({
  title: {
    type: String,
    required: true,
  },
  description: {
    type: String,
    required: false,
  },
  subTasks: {
    type: [{
      title: { type: String, required: true },
      isCompleted: { type: Boolean, default: false }
    }],
    default: []
  },
  status: {
    type: String,
    enum: ['todo', 'in-progress', 'completed'],
    default: 'todo',
  },
  priority: {
    type: String,
    enum: ['low', 'medium', 'high'],
    default: 'medium',
  },
  // Store in DB as `deadline`, allow frontend to use `dueDate`
  deadline: {
    type: Date,
    alias: 'dueDate',
  },
  team: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
  }],
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  // --- Attachments ---
  attachments: {
    type: [{
      name: { type: String, required: true },
      url: { type: String, required: true },
      originalName: { type: String },
      size: { type: Number },
      mimeType: { type: String, default: 'application/octet-stream' }
    }],
    default: []
  },

  comments: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Comment',
  }],
}, {
  timestamps: true,
});

// Add indexes for frequently queried fields
taskSchema.index({ status: 1 });
taskSchema.index({ team: 1 });

const Task = mongoose.model('Task', taskSchema);

module.exports = Task;
