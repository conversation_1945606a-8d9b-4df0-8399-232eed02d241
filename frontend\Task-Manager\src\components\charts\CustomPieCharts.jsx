import React from "react";
import {
  <PERSON><PERSON><PERSON>,
  Pie,
  Cell,
  Tooltip,
  ResponsiveContainer,
} from "recharts";
import CustomTooltip from "./CustomTooltip";

/**
 * Custom pie chart component using Recharts
 * 
 * @param {Object} props - Component props
 * @param {Array} props.data - Chart data array
 * @param {Array} props.colors - Array of colors for chart segments
 * @returns {JSX.Element} Rendered component
 */
const CustomPieChart = ({ data, colors }) => {
  return (
    <ResponsiveContainer width="100%" height="100%">
      <PieChart margin={{ top: 0, right: 0, bottom: 0, left: 0 }}>
        <Pie
          data={data}
          dataKey="count"
          nameKey="status"
          cx="50%"
          cy="50%"
          outerRadius={80}
          innerRadius={60}
          labelLine={false}
        >
          {data.map((entry, index) => (
            <Cell key={`cell-${index}`} fill={colors[index % colors.length]} />
          ))}
        </Pie>
        <Tooltip content={<CustomTooltip />} />
      </PieChart>
    </ResponsiveContainer>
  );
};

export default CustomPieChart;
