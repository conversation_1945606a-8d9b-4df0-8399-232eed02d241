/**
 * Task Utilities
 * Centralized utilities for task-related operations
 * Eliminates duplicate task logic across components
 */

/**
 * Task status constants
 */
export const TASK_STATUSES = {
  PENDING: 'Pending',
  IN_PROGRESS: 'In Progress',
  COMPLETED: 'Completed'
};

/**
 * Task priority constants
 */
export const TASK_PRIORITIES = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high'
};

/**
 * Get task status badge color
 * @param {String} status - Task status
 * @returns {String} CSS classes for status badge
 */
export const getStatusBadgeColor = (status) => {
  switch (status) {
    case TASK_STATUSES.PENDING:
      return 'bg-amber-100 text-amber-800 border-amber-200';
    case TASK_STATUSES.IN_PROGRESS:
      return 'bg-blue-100 text-blue-800 border-blue-200';
    case TASK_STATUSES.COMPLETED:
      return 'bg-green-100 text-green-800 border-green-200';
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200';
  }
};

/**
 * Get task priority badge color
 * @param {String} priority - Task priority
 * @returns {String} CSS classes for priority badge
 */
export const getPriorityBadgeColor = (priority) => {
  switch (priority) {
    case TASK_PRIORITIES.LOW:
      return 'bg-green-100 text-green-800 border-green-200';
    case TASK_PRIORITIES.MEDIUM:
      return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    case TASK_PRIORITIES.HIGH:
      return 'bg-red-100 text-red-800 border-red-200';
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200';
  }
};

/**
 * Calculate task progress based on checklist completion
 * @param {Array} todoCheckList - Array of todo items
 * @returns {Number} Progress percentage (0-100)
 */
export const calculateTaskProgress = (todoCheckList) => {
  if (!todoCheckList || todoCheckList.length === 0) {
    return 0;
  }
  
  const completedCount = todoCheckList.filter(item => item.completed || item.isCompleted).length;
  return Math.round((completedCount / todoCheckList.length) * 100);
};

/**
 * Get completed todo count
 * @param {Array} todoCheckList - Array of todo items
 * @returns {Number} Number of completed todos
 */
export const getCompletedTodoCount = (todoCheckList) => {
  if (!todoCheckList || todoCheckList.length === 0) {
    return 0;
  }
  
  return todoCheckList.filter(item => item.completed).length;
};

/**
 * Check if task is overdue
 * @param {String} dueDate - Task due date
 * @param {String} status - Task status
 * @returns {Boolean} True if task is overdue
 */
export const isTaskOverdue = (dueDate, status) => {
  if (!dueDate || status === TASK_STATUSES.COMPLETED) {
    return false;
  }
  
  return new Date(dueDate) < new Date();
};

/**
 * Get days until due date
 * @param {String} dueDate - Task due date
 * @returns {Number} Days until due (negative if overdue)
 */
export const getDaysUntilDue = (dueDate) => {
  if (!dueDate) return null;
  
  const today = new Date();
  const due = new Date(dueDate);
  const diffTime = due - today;
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  return diffDays;
};

/**
 * Format due date for display
 * @param {String} dueDate - Task due date
 * @returns {String} Formatted due date string
 */
export const formatDueDate = (dueDate) => {
  if (!dueDate) return 'No due date';
  
  const diffDays = getDaysUntilDue(dueDate);
  
  if (diffDays === 0) {
    return 'Due today';
  } else if (diffDays === 1) {
    return 'Due tomorrow';
  } else if (diffDays === -1) {
    return 'Due yesterday';
  } else if (diffDays > 0) {
    return `Due in ${diffDays} days`;
  } else {
    return `Overdue by ${Math.abs(diffDays)} days`;
  }
};

/**
 * Filter tasks by status
 * @param {Array} tasks - Array of tasks
 * @param {String} status - Status to filter by ('All' for no filter)
 * @returns {Array} Filtered tasks
 */
export const filterTasksByStatus = (tasks, status) => {
  if (!tasks || !Array.isArray(tasks)) return [];
  
  if (status === 'All') {
    return tasks;
  }
  
  return tasks.filter(task => task.status === status);
};

/**
 * Filter tasks by priority
 * @param {Array} tasks - Array of tasks
 * @param {String} priority - Priority to filter by ('All' for no filter)
 * @returns {Array} Filtered tasks
 */
export const filterTasksByPriority = (tasks, priority) => {
  if (!tasks || !Array.isArray(tasks)) return [];
  
  if (priority === 'All') {
    return tasks;
  }
  
  return tasks.filter(task => task.priority === priority);
};

/**
 * Sort tasks by various criteria
 * @param {Array} tasks - Array of tasks
 * @param {String} sortBy - Sort criteria (dueDate, priority, status, title, createdAt)
 * @param {String} sortOrder - Sort order (asc, desc)
 * @returns {Array} Sorted tasks
 */
export const sortTasks = (tasks, sortBy = 'createdAt', sortOrder = 'desc') => {
  if (!tasks || !Array.isArray(tasks)) return [];
  
  const sortedTasks = [...tasks].sort((a, b) => {
    let aValue, bValue;
    
    switch (sortBy) {
      case 'dueDate':
        aValue = a.dueDate ? new Date(a.dueDate) : new Date('9999-12-31');
        bValue = b.dueDate ? new Date(b.dueDate) : new Date('9999-12-31');
        break;
      case 'priority': {
        const priorityOrder = { 'high': 3, 'medium': 2, 'low': 1 };
        aValue = priorityOrder[a.priority] || 0;
        bValue = priorityOrder[b.priority] || 0;
        break;
      }
      case 'status': {
        const statusOrder = { 'Pending': 1, 'In Progress': 2, 'Completed': 3 };
        aValue = statusOrder[a.status] || 0;
        bValue = statusOrder[b.status] || 0;
        break;
      }
      case 'title':
        aValue = (a.title || '').toLowerCase();
        bValue = (b.title || '').toLowerCase();
        break;
      case 'createdAt':
      default:
        aValue = new Date(a.createdAt || 0);
        bValue = new Date(b.createdAt || 0);
        break;
    }
    
    if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1;
    if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1;
    return 0;
  });
  
  return sortedTasks;
};

/**
 * Get task statistics from array of tasks
 * @param {Array} tasks - Array of tasks
 * @returns {Object} Task statistics
 */
export const getTaskStatistics = (tasks) => {
  if (!tasks || !Array.isArray(tasks)) {
    return {
      total: 0,
      pending: 0,
      inProgress: 0,
      completed: 0,
      overdue: 0
    };
  }
  
  const stats = {
    total: tasks.length,
    pending: 0,
    inProgress: 0,
    completed: 0,
    overdue: 0
  };
  
  tasks.forEach(task => {
    switch (task.status) {
      case TASK_STATUSES.PENDING:
        stats.pending++;
        break;
      case TASK_STATUSES.IN_PROGRESS:
        stats.inProgress++;
        break;
      case TASK_STATUSES.COMPLETED:
        stats.completed++;
        break;
    }
    
    if (isTaskOverdue(task.dueDate, task.status)) {
      stats.overdue++;
    }
  });
  
  return stats;
};

/**
 * Create status tabs with counts
 * @param {Array} tasks - Array of tasks
 * @returns {Array} Status tabs with counts
 */
export const createStatusTabs = (tasks) => {
  const stats = getTaskStatistics(tasks);

  return [
    { label: 'All', count: stats.total },
    { label: TASK_STATUSES.PENDING, count: stats.pending },
    { label: TASK_STATUSES.IN_PROGRESS, count: stats.inProgress },
    { label: TASK_STATUSES.COMPLETED, count: stats.completed }
  ];
};

/**
 * Create dummy status tabs with zero counts (for error states)
 * @returns {Array} Status tabs with zero counts
 */
export const createDummyTabs = () => {
  return [
    { label: 'All', count: 0 },
    { label: TASK_STATUSES.PENDING, count: 0 },
    { label: TASK_STATUSES.IN_PROGRESS, count: 0 },
    { label: TASK_STATUSES.COMPLETED, count: 0 }
  ];
};

/**
 * Normalise task status coming from backend (string or numeric) to canonical string form.
 * @param {string|number} status
 * @returns {string}
 */
export const normalizeStatus = (status) => {
  if (status === null || status === undefined) return 'Unknown';
  if (typeof status === 'number') {
    switch (status) {
      case 1:
        return TASK_STATUSES.PENDING;
      case 2:
        return TASK_STATUSES.IN_PROGRESS;
      case 3:
        return TASK_STATUSES.COMPLETED;
      default:
        return 'Unknown';
    }
  }
  if (typeof status === 'string') {
    const lower = status.toLowerCase();
    if (lower === 'pending' || lower === 'todo') return TASK_STATUSES.PENDING;
    if (lower.includes('progress')) return TASK_STATUSES.IN_PROGRESS;
    if (lower === 'completed' || lower === 'done') return TASK_STATUSES.COMPLETED;
  }
  return status || 'Unknown';
};

/**
 * Convert raw task object returned by the API into a frontend-friendly structure.
 * This consolidates normalisation logic so components can stay lean.
 * @param {object} task Raw task from backend
 * @returns {object} Normalised task
 */
export const mapTaskFromApi = (task = {}) => {
  
  const taskStatus = normalizeStatus(task.status);
  const teamData = task.assignedTo || task.team || [];
  
  const assignedUsers = Array.isArray(teamData)
    ? teamData.map((member) => {
        if (typeof member === 'string') {
          return { _id: member, fullName: 'User ' + member.slice(-4), name: 'User ' + member.slice(-4) };
        }
        if (typeof member === 'object' && member !== null) {
          const user = member.user || member;
          return {
            _id: user._id || user.id,
            fullName: user.name || user.fullName || 'Unknown User',
            name: user.name || user.fullName || 'Unknown User',
            profileImageUrl: user.profileImageUrl || user.profileImage || user.avatar || null,
            email: user.email || '',
            role: user.role || 'Member',
          };
        }
        return null;
      }).filter(Boolean)
    : [];

  // --- Normalize todos from all possible sources ---
  let todos = [];
  
  // Try subTasks first (preferred)
  if (Array.isArray(task.subTasks) && task.subTasks.length > 0) {
    todos = task.subTasks;
  }
  // Try todoChecklist next
  else if (Array.isArray(task.todoChecklist) && task.todoChecklist.length > 0) {
    todos = task.todoChecklist;
  }
  // Finally try todoCheckList
  else if (Array.isArray(task.todoCheckList) && task.todoCheckList.length > 0) {
    todos = task.todoCheckList;
  }

  // Normalize each todo item
  const todoCheckList = todos.map(item => {
    // If item is a string, parse it or create a basic todo
    if (typeof item === 'string') {
      try {
        const parsed = JSON.parse(item);
        return {
          title: parsed.title || parsed.text || parsed.name || item,
          completed: Boolean(parsed.completed || parsed.isDone || parsed.isCompleted || parsed.status === 'completed')
        };
      } catch {
        return { title: item, completed: false };
      }
    }
    // If item is an object, normalize its fields
    return {
      title: item.title || item.text || item.name || '',
      completed: Boolean(item.completed || item.isDone || item.isCompleted || item.status === 'completed')
    };
  }).filter(todo => todo.title); // Remove any todos without text

  const completedTodoCount = todoCheckList.filter(i => i.completed || i.isCompleted).length;
  const totalTodos = todoCheckList.length;
  const progress = totalTodos > 0 ? Math.round((completedTodoCount / totalTodos) * 100) : 0;

  // Dynamically determine status based on sub-task completion
  let dynamicStatus = normalizeStatus(task.status);
  if (totalTodos > 0) {
    if (completedTodoCount === totalTodos) {
      // All todos done – regardless of previous status mark as Completed
      dynamicStatus = TASK_STATUSES.COMPLETED;
    } else if (completedTodoCount > 0) {
      // Some todos done → In Progress (don’t downgrade Completed)
      if (dynamicStatus !== TASK_STATUSES.COMPLETED) {
        dynamicStatus = TASK_STATUSES.IN_PROGRESS;
      }
    }
    // When no todos completed keep existing status (usually Pending or In Progress)
  }

  // Use a consistent source for attachments and ensure they are marked as existing
  const rawAttachments = task.attachments || task.attachmentsList || [];
  const parsedAttachments = rawAttachments.map(att => ({ ...att, isExisting: true }));

  // Normalize date fields
  const createdAt = task.createdAt || new Date().toISOString();
  const startDate = task.startDate || createdAt;
  const dueDate = task.dueDate || task.deadline || null;

  return {
    ...task,
    _id: task._id || task.id || Math.random().toString(36).substr(2, 9),
    title: task.title || 'Untitled Task',
    description: task.description || '',
    status: dynamicStatus,
    priority: (task.priority || 'medium').toLowerCase(),
    progress,
    todoCheckList,
    completedTodoCount,
    assignedTo: assignedUsers,
    createdAt,
    startDate,
    dueDate,
    attachments: parsedAttachments,
    attachmentsList: parsedAttachments,
  };
};

/**
 * Normalize task members from backend data
 * @param {Object} task - Task object from backend
 * @returns {Array} Normalized team members
 */
export const normalizeTaskMembers = (task) => {
  // Get team members from either assignedTo or team field
  const teamData = task.team || task.assignedTo || [];
  
  // Normalize each team member
  return teamData.map(member => ({
    _id: member._id || member.id,
    name: member.name || member.fullName || 'Unknown User',
    email: member.email || '',
    profileImageUrl: member.profileImageUrl || member.profileImage || null,
    role: member.role || 'Member'
  }));
};

/**
 * Normalize task data from backend API
 * @param {Object} task - Task object from backend
 * @returns {Object} Normalized task data
 */
export const normalizeTaskData = (task) => {
  if (!task) return null;

  const normalizedMembers = normalizeTaskMembers(task);
  
  // --- Normalize todos from all possible sources ---
  let todos = [];
  
  // Try subTasks first (preferred)
  if (Array.isArray(task.subTasks) && task.subTasks.length > 0) {
    todos = task.subTasks;
  }
  // Try todoChecklist next
  else if (Array.isArray(task.todoChecklist) && task.todoChecklist.length > 0) {
    todos = task.todoChecklist;
  }
  // Finally try todoCheckList
  else if (Array.isArray(task.todoCheckList) && task.todoCheckList.length > 0) {
    todos = task.todoCheckList;
  }

  // Normalize each todo item
  const todoCheckList = todos.map(item => {
    // If item is a string, parse it or create a basic todo
    if (typeof item === 'string') {
      try {
        const parsed = JSON.parse(item);
        return {
          title: parsed.title || parsed.text || parsed.name || item,
          completed: parsed.completed || parsed.isDone || parsed.isCompleted || parsed.status === 'completed' || false
        };
      } catch {
        return { title: item, completed: false };
      }
    }
    // If item is an object, normalize its fields
    return {
      title: item.title || item.text || item.name || '',
      completed: item.completed || item.isDone || item.isCompleted || item.status === 'completed' || false
    };
  }).filter(todo => todo.title); // Remove any todos without text

  const completedTodoCount = todoCheckList.filter(todo => todo.completed).length;
  const attachments = task.attachments || [];

  return {
    _id: task._id || task.id,
    title: task.title || 'Untitled Task',
    description: task.description || '',
    status: normalizeStatus(task.status),
    priority: (task.priority || 'medium').toLowerCase(),
    progress: task.progress || 0,
    dueDate: task.dueDate || null,
    todoCheckList,
    completedTodoCount,
    assignedTo: normalizedMembers,
    team: normalizedMembers,
    createdAt: task.createdAt || new Date().toISOString(),
    startDate: task.startDate || task.createdAt || new Date().toISOString(),
    attachments: attachments,
    attachmentCount: attachments.length
  };
};
