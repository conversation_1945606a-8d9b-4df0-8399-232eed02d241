import axiosInstance from '../utils/axiosInstance';

// Rate limiting protection
const requestCache = new Map();
const CACHE_DURATION = 5000; // 5 seconds

const getCacheKey = (method, url, data = null) => {
  return `${method}:${url}:${data ? JSON.stringify(data) : ''}`;
};

const isRequestCached = (cacheKey) => {
  const cached = requestCache.get(cacheKey);
  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
    return cached.promise;
  }
  return null;
};

const cacheRequest = (cacheKey, promise) => {
  requestCache.set(cacheKey, {
    promise,
    timestamp: Date.now()
  });

  // Clean up cache after duration
  setTimeout(() => {
    requestCache.delete(cacheKey);
  }, CACHE_DURATION);

  return promise;
};

const chatService = {
  // Get all conversations for the current user
  getConversations: async (forceRefresh = false) => {
    try {
      // Add cache-busting parameter if force refresh is requested
      const params = forceRefresh ? { _t: Date.now() } : {};
      const response = await axiosInstance.get('/api/chat/conversations', { params });

      if (!response.data || !response.data.success) {
        return { success: false, error: 'Invalid response from server' };
      }

      if (!response.data.conversations || !Array.isArray(response.data.conversations)) {
        return { success: true, conversations: [] };
      }

      return response.data;
    } catch (error) {
      return {
        success: false,
        error: error.response?.data?.message || error.message || 'Failed to fetch conversations'
      };
    }
  },

  // Get or create an individual conversation with another user
  getOrCreateIndividualConversation: async (userId) => {
    try {
      if (!userId) {
        return { success: false, error: 'No user ID provided' };
      }

      // Check cache to prevent duplicate requests
      const cacheKey = getCacheKey('POST', `/api/chat/conversations/individual/${userId}`);
      const cachedRequest = isRequestCached(cacheKey);

      if (cachedRequest) {

        return await cachedRequest;
      }

      // Create new request and cache it
      const requestPromise = (async () => {
        const response = await axiosInstance.post(`/api/chat/conversations/individual/${userId}`);

        if (!response.data || !response.data.success) {
          return { success: false, error: 'Failed to create conversation' };
        }

        return response.data;
      })();

      return await cacheRequest(cacheKey, requestPromise);
    } catch (error) {
      return {
        success: false,
        error: error.response?.data?.message || error.message || 'Failed to create conversation'
      };
    }
  },

  // Create a group conversation
  createGroupConversation: async (name, participantIds) => {
    try {
      if (!name || !participantIds || !participantIds.length) {
        return { success: false, error: 'Invalid group parameters' };
      }
      
      const response = await axiosInstance.post('/api/chat/conversations/group', {
        name,
        participantIds
      });
      
      if (!response.data || !response.data.success) {
        return { success: false, error: 'Failed to create group' };
      }
      
      return response.data;
    } catch (error) {
      return { 
        success: false, 
        error: error.response?.data?.message || error.message || 'Failed to create group' 
      };
    }
  },

  // Get messages for a specific conversation
  getMessages: async (conversationId, params = {}) => {
    try {
      if (!conversationId) {
        return { success: false, error: 'No conversation ID provided', messages: [] };
      }
      
      const response = await axiosInstance.get(`/api/chat/conversations/${conversationId}/messages`, {
        params
      });
      
      if (!response.data || !response.data.success) {
        return { success: false, error: 'Failed to fetch messages', messages: [] };
      }
      
      return response.data;
    } catch (error) {
      return { 
        success: false, 
        error: error.response?.data?.message || error.message || 'Failed to fetch messages',
        messages: []
      };
    }
  },

  // Send a message (HTTP fallback if socket is not available)
  sendMessage: async (conversationId, messageData) => {
    try {
      if (!conversationId) {
        return { success: false, error: 'No conversation ID provided' };
      }
      
      if (!messageData || (!messageData.text && (!messageData.attachments || !messageData.attachments.length))) {
        return { success: false, error: 'Invalid message data' };
      }
      
      const response = await axiosInstance.post(`/api/chat/conversations/${conversationId}/messages`, messageData);
      
      if (!response.data || !response.data.success) {
        return { success: false, error: 'Failed to send message' };
      }
      
      return response.data;
    } catch (error) {
      return {
        success: false,
        error: error.response?.data?.message || error.message || 'Failed to send message'
      };
    }
  },

  // Clear/Delete all messages in a conversation
  clearConversation: async (conversationId) => {
    try {
      if (!conversationId) {
        return { success: false, error: 'No conversation ID provided' };
      }

      const response = await axiosInstance.delete(`/api/chat/conversations/${conversationId}/clear`);

      if (!response.data || !response.data.success) {
        return { success: false, error: 'Failed to clear conversation' };
      }

      return response.data;
    } catch (error) {
      return {
        success: false,
        error: error.response?.data?.message || error.message || 'Failed to clear conversation'
      };
    }
  }
};

export default chatService; 