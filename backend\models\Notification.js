const mongoose = require('mongoose');

const notificationSchema = new mongoose.Schema(
  {
    recipient: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    type: {
      type: String,
      enum: ['task_assigned', 'task_status_updated', 'task_updated', 'chat_message', 'system', 'task_deleted'],
      required: true
    },
    title: {
      type: String,
      required: true
    },
    message: {
      type: String,
      required: true
    },
    data: {
      type: mongoose.Schema.Types.Mixed, // Additional data specific to notification type
      default: {}
    },
    isRead: {
      type: Boolean,
      default: false
    },
    readAt: {
      type: Date,
      default: null
    },
    // Reference to the related entity (task, message, etc.)
    relatedEntity: {
      entityType: {
        type: String,
        enum: ['Task', 'Message', 'User'],
        default: null
      },
      entityId: {
        type: mongoose.Schema.Types.ObjectId,
        default: null
      }
    },
    // For task notifications
    taskId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Task',
      default: null
    },
    // For chat notifications
    messageId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Message',
      default: null
    },
    conversationId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Conversation',
      default: null
    },
    // Who triggered this notification
    triggeredBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      default: null
    }
  },
  {
    timestamps: true
  }
);

// Index for efficient queries
notificationSchema.index({ recipient: 1, isRead: 1, createdAt: -1 });
notificationSchema.index({ recipient: 1, type: 1 });

// Compound index to prevent duplicate chat message notifications
notificationSchema.index({
  messageId: 1,
  recipient: 1,
  type: 1
}, {
  unique: true,
  sparse: true,
  partialFilterExpression: {
    messageId: { $exists: true },
    type: 'chat_message'
  }
});

// Static method to create task assignment notification
notificationSchema.statics.createTaskAssignmentNotification = async function(taskId, assignedUsers, assignedBy) {
  const Task = require('./Task');
  const User = require('./User');
  
  try {
    const task = await Task.findById(taskId).populate('createdBy', 'name');
    if (!task) return;

    const assignedByUser = await User.findById(assignedBy).select('name');
    const assignedByName = assignedByUser ? assignedByUser.name : 'Someone';

    const notifications = assignedUsers.map(userId => ({
      recipient: userId,
      type: 'task_assigned',
      title: 'New Task Assigned',
      message: `${assignedByName} assigned you a new task: "${task.title}"`,
      data: {
        taskTitle: task.title,
        taskPriority: task.priority,
        taskDueDate: task.dueDate,
        assignedByName
      },
      relatedEntity: {
        entityType: 'Task',
        entityId: taskId
      },
      taskId: taskId,
      triggeredBy: assignedBy
    }));

    return await this.insertMany(notifications);
  } catch (error) {
    console.error('Error creating task assignment notifications:', error);
    return [];
  }
};

// Static method to create chat message notification
notificationSchema.statics.createChatMessageNotification = async function(messageId, conversationId, senderId, recipientIds) {
  const Message = require('./Message');
  const User = require('./User');
  const Conversation = require('./Conversation');

  // console.log('🔔 Creating chat message notification:', {
  //   messageId,
  //   conversationId,
  //   senderId,
  //   recipientIds: Array.isArray(recipientIds) ? recipientIds.map(id => id.toString()) : recipientIds
  // });

  try {


    const message = await Message.findById(messageId).populate('sender', 'name');
    const conversation = await Conversation.findById(conversationId);

    // console.log('🔔 Found message and conversation:', {
    //   messageFound: !!message,
    //   conversationFound: !!conversation,
    //   senderName: message?.sender?.name
    // });

    if (!message || !conversation) {
      // console.log('❌ Message or conversation not found');
      return [];
    }

    const senderName = message.sender.name || 'Someone';
    const conversationName = conversation.type === 'group'
      ? conversation.name
      : `${senderName}`;

    const notifications = recipientIds.map(userId => ({
      recipient: userId,
      type: 'chat_message',
      title: 'New Message',
      message: `${senderName} sent you a message`,
      data: {
        senderName,
        conversationName,
        messageText: message.text,
        conversationType: conversation.type,
        conversationId: conversationId,
      },
      relatedEntity: {
        entityType: 'Message',
        entityId: messageId
      },
      messageId: messageId,
      conversationId: conversationId,
      triggeredBy: senderId
    }));

    // console.log('🔔 About to insert notifications:', notifications.length);
    const result = await this.insertMany(notifications);
    // console.log('✅ Notifications inserted successfully:', result.length);

    return result;
  } catch (error) {
    console.error('❌ Error creating chat message notifications:', error);
    return [];
  }
};

// Instance method to mark as read
notificationSchema.methods.markAsRead = function() {
  this.isRead = true;
  this.readAt = new Date();
  return this.save();
};

module.exports = mongoose.model('Notification', notificationSchema);
